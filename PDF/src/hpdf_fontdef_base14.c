/*
 * << Haru Free PDF Library >> -- hpdf_fontdef_base14.c
 *
 * URL: http://libharu.org
 *
 * Copyright (c) 1999-2006 <PERSON><PERSON> <<EMAIL>>
 * Copyright (c) 2007-2009 <PERSON> <<EMAIL>>
 *
 * Permission to use, copy, modify, distribute and sell this software
 * and its documentation for any purpose is hereby granted without fee,
 * provided that the above copyright notice appear in all copies and
 * that both that copyright notice and this permission notice appear
 * in supporting documentation.
 * It is provided "as is" without express or implied warranty.
 *
 */

#include "hpdf_conf.h"
#include "hpdf_utils.h"
#include "hpdf_fontdef.h"

static const HPDF_CharData CHAR_DATA_COURIER[316] = {
    {32, 0x0020, 600},
    {33, 0x0021, 600},
    {34, 0x0022, 600},
    {35, 0x0023, 600},
    {36, 0x0024, 600},
    {37, 0x0025, 600},
    {38, 0x0026, 600},
    {39, 0x2019, 600},
    {40, 0x0028, 600},
    {41, 0x0029, 600},
    {42, 0x002A, 600},
    {43, 0x002B, 600},
    {44, 0x002C, 600},
    {45, 0x002D, 600},
    {46, 0x002E, 600},
    {47, 0x002F, 600},
    {48, 0x0030, 600},
    {49, 0x0031, 600},
    {50, 0x0032, 600},
    {51, 0x0033, 600},
    {52, 0x0034, 600},
    {53, 0x0035, 600},
    {54, 0x0036, 600},
    {55, 0x0037, 600},
    {56, 0x0038, 600},
    {57, 0x0039, 600},
    {58, 0x003A, 600},
    {59, 0x003B, 600},
    {60, 0x003C, 600},
    {61, 0x003D, 600},
    {62, 0x003E, 600},
    {63, 0x003F, 600},
    {64, 0x0040, 600},
    {65, 0x0041, 600},
    {66, 0x0042, 600},
    {67, 0x0043, 600},
    {68, 0x0044, 600},
    {69, 0x0045, 600},
    {70, 0x0046, 600},
    {71, 0x0047, 600},
    {72, 0x0048, 600},
    {73, 0x0049, 600},
    {74, 0x004A, 600},
    {75, 0x004B, 600},
    {76, 0x004C, 600},
    {77, 0x004D, 600},
    {78, 0x004E, 600},
    {79, 0x004F, 600},
    {80, 0x0050, 600},
    {81, 0x0051, 600},
    {82, 0x0052, 600},
    {83, 0x0053, 600},
    {84, 0x0054, 600},
    {85, 0x0055, 600},
    {86, 0x0056, 600},
    {87, 0x0057, 600},
    {88, 0x0058, 600},
    {89, 0x0059, 600},
    {90, 0x005A, 600},
    {91, 0x005B, 600},
    {92, 0x005C, 600},
    {93, 0x005D, 600},
    {94, 0x005E, 600},
    {95, 0x005F, 600},
    {96, 0x2018, 600},
    {97, 0x0061, 600},
    {98, 0x0062, 600},
    {99, 0x0063, 600},
    {100, 0x0064, 600},
    {101, 0x0065, 600},
    {102, 0x0066, 600},
    {103, 0x0067, 600},
    {104, 0x0068, 600},
    {105, 0x0069, 600},
    {106, 0x006A, 600},
    {107, 0x006B, 600},
    {108, 0x006C, 600},
    {109, 0x006D, 600},
    {110, 0x006E, 600},
    {111, 0x006F, 600},
    {112, 0x0070, 600},
    {113, 0x0071, 600},
    {114, 0x0072, 600},
    {115, 0x0073, 600},
    {116, 0x0074, 600},
    {117, 0x0075, 600},
    {118, 0x0076, 600},
    {119, 0x0077, 600},
    {120, 0x0078, 600},
    {121, 0x0079, 600},
    {122, 0x007A, 600},
    {123, 0x007B, 600},
    {124, 0x007C, 600},
    {125, 0x007D, 600},
    {126, 0x007E, 600},
    {161, 0x00A1, 600},
    {162, 0x00A2, 600},
    {163, 0x00A3, 600},
    {164, 0x2044, 600},
    {165, 0x00A5, 600},
    {166, 0x0192, 600},
    {167, 0x00A7, 600},
    {168, 0x00A4, 600},
    {169, 0x0027, 600},
    {170, 0x201C, 600},
    {171, 0x00AB, 600},
    {172, 0x2039, 600},
    {173, 0x203A, 600},
    {174, 0xFB01, 600},
    {175, 0xFB02, 600},
    {177, 0x2013, 600},
    {178, 0x2020, 600},
    {179, 0x2021, 600},
    {180, 0x00B7, 600},
    {182, 0x00B6, 600},
    {183, 0x2022, 600},
    {184, 0x201A, 600},
    {185, 0x201E, 600},
    {186, 0x201D, 600},
    {187, 0x00BB, 600},
    {188, 0x2026, 600},
    {189, 0x2030, 600},
    {191, 0x00BF, 600},
    {193, 0x0060, 600},
    {194, 0x00B4, 600},
    {195, 0x02C6, 600},
    {196, 0x02DC, 600},
    {197, 0x00AF, 600},
    {198, 0x02D8, 600},
    {199, 0x02D9, 600},
    {200, 0x00A8, 600},
    {202, 0x02DA, 600},
    {203, 0x00B8, 600},
    {205, 0x02DD, 600},
    {206, 0x02DB, 600},
    {207, 0x02C7, 600},
    {208, 0x2014, 600},
    {225, 0x00C6, 600},
    {227, 0x00AA, 600},
    {232, 0x0141, 600},
    {233, 0x00D8, 600},
    {234, 0x0152, 600},
    {235, 0x00BA, 600},
    {241, 0x00E6, 600},
    {245, 0x0131, 600},
    {248, 0x0142, 600},
    {249, 0x00F8, 600},
    {250, 0x0153, 600},
    {251, 0x00DF, 600},
    {-1, 0x00CF, 600},
    {-1, 0x00E9, 600},
    {-1, 0x0103, 600},
    {-1, 0x0171, 600},
    {-1, 0x011B, 600},
    {-1, 0x0178, 600},
    {-1, 0x00F7, 600},
    {-1, 0x00DD, 600},
    {-1, 0x00C2, 600},
    {-1, 0x00E1, 600},
    {-1, 0x00DB, 600},
    {-1, 0x00FD, 600},
    {-1, 0x0219, 600},
    {-1, 0x00EA, 600},
    {-1, 0x016E, 600},
    {-1, 0x00DC, 600},
    {-1, 0x0105, 600},
    {-1, 0x00DA, 600},
    {-1, 0x0173, 600},
    {-1, 0x00CB, 600},
    {-1, 0x0110, 600},
    {-1, 0xF6C3, 600},
    {-1, 0x00A9, 600},
    {-1, 0x0112, 600},
    {-1, 0x010D, 600},
    {-1, 0x00E5, 600},
    {-1, 0x0145, 600},
    {-1, 0x013A, 600},
    {-1, 0x00E0, 600},
    {-1, 0x0162, 600},
    {-1, 0x0106, 600},
    {-1, 0x00E3, 600},
    {-1, 0x0116, 600},
    {-1, 0x0161, 600},
    {-1, 0x015F, 600},
    {-1, 0x00ED, 600},
    {-1, 0x25CA, 600},
    {-1, 0x0158, 600},
    {-1, 0x0122, 600},
    {-1, 0x00FB, 600},
    {-1, 0x00E2, 600},
    {-1, 0x0100, 600},
    {-1, 0x0159, 600},
    {-1, 0x00E7, 600},
    {-1, 0x017B, 600},
    {-1, 0x00DE, 600},
    {-1, 0x014C, 600},
    {-1, 0x0154, 600},
    {-1, 0x015A, 600},
    {-1, 0x010F, 600},
    {-1, 0x016A, 600},
    {-1, 0x016F, 600},
    {-1, 0x00B3, 600},
    {-1, 0x00D2, 600},
    {-1, 0x00C0, 600},
    {-1, 0x0102, 600},
    {-1, 0x00D7, 600},
    {-1, 0x00FA, 600},
    {-1, 0x0164, 600},
    {-1, 0x2202, 600},
    {-1, 0x00FF, 600},
    {-1, 0x0143, 600},
    {-1, 0x00EE, 600},
    {-1, 0x00CA, 600},
    {-1, 0x00E4, 600},
    {-1, 0x00EB, 600},
    {-1, 0x0107, 600},
    {-1, 0x0144, 600},
    {-1, 0x016B, 600},
    {-1, 0x0147, 600},
    {-1, 0x00CD, 600},
    {-1, 0x00B1, 600},
    {-1, 0x00A6, 600},
    {-1, 0x00AE, 600},
    {-1, 0x011E, 600},
    {-1, 0x0130, 600},
    {-1, 0x2211, 600},
    {-1, 0x00C8, 600},
    {-1, 0x0155, 600},
    {-1, 0x014D, 600},
    {-1, 0x0179, 600},
    {-1, 0x017D, 600},
    {-1, 0x2265, 600},
    {-1, 0x00D0, 600},
    {-1, 0x00C7, 600},
    {-1, 0x013C, 600},
    {-1, 0x0165, 600},
    {-1, 0x0119, 600},
    {-1, 0x0172, 600},
    {-1, 0x00C1, 600},
    {-1, 0x00C4, 600},
    {-1, 0x00E8, 600},
    {-1, 0x017A, 600},
    {-1, 0x012F, 600},
    {-1, 0x00D3, 600},
    {-1, 0x00F3, 600},
    {-1, 0x0101, 600},
    {-1, 0x015B, 600},
    {-1, 0x00EF, 600},
    {-1, 0x00D4, 600},
    {-1, 0x00D9, 600},
    {-1, 0x0394, 600},
    {-1, 0x00FE, 600},
    {-1, 0x00B2, 600},
    {-1, 0x00D6, 600},
    {-1, 0x00B5, 600},
    {-1, 0x00EC, 600},
    {-1, 0x0151, 600},
    {-1, 0x0118, 600},
    {-1, 0x0111, 600},
    {-1, 0x00BE, 600},
    {-1, 0x015E, 600},
    {-1, 0x013E, 600},
    {-1, 0x0136, 600},
    {-1, 0x0139, 600},
    {-1, 0x2122, 600},
    {-1, 0x0117, 600},
    {-1, 0x00CC, 600},
    {-1, 0x012A, 600},
    {-1, 0x013D, 600},
    {-1, 0x00BD, 600},
    {-1, 0x2264, 600},
    {-1, 0x00F4, 600},
    {-1, 0x00F1, 600},
    {-1, 0x0170, 600},
    {-1, 0x00C9, 600},
    {-1, 0x0113, 600},
    {-1, 0x011F, 600},
    {-1, 0x00BC, 600},
    {-1, 0x0160, 600},
    {-1, 0x0218, 600},
    {-1, 0x0150, 600},
    {-1, 0x00B0, 600},
    {-1, 0x00F2, 600},
    {-1, 0x010C, 600},
    {-1, 0x00F9, 600},
    {-1, 0x221A, 600},
    {-1, 0x010E, 600},
    {-1, 0x0157, 600},
    {-1, 0x00D1, 600},
    {-1, 0x00F5, 600},
    {-1, 0x0156, 600},
    {-1, 0x013B, 600},
    {-1, 0x00C3, 600},
    {-1, 0x0104, 600},
    {-1, 0x00C5, 600},
    {-1, 0x00D5, 600},
    {-1, 0x017C, 600},
    {-1, 0x011A, 600},
    {-1, 0x012E, 600},
    {-1, 0x0137, 600},
    {-1, 0x2212, 600},
    {-1, 0x00CE, 600},
    {-1, 0x0148, 600},
    {-1, 0x0163, 600},
    {-1, 0x00AC, 600},
    {-1, 0x00F6, 600},
    {-1, 0x00FC, 600},
    {-1, 0x2260, 600},
    {-1, 0x0123, 600},
    {-1, 0x00F0, 600},
    {-1, 0x017E, 600},
    {-1, 0x0146, 600},
    {-1, 0x00B9, 600},
    {-1, 0x012B, 600},
    {-1, 0x20AC, 600},
    {-1, 0xFFFF, 0}
    };

static const HPDF_CharData CHAR_DATA_COURIER_BOLD[316] = {
    {32, 0x0020, 600},
    {33, 0x0021, 600},
    {34, 0x0022, 600},
    {35, 0x0023, 600},
    {36, 0x0024, 600},
    {37, 0x0025, 600},
    {38, 0x0026, 600},
    {39, 0x2019, 600},
    {40, 0x0028, 600},
    {41, 0x0029, 600},
    {42, 0x002A, 600},
    {43, 0x002B, 600},
    {44, 0x002C, 600},
    {45, 0x002D, 600},
    {46, 0x002E, 600},
    {47, 0x002F, 600},
    {48, 0x0030, 600},
    {49, 0x0031, 600},
    {50, 0x0032, 600},
    {51, 0x0033, 600},
    {52, 0x0034, 600},
    {53, 0x0035, 600},
    {54, 0x0036, 600},
    {55, 0x0037, 600},
    {56, 0x0038, 600},
    {57, 0x0039, 600},
    {58, 0x003A, 600},
    {59, 0x003B, 600},
    {60, 0x003C, 600},
    {61, 0x003D, 600},
    {62, 0x003E, 600},
    {63, 0x003F, 600},
    {64, 0x0040, 600},
    {65, 0x0041, 600},
    {66, 0x0042, 600},
    {67, 0x0043, 600},
    {68, 0x0044, 600},
    {69, 0x0045, 600},
    {70, 0x0046, 600},
    {71, 0x0047, 600},
    {72, 0x0048, 600},
    {73, 0x0049, 600},
    {74, 0x004A, 600},
    {75, 0x004B, 600},
    {76, 0x004C, 600},
    {77, 0x004D, 600},
    {78, 0x004E, 600},
    {79, 0x004F, 600},
    {80, 0x0050, 600},
    {81, 0x0051, 600},
    {82, 0x0052, 600},
    {83, 0x0053, 600},
    {84, 0x0054, 600},
    {85, 0x0055, 600},
    {86, 0x0056, 600},
    {87, 0x0057, 600},
    {88, 0x0058, 600},
    {89, 0x0059, 600},
    {90, 0x005A, 600},
    {91, 0x005B, 600},
    {92, 0x005C, 600},
    {93, 0x005D, 600},
    {94, 0x005E, 600},
    {95, 0x005F, 600},
    {96, 0x2018, 600},
    {97, 0x0061, 600},
    {98, 0x0062, 600},
    {99, 0x0063, 600},
    {100, 0x0064, 600},
    {101, 0x0065, 600},
    {102, 0x0066, 600},
    {103, 0x0067, 600},
    {104, 0x0068, 600},
    {105, 0x0069, 600},
    {106, 0x006A, 600},
    {107, 0x006B, 600},
    {108, 0x006C, 600},
    {109, 0x006D, 600},
    {110, 0x006E, 600},
    {111, 0x006F, 600},
    {112, 0x0070, 600},
    {113, 0x0071, 600},
    {114, 0x0072, 600},
    {115, 0x0073, 600},
    {116, 0x0074, 600},
    {117, 0x0075, 600},
    {118, 0x0076, 600},
    {119, 0x0077, 600},
    {120, 0x0078, 600},
    {121, 0x0079, 600},
    {122, 0x007A, 600},
    {123, 0x007B, 600},
    {124, 0x007C, 600},
    {125, 0x007D, 600},
    {126, 0x007E, 600},
    {161, 0x00A1, 600},
    {162, 0x00A2, 600},
    {163, 0x00A3, 600},
    {164, 0x2044, 600},
    {165, 0x00A5, 600},
    {166, 0x0192, 600},
    {167, 0x00A7, 600},
    {168, 0x00A4, 600},
    {169, 0x0027, 600},
    {170, 0x201C, 600},
    {171, 0x00AB, 600},
    {172, 0x2039, 600},
    {173, 0x203A, 600},
    {174, 0xFB01, 600},
    {175, 0xFB02, 600},
    {177, 0x2013, 600},
    {178, 0x2020, 600},
    {179, 0x2021, 600},
    {180, 0x00B7, 600},
    {182, 0x00B6, 600},
    {183, 0x2022, 600},
    {184, 0x201A, 600},
    {185, 0x201E, 600},
    {186, 0x201D, 600},
    {187, 0x00BB, 600},
    {188, 0x2026, 600},
    {189, 0x2030, 600},
    {191, 0x00BF, 600},
    {193, 0x0060, 600},
    {194, 0x00B4, 600},
    {195, 0x02C6, 600},
    {196, 0x02DC, 600},
    {197, 0x00AF, 600},
    {198, 0x02D8, 600},
    {199, 0x02D9, 600},
    {200, 0x00A8, 600},
    {202, 0x02DA, 600},
    {203, 0x00B8, 600},
    {205, 0x02DD, 600},
    {206, 0x02DB, 600},
    {207, 0x02C7, 600},
    {208, 0x2014, 600},
    {225, 0x00C6, 600},
    {227, 0x00AA, 600},
    {232, 0x0141, 600},
    {233, 0x00D8, 600},
    {234, 0x0152, 600},
    {235, 0x00BA, 600},
    {241, 0x00E6, 600},
    {245, 0x0131, 600},
    {248, 0x0142, 600},
    {249, 0x00F8, 600},
    {250, 0x0153, 600},
    {251, 0x00DF, 600},
    {-1, 0x00CF, 600},
    {-1, 0x00E9, 600},
    {-1, 0x0103, 600},
    {-1, 0x0171, 600},
    {-1, 0x011B, 600},
    {-1, 0x0178, 600},
    {-1, 0x00F7, 600},
    {-1, 0x00DD, 600},
    {-1, 0x00C2, 600},
    {-1, 0x00E1, 600},
    {-1, 0x00DB, 600},
    {-1, 0x00FD, 600},
    {-1, 0x0219, 600},
    {-1, 0x00EA, 600},
    {-1, 0x016E, 600},
    {-1, 0x00DC, 600},
    {-1, 0x0105, 600},
    {-1, 0x00DA, 600},
    {-1, 0x0173, 600},
    {-1, 0x00CB, 600},
    {-1, 0x0110, 600},
    {-1, 0xF6C3, 600},
    {-1, 0x00A9, 600},
    {-1, 0x0112, 600},
    {-1, 0x010D, 600},
    {-1, 0x00E5, 600},
    {-1, 0x0145, 600},
    {-1, 0x013A, 600},
    {-1, 0x00E0, 600},
    {-1, 0x0162, 600},
    {-1, 0x0106, 600},
    {-1, 0x00E3, 600},
    {-1, 0x0116, 600},
    {-1, 0x0161, 600},
    {-1, 0x015F, 600},
    {-1, 0x00ED, 600},
    {-1, 0x25CA, 600},
    {-1, 0x0158, 600},
    {-1, 0x0122, 600},
    {-1, 0x00FB, 600},
    {-1, 0x00E2, 600},
    {-1, 0x0100, 600},
    {-1, 0x0159, 600},
    {-1, 0x00E7, 600},
    {-1, 0x017B, 600},
    {-1, 0x00DE, 600},
    {-1, 0x014C, 600},
    {-1, 0x0154, 600},
    {-1, 0x015A, 600},
    {-1, 0x010F, 600},
    {-1, 0x016A, 600},
    {-1, 0x016F, 600},
    {-1, 0x00B3, 600},
    {-1, 0x00D2, 600},
    {-1, 0x00C0, 600},
    {-1, 0x0102, 600},
    {-1, 0x00D7, 600},
    {-1, 0x00FA, 600},
    {-1, 0x0164, 600},
    {-1, 0x2202, 600},
    {-1, 0x00FF, 600},
    {-1, 0x0143, 600},
    {-1, 0x00EE, 600},
    {-1, 0x00CA, 600},
    {-1, 0x00E4, 600},
    {-1, 0x00EB, 600},
    {-1, 0x0107, 600},
    {-1, 0x0144, 600},
    {-1, 0x016B, 600},
    {-1, 0x0147, 600},
    {-1, 0x00CD, 600},
    {-1, 0x00B1, 600},
    {-1, 0x00A6, 600},
    {-1, 0x00AE, 600},
    {-1, 0x011E, 600},
    {-1, 0x0130, 600},
    {-1, 0x2211, 600},
    {-1, 0x00C8, 600},
    {-1, 0x0155, 600},
    {-1, 0x014D, 600},
    {-1, 0x0179, 600},
    {-1, 0x017D, 600},
    {-1, 0x2265, 600},
    {-1, 0x00D0, 600},
    {-1, 0x00C7, 600},
    {-1, 0x013C, 600},
    {-1, 0x0165, 600},
    {-1, 0x0119, 600},
    {-1, 0x0172, 600},
    {-1, 0x00C1, 600},
    {-1, 0x00C4, 600},
    {-1, 0x00E8, 600},
    {-1, 0x017A, 600},
    {-1, 0x012F, 600},
    {-1, 0x00D3, 600},
    {-1, 0x00F3, 600},
    {-1, 0x0101, 600},
    {-1, 0x015B, 600},
    {-1, 0x00EF, 600},
    {-1, 0x00D4, 600},
    {-1, 0x00D9, 600},
    {-1, 0x0394, 600},
    {-1, 0x00FE, 600},
    {-1, 0x00B2, 600},
    {-1, 0x00D6, 600},
    {-1, 0x00B5, 600},
    {-1, 0x00EC, 600},
    {-1, 0x0151, 600},
    {-1, 0x0118, 600},
    {-1, 0x0111, 600},
    {-1, 0x00BE, 600},
    {-1, 0x015E, 600},
    {-1, 0x013E, 600},
    {-1, 0x0136, 600},
    {-1, 0x0139, 600},
    {-1, 0x2122, 600},
    {-1, 0x0117, 600},
    {-1, 0x00CC, 600},
    {-1, 0x012A, 600},
    {-1, 0x013D, 600},
    {-1, 0x00BD, 600},
    {-1, 0x2264, 600},
    {-1, 0x00F4, 600},
    {-1, 0x00F1, 600},
    {-1, 0x0170, 600},
    {-1, 0x00C9, 600},
    {-1, 0x0113, 600},
    {-1, 0x011F, 600},
    {-1, 0x00BC, 600},
    {-1, 0x0160, 600},
    {-1, 0x0218, 600},
    {-1, 0x0150, 600},
    {-1, 0x00B0, 600},
    {-1, 0x00F2, 600},
    {-1, 0x010C, 600},
    {-1, 0x00F9, 600},
    {-1, 0x221A, 600},
    {-1, 0x010E, 600},
    {-1, 0x0157, 600},
    {-1, 0x00D1, 600},
    {-1, 0x00F5, 600},
    {-1, 0x0156, 600},
    {-1, 0x013B, 600},
    {-1, 0x00C3, 600},
    {-1, 0x0104, 600},
    {-1, 0x00C5, 600},
    {-1, 0x00D5, 600},
    {-1, 0x017C, 600},
    {-1, 0x011A, 600},
    {-1, 0x012E, 600},
    {-1, 0x0137, 600},
    {-1, 0x2212, 600},
    {-1, 0x00CE, 600},
    {-1, 0x0148, 600},
    {-1, 0x0163, 600},
    {-1, 0x00AC, 600},
    {-1, 0x00F6, 600},
    {-1, 0x00FC, 600},
    {-1, 0x2260, 600},
    {-1, 0x0123, 600},
    {-1, 0x00F0, 600},
    {-1, 0x017E, 600},
    {-1, 0x0146, 600},
    {-1, 0x00B9, 600},
    {-1, 0x012B, 600},
    {-1, 0x20AC, 600},
    {-1, 0xFFFF, 0}
    };

static const HPDF_CharData CHAR_DATA_COURIER_BOLD_OBLIQUE[316] = {
    {32, 0x0020, 600},
    {33, 0x0021, 600},
    {34, 0x0022, 600},
    {35, 0x0023, 600},
    {36, 0x0024, 600},
    {37, 0x0025, 600},
    {38, 0x0026, 600},
    {39, 0x2019, 600},
    {40, 0x0028, 600},
    {41, 0x0029, 600},
    {42, 0x002A, 600},
    {43, 0x002B, 600},
    {44, 0x002C, 600},
    {45, 0x002D, 600},
    {46, 0x002E, 600},
    {47, 0x002F, 600},
    {48, 0x0030, 600},
    {49, 0x0031, 600},
    {50, 0x0032, 600},
    {51, 0x0033, 600},
    {52, 0x0034, 600},
    {53, 0x0035, 600},
    {54, 0x0036, 600},
    {55, 0x0037, 600},
    {56, 0x0038, 600},
    {57, 0x0039, 600},
    {58, 0x003A, 600},
    {59, 0x003B, 600},
    {60, 0x003C, 600},
    {61, 0x003D, 600},
    {62, 0x003E, 600},
    {63, 0x003F, 600},
    {64, 0x0040, 600},
    {65, 0x0041, 600},
    {66, 0x0042, 600},
    {67, 0x0043, 600},
    {68, 0x0044, 600},
    {69, 0x0045, 600},
    {70, 0x0046, 600},
    {71, 0x0047, 600},
    {72, 0x0048, 600},
    {73, 0x0049, 600},
    {74, 0x004A, 600},
    {75, 0x004B, 600},
    {76, 0x004C, 600},
    {77, 0x004D, 600},
    {78, 0x004E, 600},
    {79, 0x004F, 600},
    {80, 0x0050, 600},
    {81, 0x0051, 600},
    {82, 0x0052, 600},
    {83, 0x0053, 600},
    {84, 0x0054, 600},
    {85, 0x0055, 600},
    {86, 0x0056, 600},
    {87, 0x0057, 600},
    {88, 0x0058, 600},
    {89, 0x0059, 600},
    {90, 0x005A, 600},
    {91, 0x005B, 600},
    {92, 0x005C, 600},
    {93, 0x005D, 600},
    {94, 0x005E, 600},
    {95, 0x005F, 600},
    {96, 0x2018, 600},
    {97, 0x0061, 600},
    {98, 0x0062, 600},
    {99, 0x0063, 600},
    {100, 0x0064, 600},
    {101, 0x0065, 600},
    {102, 0x0066, 600},
    {103, 0x0067, 600},
    {104, 0x0068, 600},
    {105, 0x0069, 600},
    {106, 0x006A, 600},
    {107, 0x006B, 600},
    {108, 0x006C, 600},
    {109, 0x006D, 600},
    {110, 0x006E, 600},
    {111, 0x006F, 600},
    {112, 0x0070, 600},
    {113, 0x0071, 600},
    {114, 0x0072, 600},
    {115, 0x0073, 600},
    {116, 0x0074, 600},
    {117, 0x0075, 600},
    {118, 0x0076, 600},
    {119, 0x0077, 600},
    {120, 0x0078, 600},
    {121, 0x0079, 600},
    {122, 0x007A, 600},
    {123, 0x007B, 600},
    {124, 0x007C, 600},
    {125, 0x007D, 600},
    {126, 0x007E, 600},
    {161, 0x00A1, 600},
    {162, 0x00A2, 600},
    {163, 0x00A3, 600},
    {164, 0x2044, 600},
    {165, 0x00A5, 600},
    {166, 0x0192, 600},
    {167, 0x00A7, 600},
    {168, 0x00A4, 600},
    {169, 0x0027, 600},
    {170, 0x201C, 600},
    {171, 0x00AB, 600},
    {172, 0x2039, 600},
    {173, 0x203A, 600},
    {174, 0xFB01, 600},
    {175, 0xFB02, 600},
    {177, 0x2013, 600},
    {178, 0x2020, 600},
    {179, 0x2021, 600},
    {180, 0x00B7, 600},
    {182, 0x00B6, 600},
    {183, 0x2022, 600},
    {184, 0x201A, 600},
    {185, 0x201E, 600},
    {186, 0x201D, 600},
    {187, 0x00BB, 600},
    {188, 0x2026, 600},
    {189, 0x2030, 600},
    {191, 0x00BF, 600},
    {193, 0x0060, 600},
    {194, 0x00B4, 600},
    {195, 0x02C6, 600},
    {196, 0x02DC, 600},
    {197, 0x00AF, 600},
    {198, 0x02D8, 600},
    {199, 0x02D9, 600},
    {200, 0x00A8, 600},
    {202, 0x02DA, 600},
    {203, 0x00B8, 600},
    {205, 0x02DD, 600},
    {206, 0x02DB, 600},
    {207, 0x02C7, 600},
    {208, 0x2014, 600},
    {225, 0x00C6, 600},
    {227, 0x00AA, 600},
    {232, 0x0141, 600},
    {233, 0x00D8, 600},
    {234, 0x0152, 600},
    {235, 0x00BA, 600},
    {241, 0x00E6, 600},
    {245, 0x0131, 600},
    {248, 0x0142, 600},
    {249, 0x00F8, 600},
    {250, 0x0153, 600},
    {251, 0x00DF, 600},
    {-1, 0x00CF, 600},
    {-1, 0x00E9, 600},
    {-1, 0x0103, 600},
    {-1, 0x0171, 600},
    {-1, 0x011B, 600},
    {-1, 0x0178, 600},
    {-1, 0x00F7, 600},
    {-1, 0x00DD, 600},
    {-1, 0x00C2, 600},
    {-1, 0x00E1, 600},
    {-1, 0x00DB, 600},
    {-1, 0x00FD, 600},
    {-1, 0x0219, 600},
    {-1, 0x00EA, 600},
    {-1, 0x016E, 600},
    {-1, 0x00DC, 600},
    {-1, 0x0105, 600},
    {-1, 0x00DA, 600},
    {-1, 0x0173, 600},
    {-1, 0x00CB, 600},
    {-1, 0x0110, 600},
    {-1, 0xF6C3, 600},
    {-1, 0x00A9, 600},
    {-1, 0x0112, 600},
    {-1, 0x010D, 600},
    {-1, 0x00E5, 600},
    {-1, 0x0145, 600},
    {-1, 0x013A, 600},
    {-1, 0x00E0, 600},
    {-1, 0x0162, 600},
    {-1, 0x0106, 600},
    {-1, 0x00E3, 600},
    {-1, 0x0116, 600},
    {-1, 0x0161, 600},
    {-1, 0x015F, 600},
    {-1, 0x00ED, 600},
    {-1, 0x25CA, 600},
    {-1, 0x0158, 600},
    {-1, 0x0122, 600},
    {-1, 0x00FB, 600},
    {-1, 0x00E2, 600},
    {-1, 0x0100, 600},
    {-1, 0x0159, 600},
    {-1, 0x00E7, 600},
    {-1, 0x017B, 600},
    {-1, 0x00DE, 600},
    {-1, 0x014C, 600},
    {-1, 0x0154, 600},
    {-1, 0x015A, 600},
    {-1, 0x010F, 600},
    {-1, 0x016A, 600},
    {-1, 0x016F, 600},
    {-1, 0x00B3, 600},
    {-1, 0x00D2, 600},
    {-1, 0x00C0, 600},
    {-1, 0x0102, 600},
    {-1, 0x00D7, 600},
    {-1, 0x00FA, 600},
    {-1, 0x0164, 600},
    {-1, 0x2202, 600},
    {-1, 0x00FF, 600},
    {-1, 0x0143, 600},
    {-1, 0x00EE, 600},
    {-1, 0x00CA, 600},
    {-1, 0x00E4, 600},
    {-1, 0x00EB, 600},
    {-1, 0x0107, 600},
    {-1, 0x0144, 600},
    {-1, 0x016B, 600},
    {-1, 0x0147, 600},
    {-1, 0x00CD, 600},
    {-1, 0x00B1, 600},
    {-1, 0x00A6, 600},
    {-1, 0x00AE, 600},
    {-1, 0x011E, 600},
    {-1, 0x0130, 600},
    {-1, 0x2211, 600},
    {-1, 0x00C8, 600},
    {-1, 0x0155, 600},
    {-1, 0x014D, 600},
    {-1, 0x0179, 600},
    {-1, 0x017D, 600},
    {-1, 0x2265, 600},
    {-1, 0x00D0, 600},
    {-1, 0x00C7, 600},
    {-1, 0x013C, 600},
    {-1, 0x0165, 600},
    {-1, 0x0119, 600},
    {-1, 0x0172, 600},
    {-1, 0x00C1, 600},
    {-1, 0x00C4, 600},
    {-1, 0x00E8, 600},
    {-1, 0x017A, 600},
    {-1, 0x012F, 600},
    {-1, 0x00D3, 600},
    {-1, 0x00F3, 600},
    {-1, 0x0101, 600},
    {-1, 0x015B, 600},
    {-1, 0x00EF, 600},
    {-1, 0x00D4, 600},
    {-1, 0x00D9, 600},
    {-1, 0x0394, 600},
    {-1, 0x00FE, 600},
    {-1, 0x00B2, 600},
    {-1, 0x00D6, 600},
    {-1, 0x00B5, 600},
    {-1, 0x00EC, 600},
    {-1, 0x0151, 600},
    {-1, 0x0118, 600},
    {-1, 0x0111, 600},
    {-1, 0x00BE, 600},
    {-1, 0x015E, 600},
    {-1, 0x013E, 600},
    {-1, 0x0136, 600},
    {-1, 0x0139, 600},
    {-1, 0x2122, 600},
    {-1, 0x0117, 600},
    {-1, 0x00CC, 600},
    {-1, 0x012A, 600},
    {-1, 0x013D, 600},
    {-1, 0x00BD, 600},
    {-1, 0x2264, 600},
    {-1, 0x00F4, 600},
    {-1, 0x00F1, 600},
    {-1, 0x0170, 600},
    {-1, 0x00C9, 600},
    {-1, 0x0113, 600},
    {-1, 0x011F, 600},
    {-1, 0x00BC, 600},
    {-1, 0x0160, 600},
    {-1, 0x0218, 600},
    {-1, 0x0150, 600},
    {-1, 0x00B0, 600},
    {-1, 0x00F2, 600},
    {-1, 0x010C, 600},
    {-1, 0x00F9, 600},
    {-1, 0x221A, 600},
    {-1, 0x010E, 600},
    {-1, 0x0157, 600},
    {-1, 0x00D1, 600},
    {-1, 0x00F5, 600},
    {-1, 0x0156, 600},
    {-1, 0x013B, 600},
    {-1, 0x00C3, 600},
    {-1, 0x0104, 600},
    {-1, 0x00C5, 600},
    {-1, 0x00D5, 600},
    {-1, 0x017C, 600},
    {-1, 0x011A, 600},
    {-1, 0x012E, 600},
    {-1, 0x0137, 600},
    {-1, 0x2212, 600},
    {-1, 0x00CE, 600},
    {-1, 0x0148, 600},
    {-1, 0x0163, 600},
    {-1, 0x00AC, 600},
    {-1, 0x00F6, 600},
    {-1, 0x00FC, 600},
    {-1, 0x2260, 600},
    {-1, 0x0123, 600},
    {-1, 0x00F0, 600},
    {-1, 0x017E, 600},
    {-1, 0x0146, 600},
    {-1, 0x00B9, 600},
    {-1, 0x012B, 600},
    {-1, 0x20AC, 600},
    {-1, 0xFFFF, 0}
    };

static const HPDF_CharData CHAR_DATA_COURIER_OBLIQUE[316] = {
    {32, 0x0020, 600},
    {33, 0x0021, 600},
    {34, 0x0022, 600},
    {35, 0x0023, 600},
    {36, 0x0024, 600},
    {37, 0x0025, 600},
    {38, 0x0026, 600},
    {39, 0x2019, 600},
    {40, 0x0028, 600},
    {41, 0x0029, 600},
    {42, 0x002A, 600},
    {43, 0x002B, 600},
    {44, 0x002C, 600},
    {45, 0x002D, 600},
    {46, 0x002E, 600},
    {47, 0x002F, 600},
    {48, 0x0030, 600},
    {49, 0x0031, 600},
    {50, 0x0032, 600},
    {51, 0x0033, 600},
    {52, 0x0034, 600},
    {53, 0x0035, 600},
    {54, 0x0036, 600},
    {55, 0x0037, 600},
    {56, 0x0038, 600},
    {57, 0x0039, 600},
    {58, 0x003A, 600},
    {59, 0x003B, 600},
    {60, 0x003C, 600},
    {61, 0x003D, 600},
    {62, 0x003E, 600},
    {63, 0x003F, 600},
    {64, 0x0040, 600},
    {65, 0x0041, 600},
    {66, 0x0042, 600},
    {67, 0x0043, 600},
    {68, 0x0044, 600},
    {69, 0x0045, 600},
    {70, 0x0046, 600},
    {71, 0x0047, 600},
    {72, 0x0048, 600},
    {73, 0x0049, 600},
    {74, 0x004A, 600},
    {75, 0x004B, 600},
    {76, 0x004C, 600},
    {77, 0x004D, 600},
    {78, 0x004E, 600},
    {79, 0x004F, 600},
    {80, 0x0050, 600},
    {81, 0x0051, 600},
    {82, 0x0052, 600},
    {83, 0x0053, 600},
    {84, 0x0054, 600},
    {85, 0x0055, 600},
    {86, 0x0056, 600},
    {87, 0x0057, 600},
    {88, 0x0058, 600},
    {89, 0x0059, 600},
    {90, 0x005A, 600},
    {91, 0x005B, 600},
    {92, 0x005C, 600},
    {93, 0x005D, 600},
    {94, 0x005E, 600},
    {95, 0x005F, 600},
    {96, 0x2018, 600},
    {97, 0x0061, 600},
    {98, 0x0062, 600},
    {99, 0x0063, 600},
    {100, 0x0064, 600},
    {101, 0x0065, 600},
    {102, 0x0066, 600},
    {103, 0x0067, 600},
    {104, 0x0068, 600},
    {105, 0x0069, 600},
    {106, 0x006A, 600},
    {107, 0x006B, 600},
    {108, 0x006C, 600},
    {109, 0x006D, 600},
    {110, 0x006E, 600},
    {111, 0x006F, 600},
    {112, 0x0070, 600},
    {113, 0x0071, 600},
    {114, 0x0072, 600},
    {115, 0x0073, 600},
    {116, 0x0074, 600},
    {117, 0x0075, 600},
    {118, 0x0076, 600},
    {119, 0x0077, 600},
    {120, 0x0078, 600},
    {121, 0x0079, 600},
    {122, 0x007A, 600},
    {123, 0x007B, 600},
    {124, 0x007C, 600},
    {125, 0x007D, 600},
    {126, 0x007E, 600},
    {161, 0x00A1, 600},
    {162, 0x00A2, 600},
    {163, 0x00A3, 600},
    {164, 0x2044, 600},
    {165, 0x00A5, 600},
    {166, 0x0192, 600},
    {167, 0x00A7, 600},
    {168, 0x00A4, 600},
    {169, 0x0027, 600},
    {170, 0x201C, 600},
    {171, 0x00AB, 600},
    {172, 0x2039, 600},
    {173, 0x203A, 600},
    {174, 0xFB01, 600},
    {175, 0xFB02, 600},
    {177, 0x2013, 600},
    {178, 0x2020, 600},
    {179, 0x2021, 600},
    {180, 0x00B7, 600},
    {182, 0x00B6, 600},
    {183, 0x2022, 600},
    {184, 0x201A, 600},
    {185, 0x201E, 600},
    {186, 0x201D, 600},
    {187, 0x00BB, 600},
    {188, 0x2026, 600},
    {189, 0x2030, 600},
    {191, 0x00BF, 600},
    {193, 0x0060, 600},
    {194, 0x00B4, 600},
    {195, 0x02C6, 600},
    {196, 0x02DC, 600},
    {197, 0x00AF, 600},
    {198, 0x02D8, 600},
    {199, 0x02D9, 600},
    {200, 0x00A8, 600},
    {202, 0x02DA, 600},
    {203, 0x00B8, 600},
    {205, 0x02DD, 600},
    {206, 0x02DB, 600},
    {207, 0x02C7, 600},
    {208, 0x2014, 600},
    {225, 0x00C6, 600},
    {227, 0x00AA, 600},
    {232, 0x0141, 600},
    {233, 0x00D8, 600},
    {234, 0x0152, 600},
    {235, 0x00BA, 600},
    {241, 0x00E6, 600},
    {245, 0x0131, 600},
    {248, 0x0142, 600},
    {249, 0x00F8, 600},
    {250, 0x0153, 600},
    {251, 0x00DF, 600},
    {-1, 0x00CF, 600},
    {-1, 0x00E9, 600},
    {-1, 0x0103, 600},
    {-1, 0x0171, 600},
    {-1, 0x011B, 600},
    {-1, 0x0178, 600},
    {-1, 0x00F7, 600},
    {-1, 0x00DD, 600},
    {-1, 0x00C2, 600},
    {-1, 0x00E1, 600},
    {-1, 0x00DB, 600},
    {-1, 0x00FD, 600},
    {-1, 0x0219, 600},
    {-1, 0x00EA, 600},
    {-1, 0x016E, 600},
    {-1, 0x00DC, 600},
    {-1, 0x0105, 600},
    {-1, 0x00DA, 600},
    {-1, 0x0173, 600},
    {-1, 0x00CB, 600},
    {-1, 0x0110, 600},
    {-1, 0xF6C3, 600},
    {-1, 0x00A9, 600},
    {-1, 0x0112, 600},
    {-1, 0x010D, 600},
    {-1, 0x00E5, 600},
    {-1, 0x0145, 600},
    {-1, 0x013A, 600},
    {-1, 0x00E0, 600},
    {-1, 0x0162, 600},
    {-1, 0x0106, 600},
    {-1, 0x00E3, 600},
    {-1, 0x0116, 600},
    {-1, 0x0161, 600},
    {-1, 0x015F, 600},
    {-1, 0x00ED, 600},
    {-1, 0x25CA, 600},
    {-1, 0x0158, 600},
    {-1, 0x0122, 600},
    {-1, 0x00FB, 600},
    {-1, 0x00E2, 600},
    {-1, 0x0100, 600},
    {-1, 0x0159, 600},
    {-1, 0x00E7, 600},
    {-1, 0x017B, 600},
    {-1, 0x00DE, 600},
    {-1, 0x014C, 600},
    {-1, 0x0154, 600},
    {-1, 0x015A, 600},
    {-1, 0x010F, 600},
    {-1, 0x016A, 600},
    {-1, 0x016F, 600},
    {-1, 0x00B3, 600},
    {-1, 0x00D2, 600},
    {-1, 0x00C0, 600},
    {-1, 0x0102, 600},
    {-1, 0x00D7, 600},
    {-1, 0x00FA, 600},
    {-1, 0x0164, 600},
    {-1, 0x2202, 600},
    {-1, 0x00FF, 600},
    {-1, 0x0143, 600},
    {-1, 0x00EE, 600},
    {-1, 0x00CA, 600},
    {-1, 0x00E4, 600},
    {-1, 0x00EB, 600},
    {-1, 0x0107, 600},
    {-1, 0x0144, 600},
    {-1, 0x016B, 600},
    {-1, 0x0147, 600},
    {-1, 0x00CD, 600},
    {-1, 0x00B1, 600},
    {-1, 0x00A6, 600},
    {-1, 0x00AE, 600},
    {-1, 0x011E, 600},
    {-1, 0x0130, 600},
    {-1, 0x2211, 600},
    {-1, 0x00C8, 600},
    {-1, 0x0155, 600},
    {-1, 0x014D, 600},
    {-1, 0x0179, 600},
    {-1, 0x017D, 600},
    {-1, 0x2265, 600},
    {-1, 0x00D0, 600},
    {-1, 0x00C7, 600},
    {-1, 0x013C, 600},
    {-1, 0x0165, 600},
    {-1, 0x0119, 600},
    {-1, 0x0172, 600},
    {-1, 0x00C1, 600},
    {-1, 0x00C4, 600},
    {-1, 0x00E8, 600},
    {-1, 0x017A, 600},
    {-1, 0x012F, 600},
    {-1, 0x00D3, 600},
    {-1, 0x00F3, 600},
    {-1, 0x0101, 600},
    {-1, 0x015B, 600},
    {-1, 0x00EF, 600},
    {-1, 0x00D4, 600},
    {-1, 0x00D9, 600},
    {-1, 0x0394, 600},
    {-1, 0x00FE, 600},
    {-1, 0x00B2, 600},
    {-1, 0x00D6, 600},
    {-1, 0x00B5, 600},
    {-1, 0x00EC, 600},
    {-1, 0x0151, 600},
    {-1, 0x0118, 600},
    {-1, 0x0111, 600},
    {-1, 0x00BE, 600},
    {-1, 0x015E, 600},
    {-1, 0x013E, 600},
    {-1, 0x0136, 600},
    {-1, 0x0139, 600},
    {-1, 0x2122, 600},
    {-1, 0x0117, 600},
    {-1, 0x00CC, 600},
    {-1, 0x012A, 600},
    {-1, 0x013D, 600},
    {-1, 0x00BD, 600},
    {-1, 0x2264, 600},
    {-1, 0x00F4, 600},
    {-1, 0x00F1, 600},
    {-1, 0x0170, 600},
    {-1, 0x00C9, 600},
    {-1, 0x0113, 600},
    {-1, 0x011F, 600},
    {-1, 0x00BC, 600},
    {-1, 0x0160, 600},
    {-1, 0x0218, 600},
    {-1, 0x0150, 600},
    {-1, 0x00B0, 600},
    {-1, 0x00F2, 600},
    {-1, 0x010C, 600},
    {-1, 0x00F9, 600},
    {-1, 0x221A, 600},
    {-1, 0x010E, 600},
    {-1, 0x0157, 600},
    {-1, 0x00D1, 600},
    {-1, 0x00F5, 600},
    {-1, 0x0156, 600},
    {-1, 0x013B, 600},
    {-1, 0x00C3, 600},
    {-1, 0x0104, 600},
    {-1, 0x00C5, 600},
    {-1, 0x00D5, 600},
    {-1, 0x017C, 600},
    {-1, 0x011A, 600},
    {-1, 0x012E, 600},
    {-1, 0x0137, 600},
    {-1, 0x2212, 600},
    {-1, 0x00CE, 600},
    {-1, 0x0148, 600},
    {-1, 0x0163, 600},
    {-1, 0x00AC, 600},
    {-1, 0x00F6, 600},
    {-1, 0x00FC, 600},
    {-1, 0x2260, 600},
    {-1, 0x0123, 600},
    {-1, 0x00F0, 600},
    {-1, 0x017E, 600},
    {-1, 0x0146, 600},
    {-1, 0x00B9, 600},
    {-1, 0x012B, 600},
    {-1, 0x20AC, 600},
    {-1, 0xFFFF, 0}
    };

static const HPDF_CharData CHAR_DATA_HELVETICA[316] = {
    {32, 0x0020, 278},
    {33, 0x0021, 278},
    {34, 0x0022, 355},
    {35, 0x0023, 556},
    {36, 0x0024, 556},
    {37, 0x0025, 889},
    {38, 0x0026, 667},
    {39, 0x2019, 222},
    {40, 0x0028, 333},
    {41, 0x0029, 333},
    {42, 0x002A, 389},
    {43, 0x002B, 584},
    {44, 0x002C, 278},
    {45, 0x002D, 333},
    {46, 0x002E, 278},
    {47, 0x002F, 278},
    {48, 0x0030, 556},
    {49, 0x0031, 556},
    {50, 0x0032, 556},
    {51, 0x0033, 556},
    {52, 0x0034, 556},
    {53, 0x0035, 556},
    {54, 0x0036, 556},
    {55, 0x0037, 556},
    {56, 0x0038, 556},
    {57, 0x0039, 556},
    {58, 0x003A, 278},
    {59, 0x003B, 278},
    {60, 0x003C, 584},
    {61, 0x003D, 584},
    {62, 0x003E, 584},
    {63, 0x003F, 556},
    {64, 0x0040, 1015},
    {65, 0x0041, 667},
    {66, 0x0042, 667},
    {67, 0x0043, 722},
    {68, 0x0044, 722},
    {69, 0x0045, 667},
    {70, 0x0046, 611},
    {71, 0x0047, 778},
    {72, 0x0048, 722},
    {73, 0x0049, 278},
    {74, 0x004A, 500},
    {75, 0x004B, 667},
    {76, 0x004C, 556},
    {77, 0x004D, 833},
    {78, 0x004E, 722},
    {79, 0x004F, 778},
    {80, 0x0050, 667},
    {81, 0x0051, 778},
    {82, 0x0052, 722},
    {83, 0x0053, 667},
    {84, 0x0054, 611},
    {85, 0x0055, 722},
    {86, 0x0056, 667},
    {87, 0x0057, 944},
    {88, 0x0058, 667},
    {89, 0x0059, 667},
    {90, 0x005A, 611},
    {91, 0x005B, 278},
    {92, 0x005C, 278},
    {93, 0x005D, 278},
    {94, 0x005E, 469},
    {95, 0x005F, 556},
    {96, 0x2018, 222},
    {97, 0x0061, 556},
    {98, 0x0062, 556},
    {99, 0x0063, 500},
    {100, 0x0064, 556},
    {101, 0x0065, 556},
    {102, 0x0066, 278},
    {103, 0x0067, 556},
    {104, 0x0068, 556},
    {105, 0x0069, 222},
    {106, 0x006A, 222},
    {107, 0x006B, 500},
    {108, 0x006C, 222},
    {109, 0x006D, 833},
    {110, 0x006E, 556},
    {111, 0x006F, 556},
    {112, 0x0070, 556},
    {113, 0x0071, 556},
    {114, 0x0072, 333},
    {115, 0x0073, 500},
    {116, 0x0074, 278},
    {117, 0x0075, 556},
    {118, 0x0076, 500},
    {119, 0x0077, 722},
    {120, 0x0078, 500},
    {121, 0x0079, 500},
    {122, 0x007A, 500},
    {123, 0x007B, 334},
    {124, 0x007C, 260},
    {125, 0x007D, 334},
    {126, 0x007E, 584},
    {161, 0x00A1, 333},
    {162, 0x00A2, 556},
    {163, 0x00A3, 556},
    {164, 0x2044, 167},
    {165, 0x00A5, 556},
    {166, 0x0192, 556},
    {167, 0x00A7, 556},
    {168, 0x00A4, 556},
    {169, 0x0027, 191},
    {170, 0x201C, 333},
    {171, 0x00AB, 556},
    {172, 0x2039, 333},
    {173, 0x203A, 333},
    {174, 0xFB01, 500},
    {175, 0xFB02, 500},
    {177, 0x2013, 556},
    {178, 0x2020, 556},
    {179, 0x2021, 556},
    {180, 0x00B7, 278},
    {182, 0x00B6, 537},
    {183, 0x2022, 350},
    {184, 0x201A, 222},
    {185, 0x201E, 333},
    {186, 0x201D, 333},
    {187, 0x00BB, 556},
    {188, 0x2026, 1000},
    {189, 0x2030, 1000},
    {191, 0x00BF, 611},
    {193, 0x0060, 333},
    {194, 0x00B4, 333},
    {195, 0x02C6, 333},
    {196, 0x02DC, 333},
    {197, 0x00AF, 333},
    {198, 0x02D8, 333},
    {199, 0x02D9, 333},
    {200, 0x00A8, 333},
    {202, 0x02DA, 333},
    {203, 0x00B8, 333},
    {205, 0x02DD, 333},
    {206, 0x02DB, 333},
    {207, 0x02C7, 333},
    {208, 0x2014, 1000},
    {225, 0x00C6, 1000},
    {227, 0x00AA, 370},
    {232, 0x0141, 556},
    {233, 0x00D8, 778},
    {234, 0x0152, 1000},
    {235, 0x00BA, 365},
    {241, 0x00E6, 889},
    {245, 0x0131, 278},
    {248, 0x0142, 222},
    {249, 0x00F8, 611},
    {250, 0x0153, 944},
    {251, 0x00DF, 611},
    {-1, 0x00CF, 278},
    {-1, 0x00E9, 556},
    {-1, 0x0103, 556},
    {-1, 0x0171, 556},
    {-1, 0x011B, 556},
    {-1, 0x0178, 667},
    {-1, 0x00F7, 584},
    {-1, 0x00DD, 667},
    {-1, 0x00C2, 667},
    {-1, 0x00E1, 556},
    {-1, 0x00DB, 722},
    {-1, 0x00FD, 500},
    {-1, 0x0219, 500},
    {-1, 0x00EA, 556},
    {-1, 0x016E, 722},
    {-1, 0x00DC, 722},
    {-1, 0x0105, 556},
    {-1, 0x00DA, 722},
    {-1, 0x0173, 556},
    {-1, 0x00CB, 667},
    {-1, 0x0110, 722},
    {-1, 0xF6C3, 250},
    {-1, 0x00A9, 737},
    {-1, 0x0112, 667},
    {-1, 0x010D, 500},
    {-1, 0x00E5, 556},
    {-1, 0x0145, 722},
    {-1, 0x013A, 222},
    {-1, 0x00E0, 556},
    {-1, 0x0162, 611},
    {-1, 0x0106, 722},
    {-1, 0x00E3, 556},
    {-1, 0x0116, 667},
    {-1, 0x0161, 500},
    {-1, 0x015F, 500},
    {-1, 0x00ED, 278},
    {-1, 0x25CA, 471},
    {-1, 0x0158, 722},
    {-1, 0x0122, 778},
    {-1, 0x00FB, 556},
    {-1, 0x00E2, 556},
    {-1, 0x0100, 667},
    {-1, 0x0159, 333},
    {-1, 0x00E7, 500},
    {-1, 0x017B, 611},
    {-1, 0x00DE, 667},
    {-1, 0x014C, 778},
    {-1, 0x0154, 722},
    {-1, 0x015A, 667},
    {-1, 0x010F, 643},
    {-1, 0x016A, 722},
    {-1, 0x016F, 556},
    {-1, 0x00B3, 333},
    {-1, 0x00D2, 778},
    {-1, 0x00C0, 667},
    {-1, 0x0102, 667},
    {-1, 0x00D7, 584},
    {-1, 0x00FA, 556},
    {-1, 0x0164, 611},
    {-1, 0x2202, 476},
    {-1, 0x00FF, 500},
    {-1, 0x0143, 722},
    {-1, 0x00EE, 278},
    {-1, 0x00CA, 667},
    {-1, 0x00E4, 556},
    {-1, 0x00EB, 556},
    {-1, 0x0107, 500},
    {-1, 0x0144, 556},
    {-1, 0x016B, 556},
    {-1, 0x0147, 722},
    {-1, 0x00CD, 278},
    {-1, 0x00B1, 584},
    {-1, 0x00A6, 260},
    {-1, 0x00AE, 737},
    {-1, 0x011E, 778},
    {-1, 0x0130, 278},
    {-1, 0x2211, 600},
    {-1, 0x00C8, 667},
    {-1, 0x0155, 333},
    {-1, 0x014D, 556},
    {-1, 0x0179, 611},
    {-1, 0x017D, 611},
    {-1, 0x2265, 549},
    {-1, 0x00D0, 722},
    {-1, 0x00C7, 722},
    {-1, 0x013C, 222},
    {-1, 0x0165, 316},
    {-1, 0x0119, 556},
    {-1, 0x0172, 722},
    {-1, 0x00C1, 667},
    {-1, 0x00C4, 667},
    {-1, 0x00E8, 556},
    {-1, 0x017A, 500},
    {-1, 0x012F, 222},
    {-1, 0x00D3, 778},
    {-1, 0x00F3, 556},
    {-1, 0x0101, 556},
    {-1, 0x015B, 500},
    {-1, 0x00EF, 278},
    {-1, 0x00D4, 778},
    {-1, 0x00D9, 722},
    {-1, 0x0394, 612},
    {-1, 0x00FE, 556},
    {-1, 0x00B2, 333},
    {-1, 0x00D6, 778},
    {-1, 0x00B5, 556},
    {-1, 0x00EC, 278},
    {-1, 0x0151, 556},
    {-1, 0x0118, 667},
    {-1, 0x0111, 556},
    {-1, 0x00BE, 834},
    {-1, 0x015E, 667},
    {-1, 0x013E, 299},
    {-1, 0x0136, 667},
    {-1, 0x0139, 556},
    {-1, 0x2122, 1000},
    {-1, 0x0117, 556},
    {-1, 0x00CC, 278},
    {-1, 0x012A, 278},
    {-1, 0x013D, 556},
    {-1, 0x00BD, 834},
    {-1, 0x2264, 549},
    {-1, 0x00F4, 556},
    {-1, 0x00F1, 556},
    {-1, 0x0170, 722},
    {-1, 0x00C9, 667},
    {-1, 0x0113, 556},
    {-1, 0x011F, 556},
    {-1, 0x00BC, 834},
    {-1, 0x0160, 667},
    {-1, 0x0218, 667},
    {-1, 0x0150, 778},
    {-1, 0x00B0, 400},
    {-1, 0x00F2, 556},
    {-1, 0x010C, 722},
    {-1, 0x00F9, 556},
    {-1, 0x221A, 453},
    {-1, 0x010E, 722},
    {-1, 0x0157, 333},
    {-1, 0x00D1, 722},
    {-1, 0x00F5, 556},
    {-1, 0x0156, 722},
    {-1, 0x013B, 556},
    {-1, 0x00C3, 667},
    {-1, 0x0104, 667},
    {-1, 0x00C5, 667},
    {-1, 0x00D5, 778},
    {-1, 0x017C, 500},
    {-1, 0x011A, 667},
    {-1, 0x012E, 278},
    {-1, 0x0137, 500},
    {-1, 0x2212, 584},
    {-1, 0x00CE, 278},
    {-1, 0x0148, 556},
    {-1, 0x0163, 278},
    {-1, 0x00AC, 584},
    {-1, 0x00F6, 556},
    {-1, 0x00FC, 556},
    {-1, 0x2260, 549},
    {-1, 0x0123, 556},
    {-1, 0x00F0, 556},
    {-1, 0x017E, 500},
    {-1, 0x0146, 556},
    {-1, 0x00B9, 333},
    {-1, 0x012B, 278},
    {-1, 0x20AC, 556},
    {-1, 0xFFFF, 0}
    };

static const HPDF_CharData CHAR_DATA_HELVETICA_BOLD[316] = {
    {32, 0x0020, 278},
    {33, 0x0021, 333},
    {34, 0x0022, 474},
    {35, 0x0023, 556},
    {36, 0x0024, 556},
    {37, 0x0025, 889},
    {38, 0x0026, 722},
    {39, 0x2019, 278},
    {40, 0x0028, 333},
    {41, 0x0029, 333},
    {42, 0x002A, 389},
    {43, 0x002B, 584},
    {44, 0x002C, 278},
    {45, 0x002D, 333},
    {46, 0x002E, 278},
    {47, 0x002F, 278},
    {48, 0x0030, 556},
    {49, 0x0031, 556},
    {50, 0x0032, 556},
    {51, 0x0033, 556},
    {52, 0x0034, 556},
    {53, 0x0035, 556},
    {54, 0x0036, 556},
    {55, 0x0037, 556},
    {56, 0x0038, 556},
    {57, 0x0039, 556},
    {58, 0x003A, 333},
    {59, 0x003B, 333},
    {60, 0x003C, 584},
    {61, 0x003D, 584},
    {62, 0x003E, 584},
    {63, 0x003F, 611},
    {64, 0x0040, 975},
    {65, 0x0041, 722},
    {66, 0x0042, 722},
    {67, 0x0043, 722},
    {68, 0x0044, 722},
    {69, 0x0045, 667},
    {70, 0x0046, 611},
    {71, 0x0047, 778},
    {72, 0x0048, 722},
    {73, 0x0049, 278},
    {74, 0x004A, 556},
    {75, 0x004B, 722},
    {76, 0x004C, 611},
    {77, 0x004D, 833},
    {78, 0x004E, 722},
    {79, 0x004F, 778},
    {80, 0x0050, 667},
    {81, 0x0051, 778},
    {82, 0x0052, 722},
    {83, 0x0053, 667},
    {84, 0x0054, 611},
    {85, 0x0055, 722},
    {86, 0x0056, 667},
    {87, 0x0057, 944},
    {88, 0x0058, 667},
    {89, 0x0059, 667},
    {90, 0x005A, 611},
    {91, 0x005B, 333},
    {92, 0x005C, 278},
    {93, 0x005D, 333},
    {94, 0x005E, 584},
    {95, 0x005F, 556},
    {96, 0x2018, 278},
    {97, 0x0061, 556},
    {98, 0x0062, 611},
    {99, 0x0063, 556},
    {100, 0x0064, 611},
    {101, 0x0065, 556},
    {102, 0x0066, 333},
    {103, 0x0067, 611},
    {104, 0x0068, 611},
    {105, 0x0069, 278},
    {106, 0x006A, 278},
    {107, 0x006B, 556},
    {108, 0x006C, 278},
    {109, 0x006D, 889},
    {110, 0x006E, 611},
    {111, 0x006F, 611},
    {112, 0x0070, 611},
    {113, 0x0071, 611},
    {114, 0x0072, 389},
    {115, 0x0073, 556},
    {116, 0x0074, 333},
    {117, 0x0075, 611},
    {118, 0x0076, 556},
    {119, 0x0077, 778},
    {120, 0x0078, 556},
    {121, 0x0079, 556},
    {122, 0x007A, 500},
    {123, 0x007B, 389},
    {124, 0x007C, 280},
    {125, 0x007D, 389},
    {126, 0x007E, 584},
    {161, 0x00A1, 333},
    {162, 0x00A2, 556},
    {163, 0x00A3, 556},
    {164, 0x2044, 167},
    {165, 0x00A5, 556},
    {166, 0x0192, 556},
    {167, 0x00A7, 556},
    {168, 0x00A4, 556},
    {169, 0x0027, 238},
    {170, 0x201C, 500},
    {171, 0x00AB, 556},
    {172, 0x2039, 333},
    {173, 0x203A, 333},
    {174, 0xFB01, 611},
    {175, 0xFB02, 611},
    {177, 0x2013, 556},
    {178, 0x2020, 556},
    {179, 0x2021, 556},
    {180, 0x00B7, 278},
    {182, 0x00B6, 556},
    {183, 0x2022, 350},
    {184, 0x201A, 278},
    {185, 0x201E, 500},
    {186, 0x201D, 500},
    {187, 0x00BB, 556},
    {188, 0x2026, 1000},
    {189, 0x2030, 1000},
    {191, 0x00BF, 611},
    {193, 0x0060, 333},
    {194, 0x00B4, 333},
    {195, 0x02C6, 333},
    {196, 0x02DC, 333},
    {197, 0x00AF, 333},
    {198, 0x02D8, 333},
    {199, 0x02D9, 333},
    {200, 0x00A8, 333},
    {202, 0x02DA, 333},
    {203, 0x00B8, 333},
    {205, 0x02DD, 333},
    {206, 0x02DB, 333},
    {207, 0x02C7, 333},
    {208, 0x2014, 1000},
    {225, 0x00C6, 1000},
    {227, 0x00AA, 370},
    {232, 0x0141, 611},
    {233, 0x00D8, 778},
    {234, 0x0152, 1000},
    {235, 0x00BA, 365},
    {241, 0x00E6, 889},
    {245, 0x0131, 278},
    {248, 0x0142, 278},
    {249, 0x00F8, 611},
    {250, 0x0153, 944},
    {251, 0x00DF, 611},
    {-1, 0x00CF, 278},
    {-1, 0x00E9, 556},
    {-1, 0x0103, 556},
    {-1, 0x0171, 611},
    {-1, 0x011B, 556},
    {-1, 0x0178, 667},
    {-1, 0x00F7, 584},
    {-1, 0x00DD, 667},
    {-1, 0x00C2, 722},
    {-1, 0x00E1, 556},
    {-1, 0x00DB, 722},
    {-1, 0x00FD, 556},
    {-1, 0x0219, 556},
    {-1, 0x00EA, 556},
    {-1, 0x016E, 722},
    {-1, 0x00DC, 722},
    {-1, 0x0105, 556},
    {-1, 0x00DA, 722},
    {-1, 0x0173, 611},
    {-1, 0x00CB, 667},
    {-1, 0x0110, 722},
    {-1, 0xF6C3, 250},
    {-1, 0x00A9, 737},
    {-1, 0x0112, 667},
    {-1, 0x010D, 556},
    {-1, 0x00E5, 556},
    {-1, 0x0145, 722},
    {-1, 0x013A, 278},
    {-1, 0x00E0, 556},
    {-1, 0x0162, 611},
    {-1, 0x0106, 722},
    {-1, 0x00E3, 556},
    {-1, 0x0116, 667},
    {-1, 0x0161, 556},
    {-1, 0x015F, 556},
    {-1, 0x00ED, 278},
    {-1, 0x25CA, 494},
    {-1, 0x0158, 722},
    {-1, 0x0122, 778},
    {-1, 0x00FB, 611},
    {-1, 0x00E2, 556},
    {-1, 0x0100, 722},
    {-1, 0x0159, 389},
    {-1, 0x00E7, 556},
    {-1, 0x017B, 611},
    {-1, 0x00DE, 667},
    {-1, 0x014C, 778},
    {-1, 0x0154, 722},
    {-1, 0x015A, 667},
    {-1, 0x010F, 743},
    {-1, 0x016A, 722},
    {-1, 0x016F, 611},
    {-1, 0x00B3, 333},
    {-1, 0x00D2, 778},
    {-1, 0x00C0, 722},
    {-1, 0x0102, 722},
    {-1, 0x00D7, 584},
    {-1, 0x00FA, 611},
    {-1, 0x0164, 611},
    {-1, 0x2202, 494},
    {-1, 0x00FF, 556},
    {-1, 0x0143, 722},
    {-1, 0x00EE, 278},
    {-1, 0x00CA, 667},
    {-1, 0x00E4, 556},
    {-1, 0x00EB, 556},
    {-1, 0x0107, 556},
    {-1, 0x0144, 611},
    {-1, 0x016B, 611},
    {-1, 0x0147, 722},
    {-1, 0x00CD, 278},
    {-1, 0x00B1, 584},
    {-1, 0x00A6, 280},
    {-1, 0x00AE, 737},
    {-1, 0x011E, 778},
    {-1, 0x0130, 278},
    {-1, 0x2211, 600},
    {-1, 0x00C8, 667},
    {-1, 0x0155, 389},
    {-1, 0x014D, 611},
    {-1, 0x0179, 611},
    {-1, 0x017D, 611},
    {-1, 0x2265, 549},
    {-1, 0x00D0, 722},
    {-1, 0x00C7, 722},
    {-1, 0x013C, 278},
    {-1, 0x0165, 389},
    {-1, 0x0119, 556},
    {-1, 0x0172, 722},
    {-1, 0x00C1, 722},
    {-1, 0x00C4, 722},
    {-1, 0x00E8, 556},
    {-1, 0x017A, 500},
    {-1, 0x012F, 278},
    {-1, 0x00D3, 778},
    {-1, 0x00F3, 611},
    {-1, 0x0101, 556},
    {-1, 0x015B, 556},
    {-1, 0x00EF, 278},
    {-1, 0x00D4, 778},
    {-1, 0x00D9, 722},
    {-1, 0x0394, 612},
    {-1, 0x00FE, 611},
    {-1, 0x00B2, 333},
    {-1, 0x00D6, 778},
    {-1, 0x00B5, 611},
    {-1, 0x00EC, 278},
    {-1, 0x0151, 611},
    {-1, 0x0118, 667},
    {-1, 0x0111, 611},
    {-1, 0x00BE, 834},
    {-1, 0x015E, 667},
    {-1, 0x013E, 400},
    {-1, 0x0136, 722},
    {-1, 0x0139, 611},
    {-1, 0x2122, 1000},
    {-1, 0x0117, 556},
    {-1, 0x00CC, 278},
    {-1, 0x012A, 278},
    {-1, 0x013D, 611},
    {-1, 0x00BD, 834},
    {-1, 0x2264, 549},
    {-1, 0x00F4, 611},
    {-1, 0x00F1, 611},
    {-1, 0x0170, 722},
    {-1, 0x00C9, 667},
    {-1, 0x0113, 556},
    {-1, 0x011F, 611},
    {-1, 0x00BC, 834},
    {-1, 0x0160, 667},
    {-1, 0x0218, 667},
    {-1, 0x0150, 778},
    {-1, 0x00B0, 400},
    {-1, 0x00F2, 611},
    {-1, 0x010C, 722},
    {-1, 0x00F9, 611},
    {-1, 0x221A, 549},
    {-1, 0x010E, 722},
    {-1, 0x0157, 389},
    {-1, 0x00D1, 722},
    {-1, 0x00F5, 611},
    {-1, 0x0156, 722},
    {-1, 0x013B, 611},
    {-1, 0x00C3, 722},
    {-1, 0x0104, 722},
    {-1, 0x00C5, 722},
    {-1, 0x00D5, 778},
    {-1, 0x017C, 500},
    {-1, 0x011A, 667},
    {-1, 0x012E, 278},
    {-1, 0x0137, 556},
    {-1, 0x2212, 584},
    {-1, 0x00CE, 278},
    {-1, 0x0148, 611},
    {-1, 0x0163, 333},
    {-1, 0x00AC, 584},
    {-1, 0x00F6, 611},
    {-1, 0x00FC, 611},
    {-1, 0x2260, 549},
    {-1, 0x0123, 611},
    {-1, 0x00F0, 611},
    {-1, 0x017E, 500},
    {-1, 0x0146, 611},
    {-1, 0x00B9, 333},
    {-1, 0x012B, 278},
    {-1, 0x20AC, 556},
    {-1, 0xFFFF, 0}
    };

static const HPDF_CharData CHAR_DATA_HELVETICA_BOLD_OBLIQUE[316] = {
    {32, 0x0020, 278},
    {33, 0x0021, 333},
    {34, 0x0022, 474},
    {35, 0x0023, 556},
    {36, 0x0024, 556},
    {37, 0x0025, 889},
    {38, 0x0026, 722},
    {39, 0x2019, 278},
    {40, 0x0028, 333},
    {41, 0x0029, 333},
    {42, 0x002A, 389},
    {43, 0x002B, 584},
    {44, 0x002C, 278},
    {45, 0x002D, 333},
    {46, 0x002E, 278},
    {47, 0x002F, 278},
    {48, 0x0030, 556},
    {49, 0x0031, 556},
    {50, 0x0032, 556},
    {51, 0x0033, 556},
    {52, 0x0034, 556},
    {53, 0x0035, 556},
    {54, 0x0036, 556},
    {55, 0x0037, 556},
    {56, 0x0038, 556},
    {57, 0x0039, 556},
    {58, 0x003A, 333},
    {59, 0x003B, 333},
    {60, 0x003C, 584},
    {61, 0x003D, 584},
    {62, 0x003E, 584},
    {63, 0x003F, 611},
    {64, 0x0040, 975},
    {65, 0x0041, 722},
    {66, 0x0042, 722},
    {67, 0x0043, 722},
    {68, 0x0044, 722},
    {69, 0x0045, 667},
    {70, 0x0046, 611},
    {71, 0x0047, 778},
    {72, 0x0048, 722},
    {73, 0x0049, 278},
    {74, 0x004A, 556},
    {75, 0x004B, 722},
    {76, 0x004C, 611},
    {77, 0x004D, 833},
    {78, 0x004E, 722},
    {79, 0x004F, 778},
    {80, 0x0050, 667},
    {81, 0x0051, 778},
    {82, 0x0052, 722},
    {83, 0x0053, 667},
    {84, 0x0054, 611},
    {85, 0x0055, 722},
    {86, 0x0056, 667},
    {87, 0x0057, 944},
    {88, 0x0058, 667},
    {89, 0x0059, 667},
    {90, 0x005A, 611},
    {91, 0x005B, 333},
    {92, 0x005C, 278},
    {93, 0x005D, 333},
    {94, 0x005E, 584},
    {95, 0x005F, 556},
    {96, 0x2018, 278},
    {97, 0x0061, 556},
    {98, 0x0062, 611},
    {99, 0x0063, 556},
    {100, 0x0064, 611},
    {101, 0x0065, 556},
    {102, 0x0066, 333},
    {103, 0x0067, 611},
    {104, 0x0068, 611},
    {105, 0x0069, 278},
    {106, 0x006A, 278},
    {107, 0x006B, 556},
    {108, 0x006C, 278},
    {109, 0x006D, 889},
    {110, 0x006E, 611},
    {111, 0x006F, 611},
    {112, 0x0070, 611},
    {113, 0x0071, 611},
    {114, 0x0072, 389},
    {115, 0x0073, 556},
    {116, 0x0074, 333},
    {117, 0x0075, 611},
    {118, 0x0076, 556},
    {119, 0x0077, 778},
    {120, 0x0078, 556},
    {121, 0x0079, 556},
    {122, 0x007A, 500},
    {123, 0x007B, 389},
    {124, 0x007C, 280},
    {125, 0x007D, 389},
    {126, 0x007E, 584},
    {161, 0x00A1, 333},
    {162, 0x00A2, 556},
    {163, 0x00A3, 556},
    {164, 0x2044, 167},
    {165, 0x00A5, 556},
    {166, 0x0192, 556},
    {167, 0x00A7, 556},
    {168, 0x00A4, 556},
    {169, 0x0027, 238},
    {170, 0x201C, 500},
    {171, 0x00AB, 556},
    {172, 0x2039, 333},
    {173, 0x203A, 333},
    {174, 0xFB01, 611},
    {175, 0xFB02, 611},
    {177, 0x2013, 556},
    {178, 0x2020, 556},
    {179, 0x2021, 556},
    {180, 0x00B7, 278},
    {182, 0x00B6, 556},
    {183, 0x2022, 350},
    {184, 0x201A, 278},
    {185, 0x201E, 500},
    {186, 0x201D, 500},
    {187, 0x00BB, 556},
    {188, 0x2026, 1000},
    {189, 0x2030, 1000},
    {191, 0x00BF, 611},
    {193, 0x0060, 333},
    {194, 0x00B4, 333},
    {195, 0x02C6, 333},
    {196, 0x02DC, 333},
    {197, 0x00AF, 333},
    {198, 0x02D8, 333},
    {199, 0x02D9, 333},
    {200, 0x00A8, 333},
    {202, 0x02DA, 333},
    {203, 0x00B8, 333},
    {205, 0x02DD, 333},
    {206, 0x02DB, 333},
    {207, 0x02C7, 333},
    {208, 0x2014, 1000},
    {225, 0x00C6, 1000},
    {227, 0x00AA, 370},
    {232, 0x0141, 611},
    {233, 0x00D8, 778},
    {234, 0x0152, 1000},
    {235, 0x00BA, 365},
    {241, 0x00E6, 889},
    {245, 0x0131, 278},
    {248, 0x0142, 278},
    {249, 0x00F8, 611},
    {250, 0x0153, 944},
    {251, 0x00DF, 611},
    {-1, 0x00CF, 278},
    {-1, 0x00E9, 556},
    {-1, 0x0103, 556},
    {-1, 0x0171, 611},
    {-1, 0x011B, 556},
    {-1, 0x0178, 667},
    {-1, 0x00F7, 584},
    {-1, 0x00DD, 667},
    {-1, 0x00C2, 722},
    {-1, 0x00E1, 556},
    {-1, 0x00DB, 722},
    {-1, 0x00FD, 556},
    {-1, 0x0219, 556},
    {-1, 0x00EA, 556},
    {-1, 0x016E, 722},
    {-1, 0x00DC, 722},
    {-1, 0x0105, 556},
    {-1, 0x00DA, 722},
    {-1, 0x0173, 611},
    {-1, 0x00CB, 667},
    {-1, 0x0110, 722},
    {-1, 0xF6C3, 250},
    {-1, 0x00A9, 737},
    {-1, 0x0112, 667},
    {-1, 0x010D, 556},
    {-1, 0x00E5, 556},
    {-1, 0x0145, 722},
    {-1, 0x013A, 278},
    {-1, 0x00E0, 556},
    {-1, 0x0162, 611},
    {-1, 0x0106, 722},
    {-1, 0x00E3, 556},
    {-1, 0x0116, 667},
    {-1, 0x0161, 556},
    {-1, 0x015F, 556},
    {-1, 0x00ED, 278},
    {-1, 0x25CA, 494},
    {-1, 0x0158, 722},
    {-1, 0x0122, 778},
    {-1, 0x00FB, 611},
    {-1, 0x00E2, 556},
    {-1, 0x0100, 722},
    {-1, 0x0159, 389},
    {-1, 0x00E7, 556},
    {-1, 0x017B, 611},
    {-1, 0x00DE, 667},
    {-1, 0x014C, 778},
    {-1, 0x0154, 722},
    {-1, 0x015A, 667},
    {-1, 0x010F, 743},
    {-1, 0x016A, 722},
    {-1, 0x016F, 611},
    {-1, 0x00B3, 333},
    {-1, 0x00D2, 778},
    {-1, 0x00C0, 722},
    {-1, 0x0102, 722},
    {-1, 0x00D7, 584},
    {-1, 0x00FA, 611},
    {-1, 0x0164, 611},
    {-1, 0x2202, 494},
    {-1, 0x00FF, 556},
    {-1, 0x0143, 722},
    {-1, 0x00EE, 278},
    {-1, 0x00CA, 667},
    {-1, 0x00E4, 556},
    {-1, 0x00EB, 556},
    {-1, 0x0107, 556},
    {-1, 0x0144, 611},
    {-1, 0x016B, 611},
    {-1, 0x0147, 722},
    {-1, 0x00CD, 278},
    {-1, 0x00B1, 584},
    {-1, 0x00A6, 280},
    {-1, 0x00AE, 737},
    {-1, 0x011E, 778},
    {-1, 0x0130, 278},
    {-1, 0x2211, 600},
    {-1, 0x00C8, 667},
    {-1, 0x0155, 389},
    {-1, 0x014D, 611},
    {-1, 0x0179, 611},
    {-1, 0x017D, 611},
    {-1, 0x2265, 549},
    {-1, 0x00D0, 722},
    {-1, 0x00C7, 722},
    {-1, 0x013C, 278},
    {-1, 0x0165, 389},
    {-1, 0x0119, 556},
    {-1, 0x0172, 722},
    {-1, 0x00C1, 722},
    {-1, 0x00C4, 722},
    {-1, 0x00E8, 556},
    {-1, 0x017A, 500},
    {-1, 0x012F, 278},
    {-1, 0x00D3, 778},
    {-1, 0x00F3, 611},
    {-1, 0x0101, 556},
    {-1, 0x015B, 556},
    {-1, 0x00EF, 278},
    {-1, 0x00D4, 778},
    {-1, 0x00D9, 722},
    {-1, 0x0394, 612},
    {-1, 0x00FE, 611},
    {-1, 0x00B2, 333},
    {-1, 0x00D6, 778},
    {-1, 0x00B5, 611},
    {-1, 0x00EC, 278},
    {-1, 0x0151, 611},
    {-1, 0x0118, 667},
    {-1, 0x0111, 611},
    {-1, 0x00BE, 834},
    {-1, 0x015E, 667},
    {-1, 0x013E, 400},
    {-1, 0x0136, 722},
    {-1, 0x0139, 611},
    {-1, 0x2122, 1000},
    {-1, 0x0117, 556},
    {-1, 0x00CC, 278},
    {-1, 0x012A, 278},
    {-1, 0x013D, 611},
    {-1, 0x00BD, 834},
    {-1, 0x2264, 549},
    {-1, 0x00F4, 611},
    {-1, 0x00F1, 611},
    {-1, 0x0170, 722},
    {-1, 0x00C9, 667},
    {-1, 0x0113, 556},
    {-1, 0x011F, 611},
    {-1, 0x00BC, 834},
    {-1, 0x0160, 667},
    {-1, 0x0218, 667},
    {-1, 0x0150, 778},
    {-1, 0x00B0, 400},
    {-1, 0x00F2, 611},
    {-1, 0x010C, 722},
    {-1, 0x00F9, 611},
    {-1, 0x221A, 549},
    {-1, 0x010E, 722},
    {-1, 0x0157, 389},
    {-1, 0x00D1, 722},
    {-1, 0x00F5, 611},
    {-1, 0x0156, 722},
    {-1, 0x013B, 611},
    {-1, 0x00C3, 722},
    {-1, 0x0104, 722},
    {-1, 0x00C5, 722},
    {-1, 0x00D5, 778},
    {-1, 0x017C, 500},
    {-1, 0x011A, 667},
    {-1, 0x012E, 278},
    {-1, 0x0137, 556},
    {-1, 0x2212, 584},
    {-1, 0x00CE, 278},
    {-1, 0x0148, 611},
    {-1, 0x0163, 333},
    {-1, 0x00AC, 584},
    {-1, 0x00F6, 611},
    {-1, 0x00FC, 611},
    {-1, 0x2260, 549},
    {-1, 0x0123, 611},
    {-1, 0x00F0, 611},
    {-1, 0x017E, 500},
    {-1, 0x0146, 611},
    {-1, 0x00B9, 333},
    {-1, 0x012B, 278},
    {-1, 0x20AC, 556},
    {-1, 0xFFFF, 0}
    };

static const HPDF_CharData CHAR_DATA_HELVETICA_OBLIQUE[316] = {
    {32, 0x0020, 278},
    {33, 0x0021, 278},
    {34, 0x0022, 355},
    {35, 0x0023, 556},
    {36, 0x0024, 556},
    {37, 0x0025, 889},
    {38, 0x0026, 667},
    {39, 0x2019, 222},
    {40, 0x0028, 333},
    {41, 0x0029, 333},
    {42, 0x002A, 389},
    {43, 0x002B, 584},
    {44, 0x002C, 278},
    {45, 0x002D, 333},
    {46, 0x002E, 278},
    {47, 0x002F, 278},
    {48, 0x0030, 556},
    {49, 0x0031, 556},
    {50, 0x0032, 556},
    {51, 0x0033, 556},
    {52, 0x0034, 556},
    {53, 0x0035, 556},
    {54, 0x0036, 556},
    {55, 0x0037, 556},
    {56, 0x0038, 556},
    {57, 0x0039, 556},
    {58, 0x003A, 278},
    {59, 0x003B, 278},
    {60, 0x003C, 584},
    {61, 0x003D, 584},
    {62, 0x003E, 584},
    {63, 0x003F, 556},
    {64, 0x0040, 1015},
    {65, 0x0041, 667},
    {66, 0x0042, 667},
    {67, 0x0043, 722},
    {68, 0x0044, 722},
    {69, 0x0045, 667},
    {70, 0x0046, 611},
    {71, 0x0047, 778},
    {72, 0x0048, 722},
    {73, 0x0049, 278},
    {74, 0x004A, 500},
    {75, 0x004B, 667},
    {76, 0x004C, 556},
    {77, 0x004D, 833},
    {78, 0x004E, 722},
    {79, 0x004F, 778},
    {80, 0x0050, 667},
    {81, 0x0051, 778},
    {82, 0x0052, 722},
    {83, 0x0053, 667},
    {84, 0x0054, 611},
    {85, 0x0055, 722},
    {86, 0x0056, 667},
    {87, 0x0057, 944},
    {88, 0x0058, 667},
    {89, 0x0059, 667},
    {90, 0x005A, 611},
    {91, 0x005B, 278},
    {92, 0x005C, 278},
    {93, 0x005D, 278},
    {94, 0x005E, 469},
    {95, 0x005F, 556},
    {96, 0x2018, 222},
    {97, 0x0061, 556},
    {98, 0x0062, 556},
    {99, 0x0063, 500},
    {100, 0x0064, 556},
    {101, 0x0065, 556},
    {102, 0x0066, 278},
    {103, 0x0067, 556},
    {104, 0x0068, 556},
    {105, 0x0069, 222},
    {106, 0x006A, 222},
    {107, 0x006B, 500},
    {108, 0x006C, 222},
    {109, 0x006D, 833},
    {110, 0x006E, 556},
    {111, 0x006F, 556},
    {112, 0x0070, 556},
    {113, 0x0071, 556},
    {114, 0x0072, 333},
    {115, 0x0073, 500},
    {116, 0x0074, 278},
    {117, 0x0075, 556},
    {118, 0x0076, 500},
    {119, 0x0077, 722},
    {120, 0x0078, 500},
    {121, 0x0079, 500},
    {122, 0x007A, 500},
    {123, 0x007B, 334},
    {124, 0x007C, 260},
    {125, 0x007D, 334},
    {126, 0x007E, 584},
    {161, 0x00A1, 333},
    {162, 0x00A2, 556},
    {163, 0x00A3, 556},
    {164, 0x2044, 167},
    {165, 0x00A5, 556},
    {166, 0x0192, 556},
    {167, 0x00A7, 556},
    {168, 0x00A4, 556},
    {169, 0x0027, 191},
    {170, 0x201C, 333},
    {171, 0x00AB, 556},
    {172, 0x2039, 333},
    {173, 0x203A, 333},
    {174, 0xFB01, 500},
    {175, 0xFB02, 500},
    {177, 0x2013, 556},
    {178, 0x2020, 556},
    {179, 0x2021, 556},
    {180, 0x00B7, 278},
    {182, 0x00B6, 537},
    {183, 0x2022, 350},
    {184, 0x201A, 222},
    {185, 0x201E, 333},
    {186, 0x201D, 333},
    {187, 0x00BB, 556},
    {188, 0x2026, 1000},
    {189, 0x2030, 1000},
    {191, 0x00BF, 611},
    {193, 0x0060, 333},
    {194, 0x00B4, 333},
    {195, 0x02C6, 333},
    {196, 0x02DC, 333},
    {197, 0x00AF, 333},
    {198, 0x02D8, 333},
    {199, 0x02D9, 333},
    {200, 0x00A8, 333},
    {202, 0x02DA, 333},
    {203, 0x00B8, 333},
    {205, 0x02DD, 333},
    {206, 0x02DB, 333},
    {207, 0x02C7, 333},
    {208, 0x2014, 1000},
    {225, 0x00C6, 1000},
    {227, 0x00AA, 370},
    {232, 0x0141, 556},
    {233, 0x00D8, 778},
    {234, 0x0152, 1000},
    {235, 0x00BA, 365},
    {241, 0x00E6, 889},
    {245, 0x0131, 278},
    {248, 0x0142, 222},
    {249, 0x00F8, 611},
    {250, 0x0153, 944},
    {251, 0x00DF, 611},
    {-1, 0x00CF, 278},
    {-1, 0x00E9, 556},
    {-1, 0x0103, 556},
    {-1, 0x0171, 556},
    {-1, 0x011B, 556},
    {-1, 0x0178, 667},
    {-1, 0x00F7, 584},
    {-1, 0x00DD, 667},
    {-1, 0x00C2, 667},
    {-1, 0x00E1, 556},
    {-1, 0x00DB, 722},
    {-1, 0x00FD, 500},
    {-1, 0x0219, 500},
    {-1, 0x00EA, 556},
    {-1, 0x016E, 722},
    {-1, 0x00DC, 722},
    {-1, 0x0105, 556},
    {-1, 0x00DA, 722},
    {-1, 0x0173, 556},
    {-1, 0x00CB, 667},
    {-1, 0x0110, 722},
    {-1, 0xF6C3, 250},
    {-1, 0x00A9, 737},
    {-1, 0x0112, 667},
    {-1, 0x010D, 500},
    {-1, 0x00E5, 556},
    {-1, 0x0145, 722},
    {-1, 0x013A, 222},
    {-1, 0x00E0, 556},
    {-1, 0x0162, 611},
    {-1, 0x0106, 722},
    {-1, 0x00E3, 556},
    {-1, 0x0116, 667},
    {-1, 0x0161, 500},
    {-1, 0x015F, 500},
    {-1, 0x00ED, 278},
    {-1, 0x25CA, 471},
    {-1, 0x0158, 722},
    {-1, 0x0122, 778},
    {-1, 0x00FB, 556},
    {-1, 0x00E2, 556},
    {-1, 0x0100, 667},
    {-1, 0x0159, 333},
    {-1, 0x00E7, 500},
    {-1, 0x017B, 611},
    {-1, 0x00DE, 667},
    {-1, 0x014C, 778},
    {-1, 0x0154, 722},
    {-1, 0x015A, 667},
    {-1, 0x010F, 643},
    {-1, 0x016A, 722},
    {-1, 0x016F, 556},
    {-1, 0x00B3, 333},
    {-1, 0x00D2, 778},
    {-1, 0x00C0, 667},
    {-1, 0x0102, 667},
    {-1, 0x00D7, 584},
    {-1, 0x00FA, 556},
    {-1, 0x0164, 611},
    {-1, 0x2202, 476},
    {-1, 0x00FF, 500},
    {-1, 0x0143, 722},
    {-1, 0x00EE, 278},
    {-1, 0x00CA, 667},
    {-1, 0x00E4, 556},
    {-1, 0x00EB, 556},
    {-1, 0x0107, 500},
    {-1, 0x0144, 556},
    {-1, 0x016B, 556},
    {-1, 0x0147, 722},
    {-1, 0x00CD, 278},
    {-1, 0x00B1, 584},
    {-1, 0x00A6, 260},
    {-1, 0x00AE, 737},
    {-1, 0x011E, 778},
    {-1, 0x0130, 278},
    {-1, 0x2211, 600},
    {-1, 0x00C8, 667},
    {-1, 0x0155, 333},
    {-1, 0x014D, 556},
    {-1, 0x0179, 611},
    {-1, 0x017D, 611},
    {-1, 0x2265, 549},
    {-1, 0x00D0, 722},
    {-1, 0x00C7, 722},
    {-1, 0x013C, 222},
    {-1, 0x0165, 316},
    {-1, 0x0119, 556},
    {-1, 0x0172, 722},
    {-1, 0x00C1, 667},
    {-1, 0x00C4, 667},
    {-1, 0x00E8, 556},
    {-1, 0x017A, 500},
    {-1, 0x012F, 222},
    {-1, 0x00D3, 778},
    {-1, 0x00F3, 556},
    {-1, 0x0101, 556},
    {-1, 0x015B, 500},
    {-1, 0x00EF, 278},
    {-1, 0x00D4, 778},
    {-1, 0x00D9, 722},
    {-1, 0x0394, 612},
    {-1, 0x00FE, 556},
    {-1, 0x00B2, 333},
    {-1, 0x00D6, 778},
    {-1, 0x00B5, 556},
    {-1, 0x00EC, 278},
    {-1, 0x0151, 556},
    {-1, 0x0118, 667},
    {-1, 0x0111, 556},
    {-1, 0x00BE, 834},
    {-1, 0x015E, 667},
    {-1, 0x013E, 299},
    {-1, 0x0136, 667},
    {-1, 0x0139, 556},
    {-1, 0x2122, 1000},
    {-1, 0x0117, 556},
    {-1, 0x00CC, 278},
    {-1, 0x012A, 278},
    {-1, 0x013D, 556},
    {-1, 0x00BD, 834},
    {-1, 0x2264, 549},
    {-1, 0x00F4, 556},
    {-1, 0x00F1, 556},
    {-1, 0x0170, 722},
    {-1, 0x00C9, 667},
    {-1, 0x0113, 556},
    {-1, 0x011F, 556},
    {-1, 0x00BC, 834},
    {-1, 0x0160, 667},
    {-1, 0x0218, 667},
    {-1, 0x0150, 778},
    {-1, 0x00B0, 400},
    {-1, 0x00F2, 556},
    {-1, 0x010C, 722},
    {-1, 0x00F9, 556},
    {-1, 0x221A, 453},
    {-1, 0x010E, 722},
    {-1, 0x0157, 333},
    {-1, 0x00D1, 722},
    {-1, 0x00F5, 556},
    {-1, 0x0156, 722},
    {-1, 0x013B, 556},
    {-1, 0x00C3, 667},
    {-1, 0x0104, 667},
    {-1, 0x00C5, 667},
    {-1, 0x00D5, 778},
    {-1, 0x017C, 500},
    {-1, 0x011A, 667},
    {-1, 0x012E, 278},
    {-1, 0x0137, 500},
    {-1, 0x2212, 584},
    {-1, 0x00CE, 278},
    {-1, 0x0148, 556},
    {-1, 0x0163, 278},
    {-1, 0x00AC, 584},
    {-1, 0x00F6, 556},
    {-1, 0x00FC, 556},
    {-1, 0x2260, 549},
    {-1, 0x0123, 556},
    {-1, 0x00F0, 556},
    {-1, 0x017E, 500},
    {-1, 0x0146, 556},
    {-1, 0x00B9, 333},
    {-1, 0x012B, 278},
    {-1, 0x20AC, 556},
    {-1, 0xFFFF, 0}
    };

static const HPDF_CharData CHAR_DATA_TIMES_ROMAN[316] = {
    {32, 0x0020, 250},
    {33, 0x0021, 333},
    {34, 0x0022, 408},
    {35, 0x0023, 500},
    {36, 0x0024, 500},
    {37, 0x0025, 833},
    {38, 0x0026, 778},
    {39, 0x2019, 333},
    {40, 0x0028, 333},
    {41, 0x0029, 333},
    {42, 0x002A, 500},
    {43, 0x002B, 564},
    {44, 0x002C, 250},
    {45, 0x002D, 333},
    {46, 0x002E, 250},
    {47, 0x002F, 278},
    {48, 0x0030, 500},
    {49, 0x0031, 500},
    {50, 0x0032, 500},
    {51, 0x0033, 500},
    {52, 0x0034, 500},
    {53, 0x0035, 500},
    {54, 0x0036, 500},
    {55, 0x0037, 500},
    {56, 0x0038, 500},
    {57, 0x0039, 500},
    {58, 0x003A, 278},
    {59, 0x003B, 278},
    {60, 0x003C, 564},
    {61, 0x003D, 564},
    {62, 0x003E, 564},
    {63, 0x003F, 444},
    {64, 0x0040, 921},
    {65, 0x0041, 722},
    {66, 0x0042, 667},
    {67, 0x0043, 667},
    {68, 0x0044, 722},
    {69, 0x0045, 611},
    {70, 0x0046, 556},
    {71, 0x0047, 722},
    {72, 0x0048, 722},
    {73, 0x0049, 333},
    {74, 0x004A, 389},
    {75, 0x004B, 722},
    {76, 0x004C, 611},
    {77, 0x004D, 889},
    {78, 0x004E, 722},
    {79, 0x004F, 722},
    {80, 0x0050, 556},
    {81, 0x0051, 722},
    {82, 0x0052, 667},
    {83, 0x0053, 556},
    {84, 0x0054, 611},
    {85, 0x0055, 722},
    {86, 0x0056, 722},
    {87, 0x0057, 944},
    {88, 0x0058, 722},
    {89, 0x0059, 722},
    {90, 0x005A, 611},
    {91, 0x005B, 333},
    {92, 0x005C, 278},
    {93, 0x005D, 333},
    {94, 0x005E, 469},
    {95, 0x005F, 500},
    {96, 0x2018, 333},
    {97, 0x0061, 444},
    {98, 0x0062, 500},
    {99, 0x0063, 444},
    {100, 0x0064, 500},
    {101, 0x0065, 444},
    {102, 0x0066, 333},
    {103, 0x0067, 500},
    {104, 0x0068, 500},
    {105, 0x0069, 278},
    {106, 0x006A, 278},
    {107, 0x006B, 500},
    {108, 0x006C, 278},
    {109, 0x006D, 778},
    {110, 0x006E, 500},
    {111, 0x006F, 500},
    {112, 0x0070, 500},
    {113, 0x0071, 500},
    {114, 0x0072, 333},
    {115, 0x0073, 389},
    {116, 0x0074, 278},
    {117, 0x0075, 500},
    {118, 0x0076, 500},
    {119, 0x0077, 722},
    {120, 0x0078, 500},
    {121, 0x0079, 500},
    {122, 0x007A, 444},
    {123, 0x007B, 480},
    {124, 0x007C, 200},
    {125, 0x007D, 480},
    {126, 0x007E, 541},
    {161, 0x00A1, 333},
    {162, 0x00A2, 500},
    {163, 0x00A3, 500},
    {164, 0x2044, 167},
    {165, 0x00A5, 500},
    {166, 0x0192, 500},
    {167, 0x00A7, 500},
    {168, 0x00A4, 500},
    {169, 0x0027, 180},
    {170, 0x201C, 444},
    {171, 0x00AB, 500},
    {172, 0x2039, 333},
    {173, 0x203A, 333},
    {174, 0xFB01, 556},
    {175, 0xFB02, 556},
    {177, 0x2013, 500},
    {178, 0x2020, 500},
    {179, 0x2021, 500},
    {180, 0x00B7, 250},
    {182, 0x00B6, 453},
    {183, 0x2022, 350},
    {184, 0x201A, 333},
    {185, 0x201E, 444},
    {186, 0x201D, 444},
    {187, 0x00BB, 500},
    {188, 0x2026, 1000},
    {189, 0x2030, 1000},
    {191, 0x00BF, 444},
    {193, 0x0060, 333},
    {194, 0x00B4, 333},
    {195, 0x02C6, 333},
    {196, 0x02DC, 333},
    {197, 0x00AF, 333},
    {198, 0x02D8, 333},
    {199, 0x02D9, 333},
    {200, 0x00A8, 333},
    {202, 0x02DA, 333},
    {203, 0x00B8, 333},
    {205, 0x02DD, 333},
    {206, 0x02DB, 333},
    {207, 0x02C7, 333},
    {208, 0x2014, 1000},
    {225, 0x00C6, 889},
    {227, 0x00AA, 276},
    {232, 0x0141, 611},
    {233, 0x00D8, 722},
    {234, 0x0152, 889},
    {235, 0x00BA, 310},
    {241, 0x00E6, 667},
    {245, 0x0131, 278},
    {248, 0x0142, 278},
    {249, 0x00F8, 500},
    {250, 0x0153, 722},
    {251, 0x00DF, 500},
    {-1, 0x00CF, 333},
    {-1, 0x00E9, 444},
    {-1, 0x0103, 444},
    {-1, 0x0171, 500},
    {-1, 0x011B, 444},
    {-1, 0x0178, 722},
    {-1, 0x00F7, 564},
    {-1, 0x00DD, 722},
    {-1, 0x00C2, 722},
    {-1, 0x00E1, 444},
    {-1, 0x00DB, 722},
    {-1, 0x00FD, 500},
    {-1, 0x0219, 389},
    {-1, 0x00EA, 444},
    {-1, 0x016E, 722},
    {-1, 0x00DC, 722},
    {-1, 0x0105, 444},
    {-1, 0x00DA, 722},
    {-1, 0x0173, 500},
    {-1, 0x00CB, 611},
    {-1, 0x0110, 722},
    {-1, 0xF6C3, 250},
    {-1, 0x00A9, 760},
    {-1, 0x0112, 611},
    {-1, 0x010D, 444},
    {-1, 0x00E5, 444},
    {-1, 0x0145, 722},
    {-1, 0x013A, 278},
    {-1, 0x00E0, 444},
    {-1, 0x0162, 611},
    {-1, 0x0106, 667},
    {-1, 0x00E3, 444},
    {-1, 0x0116, 611},
    {-1, 0x0161, 389},
    {-1, 0x015F, 389},
    {-1, 0x00ED, 278},
    {-1, 0x25CA, 471},
    {-1, 0x0158, 667},
    {-1, 0x0122, 722},
    {-1, 0x00FB, 500},
    {-1, 0x00E2, 444},
    {-1, 0x0100, 722},
    {-1, 0x0159, 333},
    {-1, 0x00E7, 444},
    {-1, 0x017B, 611},
    {-1, 0x00DE, 556},
    {-1, 0x014C, 722},
    {-1, 0x0154, 667},
    {-1, 0x015A, 556},
    {-1, 0x010F, 588},
    {-1, 0x016A, 722},
    {-1, 0x016F, 500},
    {-1, 0x00B3, 300},
    {-1, 0x00D2, 722},
    {-1, 0x00C0, 722},
    {-1, 0x0102, 722},
    {-1, 0x00D7, 564},
    {-1, 0x00FA, 500},
    {-1, 0x0164, 611},
    {-1, 0x2202, 476},
    {-1, 0x00FF, 500},
    {-1, 0x0143, 722},
    {-1, 0x00EE, 278},
    {-1, 0x00CA, 611},
    {-1, 0x00E4, 444},
    {-1, 0x00EB, 444},
    {-1, 0x0107, 444},
    {-1, 0x0144, 500},
    {-1, 0x016B, 500},
    {-1, 0x0147, 722},
    {-1, 0x00CD, 333},
    {-1, 0x00B1, 564},
    {-1, 0x00A6, 200},
    {-1, 0x00AE, 760},
    {-1, 0x011E, 722},
    {-1, 0x0130, 333},
    {-1, 0x2211, 600},
    {-1, 0x00C8, 611},
    {-1, 0x0155, 333},
    {-1, 0x014D, 500},
    {-1, 0x0179, 611},
    {-1, 0x017D, 611},
    {-1, 0x2265, 549},
    {-1, 0x00D0, 722},
    {-1, 0x00C7, 667},
    {-1, 0x013C, 278},
    {-1, 0x0165, 326},
    {-1, 0x0119, 444},
    {-1, 0x0172, 722},
    {-1, 0x00C1, 722},
    {-1, 0x00C4, 722},
    {-1, 0x00E8, 444},
    {-1, 0x017A, 444},
    {-1, 0x012F, 278},
    {-1, 0x00D3, 722},
    {-1, 0x00F3, 500},
    {-1, 0x0101, 444},
    {-1, 0x015B, 389},
    {-1, 0x00EF, 278},
    {-1, 0x00D4, 722},
    {-1, 0x00D9, 722},
    {-1, 0x0394, 612},
    {-1, 0x00FE, 500},
    {-1, 0x00B2, 300},
    {-1, 0x00D6, 722},
    {-1, 0x00B5, 500},
    {-1, 0x00EC, 278},
    {-1, 0x0151, 500},
    {-1, 0x0118, 611},
    {-1, 0x0111, 500},
    {-1, 0x00BE, 750},
    {-1, 0x015E, 556},
    {-1, 0x013E, 344},
    {-1, 0x0136, 722},
    {-1, 0x0139, 611},
    {-1, 0x2122, 980},
    {-1, 0x0117, 444},
    {-1, 0x00CC, 333},
    {-1, 0x012A, 333},
    {-1, 0x013D, 611},
    {-1, 0x00BD, 750},
    {-1, 0x2264, 549},
    {-1, 0x00F4, 500},
    {-1, 0x00F1, 500},
    {-1, 0x0170, 722},
    {-1, 0x00C9, 611},
    {-1, 0x0113, 444},
    {-1, 0x011F, 500},
    {-1, 0x00BC, 750},
    {-1, 0x0160, 556},
    {-1, 0x0218, 556},
    {-1, 0x0150, 722},
    {-1, 0x00B0, 400},
    {-1, 0x00F2, 500},
    {-1, 0x010C, 667},
    {-1, 0x00F9, 500},
    {-1, 0x221A, 453},
    {-1, 0x010E, 722},
    {-1, 0x0157, 333},
    {-1, 0x00D1, 722},
    {-1, 0x00F5, 500},
    {-1, 0x0156, 667},
    {-1, 0x013B, 611},
    {-1, 0x00C3, 722},
    {-1, 0x0104, 722},
    {-1, 0x00C5, 722},
    {-1, 0x00D5, 722},
    {-1, 0x017C, 444},
    {-1, 0x011A, 611},
    {-1, 0x012E, 333},
    {-1, 0x0137, 500},
    {-1, 0x2212, 564},
    {-1, 0x00CE, 333},
    {-1, 0x0148, 500},
    {-1, 0x0163, 278},
    {-1, 0x00AC, 564},
    {-1, 0x00F6, 500},
    {-1, 0x00FC, 500},
    {-1, 0x2260, 549},
    {-1, 0x0123, 500},
    {-1, 0x00F0, 500},
    {-1, 0x017E, 444},
    {-1, 0x0146, 500},
    {-1, 0x00B9, 300},
    {-1, 0x012B, 278},
    {-1, 0x20AC, 500},
    {-1, 0xFFFF, 0}
    };

static const HPDF_CharData CHAR_DATA_TIMES_BOLD[316] = {
    {32, 0x0020, 250},
    {33, 0x0021, 333},
    {34, 0x0022, 555},
    {35, 0x0023, 500},
    {36, 0x0024, 500},
    {37, 0x0025, 1000},
    {38, 0x0026, 833},
    {39, 0x2019, 333},
    {40, 0x0028, 333},
    {41, 0x0029, 333},
    {42, 0x002A, 500},
    {43, 0x002B, 570},
    {44, 0x002C, 250},
    {45, 0x002D, 333},
    {46, 0x002E, 250},
    {47, 0x002F, 278},
    {48, 0x0030, 500},
    {49, 0x0031, 500},
    {50, 0x0032, 500},
    {51, 0x0033, 500},
    {52, 0x0034, 500},
    {53, 0x0035, 500},
    {54, 0x0036, 500},
    {55, 0x0037, 500},
    {56, 0x0038, 500},
    {57, 0x0039, 500},
    {58, 0x003A, 333},
    {59, 0x003B, 333},
    {60, 0x003C, 570},
    {61, 0x003D, 570},
    {62, 0x003E, 570},
    {63, 0x003F, 500},
    {64, 0x0040, 930},
    {65, 0x0041, 722},
    {66, 0x0042, 667},
    {67, 0x0043, 722},
    {68, 0x0044, 722},
    {69, 0x0045, 667},
    {70, 0x0046, 611},
    {71, 0x0047, 778},
    {72, 0x0048, 778},
    {73, 0x0049, 389},
    {74, 0x004A, 500},
    {75, 0x004B, 778},
    {76, 0x004C, 667},
    {77, 0x004D, 944},
    {78, 0x004E, 722},
    {79, 0x004F, 778},
    {80, 0x0050, 611},
    {81, 0x0051, 778},
    {82, 0x0052, 722},
    {83, 0x0053, 556},
    {84, 0x0054, 667},
    {85, 0x0055, 722},
    {86, 0x0056, 722},
    {87, 0x0057, 1000},
    {88, 0x0058, 722},
    {89, 0x0059, 722},
    {90, 0x005A, 667},
    {91, 0x005B, 333},
    {92, 0x005C, 278},
    {93, 0x005D, 333},
    {94, 0x005E, 581},
    {95, 0x005F, 500},
    {96, 0x2018, 333},
    {97, 0x0061, 500},
    {98, 0x0062, 556},
    {99, 0x0063, 444},
    {100, 0x0064, 556},
    {101, 0x0065, 444},
    {102, 0x0066, 333},
    {103, 0x0067, 500},
    {104, 0x0068, 556},
    {105, 0x0069, 278},
    {106, 0x006A, 333},
    {107, 0x006B, 556},
    {108, 0x006C, 278},
    {109, 0x006D, 833},
    {110, 0x006E, 556},
    {111, 0x006F, 500},
    {112, 0x0070, 556},
    {113, 0x0071, 556},
    {114, 0x0072, 444},
    {115, 0x0073, 389},
    {116, 0x0074, 333},
    {117, 0x0075, 556},
    {118, 0x0076, 500},
    {119, 0x0077, 722},
    {120, 0x0078, 500},
    {121, 0x0079, 500},
    {122, 0x007A, 444},
    {123, 0x007B, 394},
    {124, 0x007C, 220},
    {125, 0x007D, 394},
    {126, 0x007E, 520},
    {161, 0x00A1, 333},
    {162, 0x00A2, 500},
    {163, 0x00A3, 500},
    {164, 0x2044, 167},
    {165, 0x00A5, 500},
    {166, 0x0192, 500},
    {167, 0x00A7, 500},
    {168, 0x00A4, 500},
    {169, 0x0027, 278},
    {170, 0x201C, 500},
    {171, 0x00AB, 500},
    {172, 0x2039, 333},
    {173, 0x203A, 333},
    {174, 0xFB01, 556},
    {175, 0xFB02, 556},
    {177, 0x2013, 500},
    {178, 0x2020, 500},
    {179, 0x2021, 500},
    {180, 0x00B7, 250},
    {182, 0x00B6, 540},
    {183, 0x2022, 350},
    {184, 0x201A, 333},
    {185, 0x201E, 500},
    {186, 0x201D, 500},
    {187, 0x00BB, 500},
    {188, 0x2026, 1000},
    {189, 0x2030, 1000},
    {191, 0x00BF, 500},
    {193, 0x0060, 333},
    {194, 0x00B4, 333},
    {195, 0x02C6, 333},
    {196, 0x02DC, 333},
    {197, 0x00AF, 333},
    {198, 0x02D8, 333},
    {199, 0x02D9, 333},
    {200, 0x00A8, 333},
    {202, 0x02DA, 333},
    {203, 0x00B8, 333},
    {205, 0x02DD, 333},
    {206, 0x02DB, 333},
    {207, 0x02C7, 333},
    {208, 0x2014, 1000},
    {225, 0x00C6, 1000},
    {227, 0x00AA, 300},
    {232, 0x0141, 667},
    {233, 0x00D8, 778},
    {234, 0x0152, 1000},
    {235, 0x00BA, 330},
    {241, 0x00E6, 722},
    {245, 0x0131, 278},
    {248, 0x0142, 278},
    {249, 0x00F8, 500},
    {250, 0x0153, 722},
    {251, 0x00DF, 556},
    {-1, 0x00CF, 389},
    {-1, 0x00E9, 444},
    {-1, 0x0103, 500},
    {-1, 0x0171, 556},
    {-1, 0x011B, 444},
    {-1, 0x0178, 722},
    {-1, 0x00F7, 570},
    {-1, 0x00DD, 722},
    {-1, 0x00C2, 722},
    {-1, 0x00E1, 500},
    {-1, 0x00DB, 722},
    {-1, 0x00FD, 500},
    {-1, 0x0219, 389},
    {-1, 0x00EA, 444},
    {-1, 0x016E, 722},
    {-1, 0x00DC, 722},
    {-1, 0x0105, 500},
    {-1, 0x00DA, 722},
    {-1, 0x0173, 556},
    {-1, 0x00CB, 667},
    {-1, 0x0110, 722},
    {-1, 0xF6C3, 250},
    {-1, 0x00A9, 747},
    {-1, 0x0112, 667},
    {-1, 0x010D, 444},
    {-1, 0x00E5, 500},
    {-1, 0x0145, 722},
    {-1, 0x013A, 278},
    {-1, 0x00E0, 500},
    {-1, 0x0162, 667},
    {-1, 0x0106, 722},
    {-1, 0x00E3, 500},
    {-1, 0x0116, 667},
    {-1, 0x0161, 389},
    {-1, 0x015F, 389},
    {-1, 0x00ED, 278},
    {-1, 0x25CA, 494},
    {-1, 0x0158, 722},
    {-1, 0x0122, 778},
    {-1, 0x00FB, 556},
    {-1, 0x00E2, 500},
    {-1, 0x0100, 722},
    {-1, 0x0159, 444},
    {-1, 0x00E7, 444},
    {-1, 0x017B, 667},
    {-1, 0x00DE, 611},
    {-1, 0x014C, 778},
    {-1, 0x0154, 722},
    {-1, 0x015A, 556},
    {-1, 0x010F, 672},
    {-1, 0x016A, 722},
    {-1, 0x016F, 556},
    {-1, 0x00B3, 300},
    {-1, 0x00D2, 778},
    {-1, 0x00C0, 722},
    {-1, 0x0102, 722},
    {-1, 0x00D7, 570},
    {-1, 0x00FA, 556},
    {-1, 0x0164, 667},
    {-1, 0x2202, 494},
    {-1, 0x00FF, 500},
    {-1, 0x0143, 722},
    {-1, 0x00EE, 278},
    {-1, 0x00CA, 667},
    {-1, 0x00E4, 500},
    {-1, 0x00EB, 444},
    {-1, 0x0107, 444},
    {-1, 0x0144, 556},
    {-1, 0x016B, 556},
    {-1, 0x0147, 722},
    {-1, 0x00CD, 389},
    {-1, 0x00B1, 570},
    {-1, 0x00A6, 220},
    {-1, 0x00AE, 747},
    {-1, 0x011E, 778},
    {-1, 0x0130, 389},
    {-1, 0x2211, 600},
    {-1, 0x00C8, 667},
    {-1, 0x0155, 444},
    {-1, 0x014D, 500},
    {-1, 0x0179, 667},
    {-1, 0x017D, 667},
    {-1, 0x2265, 549},
    {-1, 0x00D0, 722},
    {-1, 0x00C7, 722},
    {-1, 0x013C, 278},
    {-1, 0x0165, 416},
    {-1, 0x0119, 444},
    {-1, 0x0172, 722},
    {-1, 0x00C1, 722},
    {-1, 0x00C4, 722},
    {-1, 0x00E8, 444},
    {-1, 0x017A, 444},
    {-1, 0x012F, 278},
    {-1, 0x00D3, 778},
    {-1, 0x00F3, 500},
    {-1, 0x0101, 500},
    {-1, 0x015B, 389},
    {-1, 0x00EF, 278},
    {-1, 0x00D4, 778},
    {-1, 0x00D9, 722},
    {-1, 0x0394, 612},
    {-1, 0x00FE, 556},
    {-1, 0x00B2, 300},
    {-1, 0x00D6, 778},
    {-1, 0x00B5, 556},
    {-1, 0x00EC, 278},
    {-1, 0x0151, 500},
    {-1, 0x0118, 667},
    {-1, 0x0111, 556},
    {-1, 0x00BE, 750},
    {-1, 0x015E, 556},
    {-1, 0x013E, 394},
    {-1, 0x0136, 778},
    {-1, 0x0139, 667},
    {-1, 0x2122, 1000},
    {-1, 0x0117, 444},
    {-1, 0x00CC, 389},
    {-1, 0x012A, 389},
    {-1, 0x013D, 667},
    {-1, 0x00BD, 750},
    {-1, 0x2264, 549},
    {-1, 0x00F4, 500},
    {-1, 0x00F1, 556},
    {-1, 0x0170, 722},
    {-1, 0x00C9, 667},
    {-1, 0x0113, 444},
    {-1, 0x011F, 500},
    {-1, 0x00BC, 750},
    {-1, 0x0160, 556},
    {-1, 0x0218, 556},
    {-1, 0x0150, 778},
    {-1, 0x00B0, 400},
    {-1, 0x00F2, 500},
    {-1, 0x010C, 722},
    {-1, 0x00F9, 556},
    {-1, 0x221A, 549},
    {-1, 0x010E, 722},
    {-1, 0x0157, 444},
    {-1, 0x00D1, 722},
    {-1, 0x00F5, 500},
    {-1, 0x0156, 722},
    {-1, 0x013B, 667},
    {-1, 0x00C3, 722},
    {-1, 0x0104, 722},
    {-1, 0x00C5, 722},
    {-1, 0x00D5, 778},
    {-1, 0x017C, 444},
    {-1, 0x011A, 667},
    {-1, 0x012E, 389},
    {-1, 0x0137, 556},
    {-1, 0x2212, 570},
    {-1, 0x00CE, 389},
    {-1, 0x0148, 556},
    {-1, 0x0163, 333},
    {-1, 0x00AC, 570},
    {-1, 0x00F6, 500},
    {-1, 0x00FC, 556},
    {-1, 0x2260, 549},
    {-1, 0x0123, 500},
    {-1, 0x00F0, 500},
    {-1, 0x017E, 444},
    {-1, 0x0146, 556},
    {-1, 0x00B9, 300},
    {-1, 0x012B, 278},
    {-1, 0x20AC, 500},
    {-1, 0xFFFF, 0}
    };

static const HPDF_CharData CHAR_DATA_TIMES_BOLD_ITALIC[316] = {
    {32, 0x0020, 250},
    {33, 0x0021, 389},
    {34, 0x0022, 555},
    {35, 0x0023, 500},
    {36, 0x0024, 500},
    {37, 0x0025, 833},
    {38, 0x0026, 778},
    {39, 0x2019, 333},
    {40, 0x0028, 333},
    {41, 0x0029, 333},
    {42, 0x002A, 500},
    {43, 0x002B, 570},
    {44, 0x002C, 250},
    {45, 0x002D, 333},
    {46, 0x002E, 250},
    {47, 0x002F, 278},
    {48, 0x0030, 500},
    {49, 0x0031, 500},
    {50, 0x0032, 500},
    {51, 0x0033, 500},
    {52, 0x0034, 500},
    {53, 0x0035, 500},
    {54, 0x0036, 500},
    {55, 0x0037, 500},
    {56, 0x0038, 500},
    {57, 0x0039, 500},
    {58, 0x003A, 333},
    {59, 0x003B, 333},
    {60, 0x003C, 570},
    {61, 0x003D, 570},
    {62, 0x003E, 570},
    {63, 0x003F, 500},
    {64, 0x0040, 832},
    {65, 0x0041, 667},
    {66, 0x0042, 667},
    {67, 0x0043, 667},
    {68, 0x0044, 722},
    {69, 0x0045, 667},
    {70, 0x0046, 667},
    {71, 0x0047, 722},
    {72, 0x0048, 778},
    {73, 0x0049, 389},
    {74, 0x004A, 500},
    {75, 0x004B, 667},
    {76, 0x004C, 611},
    {77, 0x004D, 889},
    {78, 0x004E, 722},
    {79, 0x004F, 722},
    {80, 0x0050, 611},
    {81, 0x0051, 722},
    {82, 0x0052, 667},
    {83, 0x0053, 556},
    {84, 0x0054, 611},
    {85, 0x0055, 722},
    {86, 0x0056, 667},
    {87, 0x0057, 889},
    {88, 0x0058, 667},
    {89, 0x0059, 611},
    {90, 0x005A, 611},
    {91, 0x005B, 333},
    {92, 0x005C, 278},
    {93, 0x005D, 333},
    {94, 0x005E, 570},
    {95, 0x005F, 500},
    {96, 0x2018, 333},
    {97, 0x0061, 500},
    {98, 0x0062, 500},
    {99, 0x0063, 444},
    {100, 0x0064, 500},
    {101, 0x0065, 444},
    {102, 0x0066, 333},
    {103, 0x0067, 500},
    {104, 0x0068, 556},
    {105, 0x0069, 278},
    {106, 0x006A, 278},
    {107, 0x006B, 500},
    {108, 0x006C, 278},
    {109, 0x006D, 778},
    {110, 0x006E, 556},
    {111, 0x006F, 500},
    {112, 0x0070, 500},
    {113, 0x0071, 500},
    {114, 0x0072, 389},
    {115, 0x0073, 389},
    {116, 0x0074, 278},
    {117, 0x0075, 556},
    {118, 0x0076, 444},
    {119, 0x0077, 667},
    {120, 0x0078, 500},
    {121, 0x0079, 444},
    {122, 0x007A, 389},
    {123, 0x007B, 348},
    {124, 0x007C, 220},
    {125, 0x007D, 348},
    {126, 0x007E, 570},
    {161, 0x00A1, 389},
    {162, 0x00A2, 500},
    {163, 0x00A3, 500},
    {164, 0x2044, 167},
    {165, 0x00A5, 500},
    {166, 0x0192, 500},
    {167, 0x00A7, 500},
    {168, 0x00A4, 500},
    {169, 0x0027, 278},
    {170, 0x201C, 500},
    {171, 0x00AB, 500},
    {172, 0x2039, 333},
    {173, 0x203A, 333},
    {174, 0xFB01, 556},
    {175, 0xFB02, 556},
    {177, 0x2013, 500},
    {178, 0x2020, 500},
    {179, 0x2021, 500},
    {180, 0x00B7, 250},
    {182, 0x00B6, 500},
    {183, 0x2022, 350},
    {184, 0x201A, 333},
    {185, 0x201E, 500},
    {186, 0x201D, 500},
    {187, 0x00BB, 500},
    {188, 0x2026, 1000},
    {189, 0x2030, 1000},
    {191, 0x00BF, 500},
    {193, 0x0060, 333},
    {194, 0x00B4, 333},
    {195, 0x02C6, 333},
    {196, 0x02DC, 333},
    {197, 0x00AF, 333},
    {198, 0x02D8, 333},
    {199, 0x02D9, 333},
    {200, 0x00A8, 333},
    {202, 0x02DA, 333},
    {203, 0x00B8, 333},
    {205, 0x02DD, 333},
    {206, 0x02DB, 333},
    {207, 0x02C7, 333},
    {208, 0x2014, 1000},
    {225, 0x00C6, 944},
    {227, 0x00AA, 266},
    {232, 0x0141, 611},
    {233, 0x00D8, 722},
    {234, 0x0152, 944},
    {235, 0x00BA, 300},
    {241, 0x00E6, 722},
    {245, 0x0131, 278},
    {248, 0x0142, 278},
    {249, 0x00F8, 500},
    {250, 0x0153, 722},
    {251, 0x00DF, 500},
    {-1, 0x00CF, 389},
    {-1, 0x00E9, 444},
    {-1, 0x0103, 500},
    {-1, 0x0171, 556},
    {-1, 0x011B, 444},
    {-1, 0x0178, 611},
    {-1, 0x00F7, 570},
    {-1, 0x00DD, 611},
    {-1, 0x00C2, 667},
    {-1, 0x00E1, 500},
    {-1, 0x00DB, 722},
    {-1, 0x00FD, 444},
    {-1, 0x0219, 389},
    {-1, 0x00EA, 444},
    {-1, 0x016E, 722},
    {-1, 0x00DC, 722},
    {-1, 0x0105, 500},
    {-1, 0x00DA, 722},
    {-1, 0x0173, 556},
    {-1, 0x00CB, 667},
    {-1, 0x0110, 722},
    {-1, 0xF6C3, 250},
    {-1, 0x00A9, 747},
    {-1, 0x0112, 667},
    {-1, 0x010D, 444},
    {-1, 0x00E5, 500},
    {-1, 0x0145, 722},
    {-1, 0x013A, 278},
    {-1, 0x00E0, 500},
    {-1, 0x0162, 611},
    {-1, 0x0106, 667},
    {-1, 0x00E3, 500},
    {-1, 0x0116, 667},
    {-1, 0x0161, 389},
    {-1, 0x015F, 389},
    {-1, 0x00ED, 278},
    {-1, 0x25CA, 494},
    {-1, 0x0158, 667},
    {-1, 0x0122, 722},
    {-1, 0x00FB, 556},
    {-1, 0x00E2, 500},
    {-1, 0x0100, 667},
    {-1, 0x0159, 389},
    {-1, 0x00E7, 444},
    {-1, 0x017B, 611},
    {-1, 0x00DE, 611},
    {-1, 0x014C, 722},
    {-1, 0x0154, 667},
    {-1, 0x015A, 556},
    {-1, 0x010F, 608},
    {-1, 0x016A, 722},
    {-1, 0x016F, 556},
    {-1, 0x00B3, 300},
    {-1, 0x00D2, 722},
    {-1, 0x00C0, 667},
    {-1, 0x0102, 667},
    {-1, 0x00D7, 570},
    {-1, 0x00FA, 556},
    {-1, 0x0164, 611},
    {-1, 0x2202, 494},
    {-1, 0x00FF, 444},
    {-1, 0x0143, 722},
    {-1, 0x00EE, 278},
    {-1, 0x00CA, 667},
    {-1, 0x00E4, 500},
    {-1, 0x00EB, 444},
    {-1, 0x0107, 444},
    {-1, 0x0144, 556},
    {-1, 0x016B, 556},
    {-1, 0x0147, 722},
    {-1, 0x00CD, 389},
    {-1, 0x00B1, 570},
    {-1, 0x00A6, 220},
    {-1, 0x00AE, 747},
    {-1, 0x011E, 722},
    {-1, 0x0130, 389},
    {-1, 0x2211, 600},
    {-1, 0x00C8, 667},
    {-1, 0x0155, 389},
    {-1, 0x014D, 500},
    {-1, 0x0179, 611},
    {-1, 0x017D, 611},
    {-1, 0x2265, 549},
    {-1, 0x00D0, 722},
    {-1, 0x00C7, 667},
    {-1, 0x013C, 278},
    {-1, 0x0165, 366},
    {-1, 0x0119, 444},
    {-1, 0x0172, 722},
    {-1, 0x00C1, 667},
    {-1, 0x00C4, 667},
    {-1, 0x00E8, 444},
    {-1, 0x017A, 389},
    {-1, 0x012F, 278},
    {-1, 0x00D3, 722},
    {-1, 0x00F3, 500},
    {-1, 0x0101, 500},
    {-1, 0x015B, 389},
    {-1, 0x00EF, 278},
    {-1, 0x00D4, 722},
    {-1, 0x00D9, 722},
    {-1, 0x0394, 612},
    {-1, 0x00FE, 500},
    {-1, 0x00B2, 300},
    {-1, 0x00D6, 722},
    {-1, 0x00B5, 576},
    {-1, 0x00EC, 278},
    {-1, 0x0151, 500},
    {-1, 0x0118, 667},
    {-1, 0x0111, 500},
    {-1, 0x00BE, 750},
    {-1, 0x015E, 556},
    {-1, 0x013E, 382},
    {-1, 0x0136, 667},
    {-1, 0x0139, 611},
    {-1, 0x2122, 1000},
    {-1, 0x0117, 444},
    {-1, 0x00CC, 389},
    {-1, 0x012A, 389},
    {-1, 0x013D, 611},
    {-1, 0x00BD, 750},
    {-1, 0x2264, 549},
    {-1, 0x00F4, 500},
    {-1, 0x00F1, 556},
    {-1, 0x0170, 722},
    {-1, 0x00C9, 667},
    {-1, 0x0113, 444},
    {-1, 0x011F, 500},
    {-1, 0x00BC, 750},
    {-1, 0x0160, 556},
    {-1, 0x0218, 556},
    {-1, 0x0150, 722},
    {-1, 0x00B0, 400},
    {-1, 0x00F2, 500},
    {-1, 0x010C, 667},
    {-1, 0x00F9, 556},
    {-1, 0x221A, 549},
    {-1, 0x010E, 722},
    {-1, 0x0157, 389},
    {-1, 0x00D1, 722},
    {-1, 0x00F5, 500},
    {-1, 0x0156, 667},
    {-1, 0x013B, 611},
    {-1, 0x00C3, 667},
    {-1, 0x0104, 667},
    {-1, 0x00C5, 667},
    {-1, 0x00D5, 722},
    {-1, 0x017C, 389},
    {-1, 0x011A, 667},
    {-1, 0x012E, 389},
    {-1, 0x0137, 500},
    {-1, 0x2212, 606},
    {-1, 0x00CE, 389},
    {-1, 0x0148, 556},
    {-1, 0x0163, 278},
    {-1, 0x00AC, 606},
    {-1, 0x00F6, 500},
    {-1, 0x00FC, 556},
    {-1, 0x2260, 549},
    {-1, 0x0123, 500},
    {-1, 0x00F0, 500},
    {-1, 0x017E, 389},
    {-1, 0x0146, 556},
    {-1, 0x00B9, 300},
    {-1, 0x012B, 278},
    {-1, 0x20AC, 500},
    {-1, 0xFFFF, 0}
    };

static const HPDF_CharData CHAR_DATA_TIMES_ITALIC[316] = {
    {32, 0x0020, 250},
    {33, 0x0021, 333},
    {34, 0x0022, 420},
    {35, 0x0023, 500},
    {36, 0x0024, 500},
    {37, 0x0025, 833},
    {38, 0x0026, 778},
    {39, 0x2019, 333},
    {40, 0x0028, 333},
    {41, 0x0029, 333},
    {42, 0x002A, 500},
    {43, 0x002B, 675},
    {44, 0x002C, 250},
    {45, 0x002D, 333},
    {46, 0x002E, 250},
    {47, 0x002F, 278},
    {48, 0x0030, 500},
    {49, 0x0031, 500},
    {50, 0x0032, 500},
    {51, 0x0033, 500},
    {52, 0x0034, 500},
    {53, 0x0035, 500},
    {54, 0x0036, 500},
    {55, 0x0037, 500},
    {56, 0x0038, 500},
    {57, 0x0039, 500},
    {58, 0x003A, 333},
    {59, 0x003B, 333},
    {60, 0x003C, 675},
    {61, 0x003D, 675},
    {62, 0x003E, 675},
    {63, 0x003F, 500},
    {64, 0x0040, 920},
    {65, 0x0041, 611},
    {66, 0x0042, 611},
    {67, 0x0043, 667},
    {68, 0x0044, 722},
    {69, 0x0045, 611},
    {70, 0x0046, 611},
    {71, 0x0047, 722},
    {72, 0x0048, 722},
    {73, 0x0049, 333},
    {74, 0x004A, 444},
    {75, 0x004B, 667},
    {76, 0x004C, 556},
    {77, 0x004D, 833},
    {78, 0x004E, 667},
    {79, 0x004F, 722},
    {80, 0x0050, 611},
    {81, 0x0051, 722},
    {82, 0x0052, 611},
    {83, 0x0053, 500},
    {84, 0x0054, 556},
    {85, 0x0055, 722},
    {86, 0x0056, 611},
    {87, 0x0057, 833},
    {88, 0x0058, 611},
    {89, 0x0059, 556},
    {90, 0x005A, 556},
    {91, 0x005B, 389},
    {92, 0x005C, 278},
    {93, 0x005D, 389},
    {94, 0x005E, 422},
    {95, 0x005F, 500},
    {96, 0x2018, 333},
    {97, 0x0061, 500},
    {98, 0x0062, 500},
    {99, 0x0063, 444},
    {100, 0x0064, 500},
    {101, 0x0065, 444},
    {102, 0x0066, 278},
    {103, 0x0067, 500},
    {104, 0x0068, 500},
    {105, 0x0069, 278},
    {106, 0x006A, 278},
    {107, 0x006B, 444},
    {108, 0x006C, 278},
    {109, 0x006D, 722},
    {110, 0x006E, 500},
    {111, 0x006F, 500},
    {112, 0x0070, 500},
    {113, 0x0071, 500},
    {114, 0x0072, 389},
    {115, 0x0073, 389},
    {116, 0x0074, 278},
    {117, 0x0075, 500},
    {118, 0x0076, 444},
    {119, 0x0077, 667},
    {120, 0x0078, 444},
    {121, 0x0079, 444},
    {122, 0x007A, 389},
    {123, 0x007B, 400},
    {124, 0x007C, 275},
    {125, 0x007D, 400},
    {126, 0x007E, 541},
    {161, 0x00A1, 389},
    {162, 0x00A2, 500},
    {163, 0x00A3, 500},
    {164, 0x2044, 167},
    {165, 0x00A5, 500},
    {166, 0x0192, 500},
    {167, 0x00A7, 500},
    {168, 0x00A4, 500},
    {169, 0x0027, 214},
    {170, 0x201C, 556},
    {171, 0x00AB, 500},
    {172, 0x2039, 333},
    {173, 0x203A, 333},
    {174, 0xFB01, 500},
    {175, 0xFB02, 500},
    {177, 0x2013, 500},
    {178, 0x2020, 500},
    {179, 0x2021, 500},
    {180, 0x00B7, 250},
    {182, 0x00B6, 523},
    {183, 0x2022, 350},
    {184, 0x201A, 333},
    {185, 0x201E, 556},
    {186, 0x201D, 556},
    {187, 0x00BB, 500},
    {188, 0x2026, 889},
    {189, 0x2030, 1000},
    {191, 0x00BF, 500},
    {193, 0x0060, 333},
    {194, 0x00B4, 333},
    {195, 0x02C6, 333},
    {196, 0x02DC, 333},
    {197, 0x00AF, 333},
    {198, 0x02D8, 333},
    {199, 0x02D9, 333},
    {200, 0x00A8, 333},
    {202, 0x02DA, 333},
    {203, 0x00B8, 333},
    {205, 0x02DD, 333},
    {206, 0x02DB, 333},
    {207, 0x02C7, 333},
    {208, 0x2014, 889},
    {225, 0x00C6, 889},
    {227, 0x00AA, 276},
    {232, 0x0141, 556},
    {233, 0x00D8, 722},
    {234, 0x0152, 944},
    {235, 0x00BA, 310},
    {241, 0x00E6, 667},
    {245, 0x0131, 278},
    {248, 0x0142, 278},
    {249, 0x00F8, 500},
    {250, 0x0153, 667},
    {251, 0x00DF, 500},
    {-1, 0x00CF, 333},
    {-1, 0x00E9, 444},
    {-1, 0x0103, 500},
    {-1, 0x0171, 500},
    {-1, 0x011B, 444},
    {-1, 0x0178, 556},
    {-1, 0x00F7, 675},
    {-1, 0x00DD, 556},
    {-1, 0x00C2, 611},
    {-1, 0x00E1, 500},
    {-1, 0x00DB, 722},
    {-1, 0x00FD, 444},
    {-1, 0x0219, 389},
    {-1, 0x00EA, 444},
    {-1, 0x016E, 722},
    {-1, 0x00DC, 722},
    {-1, 0x0105, 500},
    {-1, 0x00DA, 722},
    {-1, 0x0173, 500},
    {-1, 0x00CB, 611},
    {-1, 0x0110, 722},
    {-1, 0xF6C3, 250},
    {-1, 0x00A9, 760},
    {-1, 0x0112, 611},
    {-1, 0x010D, 444},
    {-1, 0x00E5, 500},
    {-1, 0x0145, 667},
    {-1, 0x013A, 278},
    {-1, 0x00E0, 500},
    {-1, 0x0162, 556},
    {-1, 0x0106, 667},
    {-1, 0x00E3, 500},
    {-1, 0x0116, 611},
    {-1, 0x0161, 389},
    {-1, 0x015F, 389},
    {-1, 0x00ED, 278},
    {-1, 0x25CA, 471},
    {-1, 0x0158, 611},
    {-1, 0x0122, 722},
    {-1, 0x00FB, 500},
    {-1, 0x00E2, 500},
    {-1, 0x0100, 611},
    {-1, 0x0159, 389},
    {-1, 0x00E7, 444},
    {-1, 0x017B, 556},
    {-1, 0x00DE, 611},
    {-1, 0x014C, 722},
    {-1, 0x0154, 611},
    {-1, 0x015A, 500},
    {-1, 0x010F, 544},
    {-1, 0x016A, 722},
    {-1, 0x016F, 500},
    {-1, 0x00B3, 300},
    {-1, 0x00D2, 722},
    {-1, 0x00C0, 611},
    {-1, 0x0102, 611},
    {-1, 0x00D7, 675},
    {-1, 0x00FA, 500},
    {-1, 0x0164, 556},
    {-1, 0x2202, 476},
    {-1, 0x00FF, 444},
    {-1, 0x0143, 667},
    {-1, 0x00EE, 278},
    {-1, 0x00CA, 611},
    {-1, 0x00E4, 500},
    {-1, 0x00EB, 444},
    {-1, 0x0107, 444},
    {-1, 0x0144, 500},
    {-1, 0x016B, 500},
    {-1, 0x0147, 667},
    {-1, 0x00CD, 333},
    {-1, 0x00B1, 675},
    {-1, 0x00A6, 275},
    {-1, 0x00AE, 760},
    {-1, 0x011E, 722},
    {-1, 0x0130, 333},
    {-1, 0x2211, 600},
    {-1, 0x00C8, 611},
    {-1, 0x0155, 389},
    {-1, 0x014D, 500},
    {-1, 0x0179, 556},
    {-1, 0x017D, 556},
    {-1, 0x2265, 549},
    {-1, 0x00D0, 722},
    {-1, 0x00C7, 667},
    {-1, 0x013C, 278},
    {-1, 0x0165, 300},
    {-1, 0x0119, 444},
    {-1, 0x0172, 722},
    {-1, 0x00C1, 611},
    {-1, 0x00C4, 611},
    {-1, 0x00E8, 444},
    {-1, 0x017A, 389},
    {-1, 0x012F, 278},
    {-1, 0x00D3, 722},
    {-1, 0x00F3, 500},
    {-1, 0x0101, 500},
    {-1, 0x015B, 389},
    {-1, 0x00EF, 278},
    {-1, 0x00D4, 722},
    {-1, 0x00D9, 722},
    {-1, 0x0394, 612},
    {-1, 0x00FE, 500},
    {-1, 0x00B2, 300},
    {-1, 0x00D6, 722},
    {-1, 0x00B5, 500},
    {-1, 0x00EC, 278},
    {-1, 0x0151, 500},
    {-1, 0x0118, 611},
    {-1, 0x0111, 500},
    {-1, 0x00BE, 750},
    {-1, 0x015E, 500},
    {-1, 0x013E, 300},
    {-1, 0x0136, 667},
    {-1, 0x0139, 556},
    {-1, 0x2122, 980},
    {-1, 0x0117, 444},
    {-1, 0x00CC, 333},
    {-1, 0x012A, 333},
    {-1, 0x013D, 611},
    {-1, 0x00BD, 750},
    {-1, 0x2264, 549},
    {-1, 0x00F4, 500},
    {-1, 0x00F1, 500},
    {-1, 0x0170, 722},
    {-1, 0x00C9, 611},
    {-1, 0x0113, 444},
    {-1, 0x011F, 500},
    {-1, 0x00BC, 750},
    {-1, 0x0160, 500},
    {-1, 0x0218, 500},
    {-1, 0x0150, 722},
    {-1, 0x00B0, 400},
    {-1, 0x00F2, 500},
    {-1, 0x010C, 667},
    {-1, 0x00F9, 500},
    {-1, 0x221A, 453},
    {-1, 0x010E, 722},
    {-1, 0x0157, 389},
    {-1, 0x00D1, 667},
    {-1, 0x00F5, 500},
    {-1, 0x0156, 611},
    {-1, 0x013B, 556},
    {-1, 0x00C3, 611},
    {-1, 0x0104, 611},
    {-1, 0x00C5, 611},
    {-1, 0x00D5, 722},
    {-1, 0x017C, 389},
    {-1, 0x011A, 611},
    {-1, 0x012E, 333},
    {-1, 0x0137, 444},
    {-1, 0x2212, 675},
    {-1, 0x00CE, 333},
    {-1, 0x0148, 500},
    {-1, 0x0163, 278},
    {-1, 0x00AC, 675},
    {-1, 0x00F6, 500},
    {-1, 0x00FC, 500},
    {-1, 0x2260, 549},
    {-1, 0x0123, 500},
    {-1, 0x00F0, 500},
    {-1, 0x017E, 389},
    {-1, 0x0146, 500},
    {-1, 0x00B9, 300},
    {-1, 0x012B, 278},
    {-1, 0x20AC, 500},
    {-1, 0xFFFF, 0}
    };

static const HPDF_CharData CHAR_DATA_ZAPF_DINGBATS[203] = {
    {32, 0x0020, 278},
    {33, 0x0021, 974},
    {34, 0x0022, 961},
    {35, 0x0023, 974},
    {36, 0x0024, 980},
    {37, 0x0025, 719},
    {38, 0x0026, 789},
    {39, 0x0027, 790},
    {40, 0x0028, 791},
    {41, 0x0029, 690},
    {42, 0x002A, 960},
    {43, 0x002B, 939},
    {44, 0x002C, 549},
    {45, 0x002D, 855},
    {46, 0x002E, 911},
    {47, 0x002F, 933},
    {48, 0x0030, 911},
    {49, 0x0031, 945},
    {50, 0x0032, 974},
    {51, 0x0033, 755},
    {52, 0x0034, 846},
    {53, 0x0035, 762},
    {54, 0x0036, 761},
    {55, 0x0037, 571},
    {56, 0x0038, 677},
    {57, 0x0039, 763},
    {58, 0x003A, 760},
    {59, 0x003B, 759},
    {60, 0x003C, 754},
    {61, 0x003D, 494},
    {62, 0x003E, 552},
    {63, 0x003F, 537},
    {64, 0x0040, 577},
    {65, 0x0041, 692},
    {66, 0x0042, 786},
    {67, 0x0043, 788},
    {68, 0x0044, 788},
    {69, 0x0045, 790},
    {70, 0x0046, 793},
    {71, 0x0047, 794},
    {72, 0x0048, 816},
    {73, 0x0049, 823},
    {74, 0x004A, 789},
    {75, 0x004B, 841},
    {76, 0x004C, 823},
    {77, 0x004D, 833},
    {78, 0x004E, 816},
    {79, 0x004F, 831},
    {80, 0x0050, 923},
    {81, 0x0051, 744},
    {82, 0x0052, 723},
    {83, 0x0053, 749},
    {84, 0x0054, 790},
    {85, 0x0055, 792},
    {86, 0x0056, 695},
    {87, 0x0057, 776},
    {88, 0x0058, 768},
    {89, 0x0059, 792},
    {90, 0x005A, 759},
    {91, 0x005B, 707},
    {92, 0x005C, 708},
    {93, 0x005D, 682},
    {94, 0x005E, 701},
    {95, 0x005F, 826},
    {96, 0x0060, 815},
    {97, 0x0061, 789},
    {98, 0x0062, 789},
    {99, 0x0063, 707},
    {100, 0x0064, 687},
    {101, 0x0065, 696},
    {102, 0x0066, 689},
    {103, 0x0067, 786},
    {104, 0x0068, 787},
    {105, 0x0069, 713},
    {106, 0x006A, 791},
    {107, 0x006B, 785},
    {108, 0x006C, 791},
    {109, 0x006D, 873},
    {110, 0x006E, 761},
    {111, 0x006F, 762},
    {112, 0x0070, 762},
    {113, 0x0071, 759},
    {114, 0x0072, 759},
    {115, 0x0073, 892},
    {116, 0x0074, 892},
    {117, 0x0075, 788},
    {118, 0x0076, 784},
    {119, 0x0077, 438},
    {120, 0x0078, 138},
    {121, 0x0079, 277},
    {122, 0x007A, 415},
    {123, 0x007B, 392},
    {124, 0x007C, 392},
    {125, 0x007D, 668},
    {126, 0x007E, 668},
    {128, 0x0080, 390},
    {129, 0x0081, 390},
    {130, 0x0082, 317},
    {131, 0x0083, 317},
    {132, 0x0084, 276},
    {133, 0x0085, 276},
    {134, 0x0086, 509},
    {135, 0x0087, 509},
    {136, 0x0088, 410},
    {137, 0x0089, 410},
    {138, 0x008A, 234},
    {139, 0x008B, 234},
    {140, 0x008C, 334},
    {141, 0x008D, 334},
    {161, 0x00A1, 732},
    {162, 0x00A2, 544},
    {163, 0x00A3, 544},
    {164, 0x00A4, 910},
    {165, 0x00A5, 667},
    {166, 0x00A6, 760},
    {167, 0x00A7, 760},
    {168, 0x00A8, 776},
    {169, 0x00A9, 595},
    {170, 0x00AA, 694},
    {171, 0x00AB, 626},
    {172, 0x00AC, 788},
    {173, 0x00AD, 788},
    {174, 0x00AE, 788},
    {175, 0x00AF, 788},
    {176, 0x00B0, 788},
    {177, 0x00B1, 788},
    {178, 0x00B2, 788},
    {179, 0x00B3, 788},
    {180, 0x00B4, 788},
    {181, 0x00B5, 788},
    {182, 0x00B6, 788},
    {183, 0x00B7, 788},
    {184, 0x00B8, 788},
    {185, 0x00B9, 788},
    {186, 0x00BA, 788},
    {187, 0x00BB, 788},
    {188, 0x00BC, 788},
    {189, 0x00BD, 788},
    {190, 0x00BE, 788},
    {191, 0x00BF, 788},
    {192, 0x00C0, 788},
    {193, 0x00C1, 788},
    {194, 0x00C2, 788},
    {195, 0x00C3, 788},
    {196, 0x00C4, 788},
    {197, 0x00C5, 788},
    {198, 0x00C6, 788},
    {199, 0x00C7, 788},
    {200, 0x00C8, 788},
    {201, 0x00C9, 788},
    {202, 0x00CA, 788},
    {203, 0x00CB, 788},
    {204, 0x00CC, 788},
    {205, 0x00CD, 788},
    {206, 0x00CE, 788},
    {207, 0x00CF, 788},
    {208, 0x00D0, 788},
    {209, 0x00D1, 788},
    {210, 0x00D2, 788},
    {211, 0x00D3, 788},
    {212, 0x00D4, 894},
    {213, 0x00D5, 838},
    {214, 0x00D6, 1016},
    {215, 0x00D7, 458},
    {216, 0x00D8, 748},
    {217, 0x00D9, 924},
    {218, 0x00DA, 748},
    {219, 0x00DB, 918},
    {220, 0x00DC, 927},
    {221, 0x00DD, 928},
    {222, 0x00DE, 928},
    {223, 0x00DF, 834},
    {224, 0x00E0, 873},
    {225, 0x00E1, 828},
    {226, 0x00E2, 924},
    {227, 0x00E3, 924},
    {228, 0x00E4, 917},
    {229, 0x00E5, 930},
    {230, 0x00E6, 931},
    {231, 0x00E7, 463},
    {232, 0x00E8, 883},
    {233, 0x00E9, 836},
    {234, 0x00EA, 836},
    {235, 0x00EB, 867},
    {236, 0x00EC, 867},
    {237, 0x00ED, 696},
    {238, 0x00EE, 696},
    {239, 0x00EF, 874},
    {241, 0x00F1, 874},
    {242, 0x00F2, 760},
    {243, 0x00F3, 946},
    {244, 0x00F4, 771},
    {245, 0x00F5, 865},
    {246, 0x00F6, 771},
    {247, 0x00F7, 888},
    {248, 0x00F8, 967},
    {249, 0x00F9, 888},
    {250, 0x00FA, 831},
    {251, 0x00FB, 873},
    {252, 0x00FC, 927},
    {253, 0x00FD, 970},
    {254, 0x00FE, 918},
    {-1, 0xFFFF, 0}
    };

static const HPDF_CharData CHAR_DATA_SYMBOL[190] = {
    {32, 0x0020, 250},
    {33, 0x0021, 333},
    {34, 0x0022, 713},
    {35, 0x0023, 500},
    {36, 0x0024, 549},
    {37, 0x0025, 833},
    {38, 0x0026, 778},
    {39, 0x0027, 439},
    {40, 0x0028, 333},
    {41, 0x0029, 333},
    {42, 0x002A, 500},
    {43, 0x002B, 549},
    {44, 0x002C, 250},
    {45, 0x002D, 549},
    {46, 0x002E, 250},
    {47, 0x002F, 278},
    {48, 0x0030, 500},
    {49, 0x0031, 500},
    {50, 0x0032, 500},
    {51, 0x0033, 500},
    {52, 0x0034, 500},
    {53, 0x0035, 500},
    {54, 0x0036, 500},
    {55, 0x0037, 500},
    {56, 0x0038, 500},
    {57, 0x0039, 500},
    {58, 0x003A, 278},
    {59, 0x003B, 278},
    {60, 0x003C, 549},
    {61, 0x003D, 549},
    {62, 0x003E, 549},
    {63, 0x003F, 444},
    {64, 0x0040, 549},
    {65, 0x0041, 722},
    {66, 0x0042, 667},
    {67, 0x0043, 722},
    {68, 0x0044, 612},
    {69, 0x0045, 611},
    {70, 0x0046, 763},
    {71, 0x0047, 603},
    {72, 0x0048, 722},
    {73, 0x0049, 333},
    {74, 0x004A, 631},
    {75, 0x004B, 722},
    {76, 0x004C, 686},
    {77, 0x004D, 889},
    {78, 0x004E, 722},
    {79, 0x004F, 722},
    {80, 0x0050, 768},
    {81, 0x0051, 741},
    {82, 0x0052, 556},
    {83, 0x0053, 592},
    {84, 0x0054, 611},
    {85, 0x0055, 690},
    {86, 0x0056, 439},
    {87, 0x0057, 768},
    {88, 0x0058, 645},
    {89, 0x0059, 795},
    {90, 0x005A, 611},
    {91, 0x005B, 333},
    {92, 0x005C, 863},
    {93, 0x005D, 333},
    {94, 0x005E, 658},
    {95, 0x005F, 500},
    {96, 0x0060, 500},
    {97, 0x0061, 631},
    {98, 0x0062, 549},
    {99, 0x0063, 549},
    {100, 0x0064, 494},
    {101, 0x0065, 439},
    {102, 0x0066, 521},
    {103, 0x0067, 411},
    {104, 0x0068, 603},
    {105, 0x0069, 329},
    {106, 0x006A, 603},
    {107, 0x006B, 549},
    {108, 0x006C, 549},
    {109, 0x006D, 576},
    {110, 0x006E, 521},
    {111, 0x006F, 549},
    {112, 0x0070, 549},
    {113, 0x0071, 521},
    {114, 0x0072, 549},
    {115, 0x0073, 603},
    {116, 0x0074, 439},
    {117, 0x0075, 576},
    {118, 0x0076, 713},
    {119, 0x0077, 686},
    {120, 0x0078, 493},
    {121, 0x0079, 686},
    {122, 0x007A, 494},
    {123, 0x007B, 480},
    {124, 0x007C, 200},
    {125, 0x007D, 480},
    {126, 0x007E, 549},
    {160, 0x00A0, 750},
    {161, 0x00A1, 620},
    {162, 0x00A2, 247},
    {163, 0x00A3, 549},
    {164, 0x00A4, 167},
    {165, 0x00A5, 713},
    {166, 0x00A6, 500},
    {167, 0x00A7, 753},
    {168, 0x00A8, 753},
    {169, 0x00A9, 753},
    {170, 0x00AA, 753},
    {171, 0x00AB, 1042},
    {172, 0x00AC, 987},
    {173, 0x00AD, 603},
    {174, 0x00AE, 987},
    {175, 0x00AF, 603},
    {176, 0x00B0, 400},
    {177, 0x00B1, 549},
    {178, 0x00B2, 411},
    {179, 0x00B3, 549},
    {180, 0x00B4, 549},
    {181, 0x00B5, 713},
    {182, 0x00B6, 494},
    {183, 0x00B7, 460},
    {184, 0x00B8, 549},
    {185, 0x00B9, 549},
    {186, 0x00BA, 549},
    {187, 0x00BB, 549},
    {188, 0x00BC, 1000},
    {189, 0x00BD, 603},
    {190, 0x00BE, 1000},
    {191, 0x00BF, 658},
    {192, 0x00C0, 823},
    {193, 0x00C1, 686},
    {194, 0x00C2, 795},
    {195, 0x00C3, 987},
    {196, 0x00C4, 768},
    {197, 0x00C5, 768},
    {198, 0x00C6, 823},
    {199, 0x00C7, 768},
    {200, 0x00C8, 768},
    {201, 0x00C9, 713},
    {202, 0x00CA, 713},
    {203, 0x00CB, 713},
    {204, 0x00CC, 713},
    {205, 0x00CD, 713},
    {206, 0x00CE, 713},
    {207, 0x00CF, 713},
    {208, 0x00D0, 768},
    {209, 0x00D1, 713},
    {210, 0x00D2, 790},
    {211, 0x00D3, 790},
    {212, 0x00D4, 890},
    {213, 0x00D5, 823},
    {214, 0x00D6, 549},
    {215, 0x00D7, 250},
    {216, 0x00D8, 713},
    {217, 0x00D9, 603},
    {218, 0x00DA, 603},
    {219, 0x00DB, 1042},
    {220, 0x00DC, 987},
    {221, 0x00DD, 603},
    {222, 0x00DE, 987},
    {223, 0x00DF, 603},
    {224, 0x00E0, 494},
    {225, 0x00E1, 329},
    {226, 0x00E2, 790},
    {227, 0x00E3, 790},
    {228, 0x00E4, 786},
    {229, 0x00E5, 713},
    {230, 0x00E6, 384},
    {231, 0x00E7, 384},
    {232, 0x00E8, 384},
    {233, 0x00E9, 384},
    {234, 0x00EA, 384},
    {235, 0x00EB, 384},
    {236, 0x00EC, 494},
    {237, 0x00ED, 494},
    {238, 0x00EE, 494},
    {239, 0x00EF, 494},
    {241, 0x00F1, 329},
    {242, 0x00F2, 274},
    {243, 0x00F3, 686},
    {244, 0x00F4, 686},
    {245, 0x00F5, 686},
    {246, 0x00F6, 384},
    {247, 0x00F7, 384},
    {248, 0x00F8, 384},
    {249, 0x00F9, 384},
    {250, 0x00FA, 384},
    {251, 0x00FB, 384},
    {252, 0x00FC, 494},
    {253, 0x00FD, 494},
    {254, 0x00FE, 494},
    {-1, 0xFFFF, 0}
    };


/*----------------------------------------------------------------------------*/
/*------ base14 fonts --------------------------------------------------------*/

#define  HPDF_FONT_COURIER                 "Courier"
#define  HPDF_FONT_COURIER_BOLD            "Courier-Bold"
#define  HPDF_FONT_COURIER_OBLIQUE         "Courier-Oblique"
#define  HPDF_FONT_COURIER_BOLD_OBLIQUE    "Courier-BoldOblique"
#define  HPDF_FONT_HELVETICA               "Helvetica"
#define  HPDF_FONT_HELVETICA_BOLD          "Helvetica-Bold"
#define  HPDF_FONT_HELVETICA_OBLIQUE       "Helvetica-Oblique"
#define  HPDF_FONT_HELVETICA_BOLD_OBLIQUE  "Helvetica-BoldOblique"
#define  HPDF_FONT_TIMES_ROMAN             "Times-Roman"
#define  HPDF_FONT_TIMES_BOLD              "Times-Bold"
#define  HPDF_FONT_TIMES_ITALIC            "Times-Italic"
#define  HPDF_FONT_TIMES_BOLD_ITALIC       "Times-BoldItalic"
#define  HPDF_FONT_SYMBOL                  "Symbol"
#define  HPDF_FONT_ZAPF_DINGBATS           "ZapfDingbats"


typedef struct _HPDF_Base14FontDefData {
    const char      *font_name;
    const HPDF_CharData  *widths_table;
    HPDF_BOOL             is_font_specific;
    HPDF_INT16            ascent;
    HPDF_INT16            descent;
    HPDF_UINT16           x_height;
    HPDF_UINT16           cap_height;
    HPDF_Box              bbox;
} HPDF_Base14FontDefData;


static const HPDF_Base14FontDefData  HPDF_BUILTIN_FONTS[] = {
    {
        HPDF_FONT_COURIER,
        CHAR_DATA_COURIER,
        HPDF_FALSE,
        629,
        -157,
        426,
        562,
        {-23, -250, 715, 805}
    },
    {
        HPDF_FONT_COURIER_BOLD,
        CHAR_DATA_COURIER_BOLD,
        HPDF_FALSE,
        629,
        -157,
        439,
        562,
        {-113, -250, 749, 801}
    },
    {
        HPDF_FONT_COURIER_OBLIQUE,
        CHAR_DATA_COURIER_OBLIQUE,
        HPDF_FALSE,
        629,
        -157,
        426,
        562,
        {-27, -250, 849, 805}
    },
    {
        HPDF_FONT_COURIER_BOLD_OBLIQUE,
        CHAR_DATA_COURIER_BOLD_OBLIQUE,
        HPDF_FALSE,
        629,
        -157,
        439,
        562,
        {-57, -250, 869, 801}
    },
    {
        HPDF_FONT_HELVETICA,
        CHAR_DATA_HELVETICA,
        HPDF_FALSE,
        718,
        -207,
        523,
        718,
        {-166, -225, 1000, 931}
    },
    {
        HPDF_FONT_HELVETICA_BOLD,
        CHAR_DATA_HELVETICA_BOLD,
        HPDF_FALSE,
        718,
        -207,
        532,
        718,
        {-170, -228, 1003, 962}
    },
    {
        HPDF_FONT_HELVETICA_OBLIQUE,
        CHAR_DATA_HELVETICA_OBLIQUE,
        HPDF_FALSE,
        718,
        -207,
        532,
        718,
        {-170, -225, 1116, 931}
    },
    {
        HPDF_FONT_HELVETICA_BOLD_OBLIQUE,
        CHAR_DATA_HELVETICA_BOLD_OBLIQUE,
        HPDF_FALSE,
        718,
        -207,
        532,
        718,
        {-174, -228, 1114, 962}
    },
    {
        HPDF_FONT_TIMES_ROMAN,
        CHAR_DATA_TIMES_ROMAN,
        HPDF_FALSE,
        683,
        -217,
        450,
        662,
        {-168, -218, 1000, 898}
    },
    {
        HPDF_FONT_TIMES_BOLD,
        CHAR_DATA_TIMES_BOLD,
        HPDF_FALSE,
        683,
        -217,
        461,
        676,
        {-168, -218, 1000, 935}
    },
    {
        HPDF_FONT_TIMES_ITALIC,
        CHAR_DATA_TIMES_ITALIC,
        HPDF_FALSE,
        683,
        -217,
        441,
        653,
        {-169, -217, 1010, 883}
    },
    {
        HPDF_FONT_TIMES_BOLD_ITALIC,
        CHAR_DATA_TIMES_BOLD_ITALIC,
        HPDF_FALSE,
        683,
        -217,
        462,
        669,
        {-200, -218, 996, 921}
    },
    {
        HPDF_FONT_SYMBOL,
        CHAR_DATA_SYMBOL,
        HPDF_TRUE,
        0,
        0,
        0,
        0,
        {-180, -293, 1090, 1010}
    },
    {
        HPDF_FONT_ZAPF_DINGBATS,
        CHAR_DATA_ZAPF_DINGBATS,
        HPDF_TRUE,
        0,
        0,
        0,
        0,
        {-1, -143, 981, 820}
    },
    {
        NULL,
        NULL,
        HPDF_FALSE,
        0,
        0,
        0,
        0,
        {0, 0, 0, 0}
    },
};


/*---------------------------------------------------------------------------*/

const HPDF_Base14FontDefData*
HPDF_Base14FontDef_FindBuiltinData  (const char  *font_name);


/*---------------------------------------------------------------------------*/
/*----- PDF_Base14FontDef ---------------------------------------------------*/

const HPDF_Base14FontDefData*
HPDF_Base14FontDef_FindBuiltinData  (const char  *font_name)
{
    HPDF_UINT i = 0;

    while (HPDF_BUILTIN_FONTS[i].font_name) {
        if (HPDF_StrCmp (HPDF_BUILTIN_FONTS[i].font_name, font_name) == 0)
            break;

        i++;
    }

    return &HPDF_BUILTIN_FONTS[i];
}

HPDF_FontDef
HPDF_Base14FontDef_New  (HPDF_MMgr        mmgr,
                         const char  *font_name)
{
    HPDF_FontDef                   fontdef;
    HPDF_STATUS                    ret;
    const HPDF_Base14FontDefData   *data;
    char                      *eptr;
    HPDF_Type1FontDefAttr          attr;

    fontdef = HPDF_Type1FontDef_New (mmgr);
    if (!fontdef)
        return NULL;

    data = HPDF_Base14FontDef_FindBuiltinData (font_name);

    if (!data->font_name) {
        HPDF_SetError (mmgr->error, HPDF_INVALID_FONT_NAME, 0);
        HPDF_FontDef_Free (fontdef);
        return NULL;
    }

    eptr = fontdef->base_font + HPDF_LIMIT_MAX_NAME_LEN;
    HPDF_StrCpy (fontdef->base_font, data->font_name, eptr);

    attr = (HPDF_Type1FontDefAttr)fontdef->attr;
    attr->is_base14font = HPDF_TRUE;

    if (data->is_font_specific)
        HPDF_StrCpy (attr->encoding_scheme, HPDF_ENCODING_FONT_SPECIFIC,
                attr->encoding_scheme + HPDF_LIMIT_MAX_NAME_LEN);

    ret = HPDF_Type1FontDef_SetWidths (fontdef, data->widths_table);

    if (ret != HPDF_OK) {
        HPDF_FontDef_Free (fontdef);
        return NULL;
    }

    fontdef->font_bbox = data->bbox;
    fontdef->ascent = data->ascent;
    fontdef->descent = data->descent;
    fontdef->x_height = data->x_height;
    fontdef->cap_height = data->cap_height;

    fontdef->valid = HPDF_TRUE;

    return fontdef;
}

