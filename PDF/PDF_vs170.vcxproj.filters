<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{22024715-a87e-476f-99b6-8c1e3471578a}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{83111f3e-9288-4ee2-88ef-c7ae6d22ebd4}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rd Party">
      <UniqueIdentifier>{01093604-2890-4215-9d61-3c2c1137b796}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rd Party\PNG">
      <UniqueIdentifier>{0da2898f-0b32-46eb-a0be-06696acd2072}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rd Party\PNG\Header Files">
      <UniqueIdentifier>{82595127-16fc-4973-904c-d3f94ab9e46f}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rd Party\PNG\Source Files">
      <UniqueIdentifier>{71664612-437f-4634-b8cd-11d2a6d58619}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rd Party\zlib">
      <UniqueIdentifier>{1c8f27d9-790c-4470-b109-5836a88f68b5}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rd Party\zlib\Header Files">
      <UniqueIdentifier>{2921824a-72de-4cc0-a51d-b377d2b6ff9d}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rd Party\zlib\Source Files">
      <UniqueIdentifier>{014bfa9e-75f5-4eb4-a62c-0e538b372395}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rd Party\HARU">
      <UniqueIdentifier>{9bb656c9-8dc5-4902-a8c5-78dd9dbc85c1}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rd Party\HARU\Source Files">
      <UniqueIdentifier>{1944c485-901e-46e8-b860-958e3a4cb65b}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rd Party\HARU\Header Files">
      <UniqueIdentifier>{194e9651-b17f-43cc-9f71-eae3aa905605}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\AttributedString.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Cell.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Destination.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Document.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Encoder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Font.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Image.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LinkAnnotation.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Outline.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Page.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PDFException.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Table.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TextAnnotation.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\XMLTemplate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\png.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngerror.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pnggccrd.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngget.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngmem.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngpread.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngread.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngrio.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngrtran.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngrutil.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngset.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngtest.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngtrans.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngvcrd.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngwio.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngwrite.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngwtran.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pngwutil.c">
      <Filter>3rd Party\PNG\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\adler32.c">
      <Filter>3rd Party\zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\compress.c">
      <Filter>3rd Party\zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\crc32.c">
      <Filter>3rd Party\zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\deflate.c">
      <Filter>3rd Party\zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\infback.c">
      <Filter>3rd Party\zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\inffast.c">
      <Filter>3rd Party\zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\inflate.c">
      <Filter>3rd Party\zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\inftrees.c">
      <Filter>3rd Party\zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\trees.c">
      <Filter>3rd Party\zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\zutil.c">
      <Filter>3rd Party\zlib\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_3dmeasure.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_annotation.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_array.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_binary.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_boolean.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_catalog.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_destination.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_dict.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_doc.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_doc_png.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_encoder.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_encoder_cns.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_encoder_cnt.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_encoder_jp.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_encoder_kr.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_encoder_utf.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_encrypt.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_encryptdict.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_error.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_exdata.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_ext_gstate.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_font.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_font_cid.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_font_tt.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_font_type1.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_fontdef.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_fontdef_base14.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_fontdef_cid.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_fontdef_cns.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_fontdef_cnt.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_fontdef_jp.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_fontdef_kr.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_fontdef_tt.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_fontdef_type1.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_gstate.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_image.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_image_ccitt.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_image_png.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_info.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_list.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_mmgr.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_name.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_namedict.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_null.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_number.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_objects.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_outline.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_page_label.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_page_operator.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_pages.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_pdfa.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_real.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_streams.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_string.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_u3d.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_utils.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\hpdf_xref.c">
      <Filter>3rd Party\HARU\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\PDF\Destination.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\Document.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\Encoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\Font.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\Image.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\LinkAnnotation.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\Outline.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\Page.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\PDF.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\PDFException.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\Resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\TextAnnotation.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\png.h">
      <Filter>3rd Party\PNG\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\pngconf.h">
      <Filter>3rd Party\PNG\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\crc32.h">
      <Filter>3rd Party\zlib\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\deflate.h">
      <Filter>3rd Party\zlib\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\inffast.h">
      <Filter>3rd Party\zlib\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\inffixed.h">
      <Filter>3rd Party\zlib\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\inflate.h">
      <Filter>3rd Party\zlib\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\inftrees.h">
      <Filter>3rd Party\zlib\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\trees.h">
      <Filter>3rd Party\zlib\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\zconf.h">
      <Filter>3rd Party\zlib\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\zlib.h">
      <Filter>3rd Party\zlib\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\zutil.h">
      <Filter>3rd Party\zlib\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_3dmeasure.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_annotation.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_catalog.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_conf.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_consts.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_destination.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_doc.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_encoder.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_encrypt.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_encryptdict.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_error.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_exdata.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_ext_gstate.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_font.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_fontdef.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_gstate.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_image.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_info.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_list.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_mmgr.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_namedict.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_objects.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_outline.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_page_label.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_pages.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_pdfa.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_streams.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_types.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_u3d.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_utils.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\PDF\hpdf_version.h">
      <Filter>3rd Party\HARU\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>