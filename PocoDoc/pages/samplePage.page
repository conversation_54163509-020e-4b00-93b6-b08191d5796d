This is a test page
Category 1

Poco::Util::AbstractConfiguration is an abstract base class for different
kinds of configuration data, such as INI files, property files,
XML configuration files or the Windows Registry.

!!! Large Heading
Configuration property keys have a hierarchical format, consisting
of names separated by periods. The exact interpretation of key names
is up to the actual subclass implementation of AbstractConfiguration.
Keys are case sensitive. This is a link to somewhere else: http://www.appinf.com
!! Medium Heading
All public methods are synchronized, so the class is safe for multithreaded use.
AbstractConfiguration implements reference counting based garbage collection.
!Small Heading
Subclasses must override the getRaw(), setRaw() and enumerate() methods.

Here is some sample code:
    int main(int argc, char** argv)
    {
    	std::cout << "Hello, world!" << std::endl;
    	return 0;
    }

!Another Paragraph
Here we're -- showing <*italic text*>, <!bold text!> and <[teletype text]>.
We can even do <*<!bold italic text!>*>!

We can also refer to config properties: <? PocoDoc.software ?>

And we can even include HTML:
<%
<p align="center">This is centered</p>
%>
...and <{<span style="font-weight: bold;">inline HTML</span>}>.
And here is ordinary text, again.

This is an [[http://www.appinf.com external link]].
