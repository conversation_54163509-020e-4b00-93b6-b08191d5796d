<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="SSLCore">
      <UniqueIdentifier>{98aa424b-7532-4b2d-891c-9fef80bfae5e}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLCore\Header Files">
      <UniqueIdentifier>{cbbd7304-fb93-40b7-9982-0c446fdefa25}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLCore\Source Files">
      <UniqueIdentifier>{aa2ad61f-2a83-410f-8b3a-ea51dc73ad47}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient">
      <UniqueIdentifier>{fb360728-36fd-4f1b-a0f1-ee8df1bbeae7}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Header Files">
      <UniqueIdentifier>{d62316e6-7cf9-4c0a-a059-64686f0a9503}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Source Files">
      <UniqueIdentifier>{b4d8bc41-7eb7-4203-b495-5049a1960ae7}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLSockets">
      <UniqueIdentifier>{21eeec6b-14bb-4044-8246-54ecc22d7996}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLSockets\Header Files">
      <UniqueIdentifier>{06e2d595-1e6d-4c10-a4e3-acddd13a876a}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLSockets\Source Files">
      <UniqueIdentifier>{74f65ce5-71e3-4979-9cb6-4d3ff23ecaa1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail">
      <UniqueIdentifier>{3aa23113-cd7b-463c-9409-b4c6be2ebb4e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Header Files">
      <UniqueIdentifier>{e13f55e1-706d-4568-8d19-18cf32ae7ae8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Source Files">
      <UniqueIdentifier>{b6c5e09f-4afa-4cb0-bc97-08d4355611da}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPSClient">
      <UniqueIdentifier>{b903515b-ef30-4735-a8b7-0a2d03bd2ef8}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPSClient\Header Files">
      <UniqueIdentifier>{7a5e617a-e8ae-4aa5-897d-d91a64a4121c}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPSClient\Source Files">
      <UniqueIdentifier>{8b360cc5-4e66-475f-acce-130229e1c26f}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Net\AcceptCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\CertificateHandlerFactory.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\CertificateHandlerFactoryMgr.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ConsoleCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Context.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\InvalidCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\KeyConsoleHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\KeyFileHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NetSSL.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PrivateKeyFactory.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PrivateKeyFactoryMgr.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PrivateKeyPassphraseHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\RejectCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Session.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SSLException.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SSLManager.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Utility.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\VerificationErrorArgs.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\X509Certificate.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSClientSession.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSSessionInstantiator.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSStreamFactory.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureServerSocket.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureServerSocketImpl.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureSocketImpl.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureStreamSocket.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureStreamSocketImpl.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureSMTPClientSession.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\FTPSClientSession.h">
      <Filter>FTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\FTPSStreamFactory.h">
      <Filter>FTPSClient\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\AcceptCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CertificateHandlerFactory.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CertificateHandlerFactoryMgr.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ConsoleCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Context.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\InvalidCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KeyConsoleHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KeyFileHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PrivateKeyFactory.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PrivateKeyFactoryMgr.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PrivateKeyPassphraseHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RejectCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Session.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SSLException.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SSLManager.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Utility.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\VerificationErrorArgs.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\X509Certificate.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSClientSession.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSSessionInstantiator.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSStreamFactory.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureServerSocket.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureServerSocketImpl.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureSocketImpl.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocket.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocketImpl.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureSMTPClientSession.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FTPSClientSession.cpp">
      <Filter>FTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FTPSStreamFactory.cpp">
      <Filter>FTPSClient\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>