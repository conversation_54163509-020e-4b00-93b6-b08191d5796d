<AppConfig>
	<openSSL>
		<server>
			<privateKeyFile>${application.configDir}any.pem</privateKeyFile>
			<caConfig>${application.configDir}rootcert.pem</caConfig>
			<verificationMode>none</verificationMode>
			<verificationDepth>9</verificationDepth>
			<loadDefaultCAFile>true</loadDefaultCAFile>
			<cypherList>ALL:!ADH:!LOW:!EXP:!MD5:@STRENGTH</cypherList>
			<privateKeyPassphraseHandler>
				<name>KeyFileHandler</name>
				<options>
					<password>secret</password>
				</options>
			</privateKeyPassphraseHandler>
			<invalidCertificateHandler>
				<name>AcceptCertificateHandler</name>
				<options>
				</options>
			</invalidCertificateHandler>
		</server>
		<client>
			<privateKeyFile>${application.configDir}any.pem</privateKeyFile>
			<caConfig>${application.configDir}rootcert.pem</caConfig>
			<verificationMode>relaxed</verificationMode>
			<verificationDepth>9</verificationDepth>
			<loadDefaultCAFile>true</loadDefaultCAFile>
			<cypherList>ALL:!ADH:!LOW:!EXP:!MD5:@STRENGTH</cypherList>
			<dhParamsFile>${application.configDir}dhparams.pem</dhParamsFile>
			<privateKeyPassphraseHandler>
				<name>KeyFileHandler</name>
				<options>
					<password>secret</password>
				</options>
			</privateKeyPassphraseHandler>
			<invalidCertificateHandler>
				<name>AcceptCertificateHandler</name>
				<options>
				</options>
			</invalidCertificateHandler>
		</client>
	</openSSL>
	<testsuite>
		<proxy>
			<host>proxy.aon.at</host>
			<port>8080</port>
		</proxy>
	</testsuite>
</AppConfig>
