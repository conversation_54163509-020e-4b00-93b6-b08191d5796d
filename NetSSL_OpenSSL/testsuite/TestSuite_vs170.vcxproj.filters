<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="HTTPS">
      <UniqueIdentifier>{4e1ec44c-07fc-4690-95f0-7656bce2995b}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPS\Header Files">
      <UniqueIdentifier>{f0decc95-5d09-41a3-bb93-365bc1c063e4}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPS\Source Files">
      <UniqueIdentifier>{a69ee5de-36bb-47dc-8a79-8e90b232221e}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite">
      <UniqueIdentifier>{3049295c-de72-48ea-b55d-44e9db140f37}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Header Files">
      <UniqueIdentifier>{ed85b617-1f5a-4dc3-91dc-fdc4fedf75f9}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Source Files">
      <UniqueIdentifier>{8a9073d0-cb04-478f-8761-972aa3ba539a}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver">
      <UniqueIdentifier>{826b3eca-d42e-4fec-ac91-4b82885f0ecb}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver\Source Files">
      <UniqueIdentifier>{710b3009-792e-45a5-9196-52216b764813}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer">
      <UniqueIdentifier>{545a8aff-5dd4-4017-a42d-70d094d44395}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Header Files">
      <UniqueIdentifier>{4087a9d7-a00c-4804-b95a-c4ef33715e5e}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Source Files">
      <UniqueIdentifier>{4a06593b-d9b0-4cf7-859d-5109fbe36d40}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSServer">
      <UniqueIdentifier>{7affb5c0-0cb5-4eb7-a3cc-3102a9bca878}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSServer\Header Files">
      <UniqueIdentifier>{9df04650-945a-4f98-a712-86c34f843c34}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSServer\Source Files">
      <UniqueIdentifier>{7c658802-a93f-4dfd-a76c-ca9e51388982}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient">
      <UniqueIdentifier>{fab7172b-d802-483c-9a8f-43f53e32441c}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Header Files">
      <UniqueIdentifier>{10c61850-b18d-47df-9d71-f5b42bc487b3}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Source Files">
      <UniqueIdentifier>{02bb6939-5818-4a64-b27f-c5b0545a453b}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket">
      <UniqueIdentifier>{2181bca5-6467-41e2-a90d-30263bb69a01}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Header Files">
      <UniqueIdentifier>{a8ddeab9-2737-412d-9279-637f7220079d}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Source Files">
      <UniqueIdentifier>{cb8d8052-e837-48c6-ac74-849366ed75d0}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPSClient">
      <UniqueIdentifier>{6f0e6134-4fc6-41b1-95f3-d4c71ea53fd0}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPSClient\Header Files">
      <UniqueIdentifier>{767b5059-aa88-4b4b-b5e7-98b3167b1ae8}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPSClient\Source Files">
      <UniqueIdentifier>{93a3a5f8-8056-435b-86ca-ef2fb15351c8}</UniqueIdentifier>
    </Filter>
    <Filter Include="SecureSocket">
      <UniqueIdentifier>{a5fbbc5a-297c-465b-a150-e7e4f9078eef}</UniqueIdentifier>
    </Filter>
    <Filter Include="SecureSocket\Header Files">
      <UniqueIdentifier>{ed2ce712-70d3-45cc-a0d1-ca8823321ca6}</UniqueIdentifier>
    </Filter>
    <Filter Include="SecureSocket\Source Files">
      <UniqueIdentifier>{432fbf93-c96e-47a5-888f-aa786982fdb3}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\HTTPSTestServer.h">
      <Filter>HTTPS\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NetSSLTestSuite.h">
      <Filter>_Suite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TCPServerTest.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TCPServerTestSuite.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSServerTest.h">
      <Filter>HTTPSServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSServerTestSuite.h">
      <Filter>HTTPSServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSClientSessionTest.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSClientTestSuite.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSStreamFactoryTest.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\WebSocketTest.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\WebSocketTestSuite.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DialogServer.h">
      <Filter>FTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FTPSClientSessionTest.h">
      <Filter>FTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FTPSClientTestSuite.h">
      <Filter>FTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SecureStreamSocketTest.h">
      <Filter>SecureSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SecureStreamSocketTestSuite.h">
      <Filter>SecureSocket\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\HTTPSTestServer.cpp">
      <Filter>HTTPS\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NetSSLTestSuite.cpp">
      <Filter>_Suite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Driver.cpp">
      <Filter>_Driver\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerTest.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerTestSuite.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSServerTest.cpp">
      <Filter>HTTPSServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSServerTestSuite.cpp">
      <Filter>HTTPSServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSClientSessionTest.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSClientTestSuite.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSStreamFactoryTest.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WebSocketTest.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WebSocketTestSuite.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DialogServer.cpp">
      <Filter>FTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FTPSClientSessionTest.cpp">
      <Filter>FTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FTPSClientTestSuite.cpp">
      <Filter>FTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocketTest.cpp">
      <Filter>SecureSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocketTestSuite.cpp">
      <Filter>SecureSocket\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>