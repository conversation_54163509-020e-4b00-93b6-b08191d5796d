# This is a sample configuration file for HTTPSTimeServer

HTTPSTimeServer.format = %W, %e %b %y %H:%M:%S %Z
HTTPSTimeServer.port   = 9443

openSSL.server.privateKeyFile = ${application.configDir}any.pem
openSSL.server.caConfig = ${application.configDir}rootcert.pem
openSSL.server.verificationMode = relaxed
openSSL.server.verificationDepth = 9
openSSL.server.loadDefaultCAFile = true
openSSL.server.cipherList = ALL:!ADH:!LOW:!EXP:!MD5:@STRENGTH
openSSL.server.privateKeyPassphraseHandler.name = KeyFileHandler
openSSL.server.privateKeyPassphraseHandler.options.password = secret
openSSL.server.invalidCertificateHandler.name = AcceptCertificateHandler
openSSL.server.extendedVerification = false
openSSL.server.cacheSessions = true
openSSL.server.sessionIdContext = ${application.name}
openSSL.server.sessionCacheSize = 100
openSSL.server.requireTLSv1 = false

logging.loggers.root.channel.class = ConsoleChannel
logging.loggers.app.name = Application
logging.loggers.app.channel = c1
logging.formatters.f1.class = PatternFormatter
logging.formatters.f1.pattern = [%p] %t
logging.channels.c1.class = ConsoleChannel
logging.channels.c1.formatter = f1
