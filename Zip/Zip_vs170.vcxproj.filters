<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Zip">
      <UniqueIdentifier>{4c57dcaf-46ac-4201-aaa4-7056a393dd08}</UniqueIdentifier>
    </Filter>
    <Filter Include="Zip\Header Files">
      <UniqueIdentifier>{c5d0f348-f132-490b-af39-ba1bd042070c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Zip\Source Files">
      <UniqueIdentifier>{936b3719-5064-453b-bb9f-c797838aa606}</UniqueIdentifier>
    </Filter>
    <Filter Include="Manipulation">
      <UniqueIdentifier>{eb3f3d7d-7788-495f-8053-4960a6d5ff1e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Manipulation\Header Files">
      <UniqueIdentifier>{15e5afd7-5b52-47bc-9e3b-501f5359b634}</UniqueIdentifier>
    </Filter>
    <Filter Include="Manipulation\Source Files">
      <UniqueIdentifier>{77017fbc-a16a-46f7-90e9-b27ca9f6fdf8}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Zip\AutoDetectStream.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\Compress.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\Decompress.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\ParseCallback.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\PartialStream.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\SkipCallback.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\Zip.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\ZipArchive.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\ZipArchiveInfo.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\ZipCommon.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\ZipDataInfo.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\ZipException.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\ZipFileInfo.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\ZipLocalFileHeader.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\ZipStream.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\ZipUtil.h">
      <Filter>Zip\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\Add.h">
      <Filter>Manipulation\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\Delete.h">
      <Filter>Manipulation\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\Keep.h">
      <Filter>Manipulation\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\Rename.h">
      <Filter>Manipulation\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\Replace.h">
      <Filter>Manipulation\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\ZipManipulator.h">
      <Filter>Manipulation\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Zip\ZipOperation.h">
      <Filter>Manipulation\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\AutoDetectStream.cpp">
      <Filter>Zip\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Compress.cpp">
      <Filter>Zip\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Decompress.cpp">
      <Filter>Zip\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ParseCallback.cpp">
      <Filter>Zip\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PartialStream.cpp">
      <Filter>Zip\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SkipCallback.cpp">
      <Filter>Zip\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ZipArchive.cpp">
      <Filter>Zip\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ZipArchiveInfo.cpp">
      <Filter>Zip\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ZipCommon.cpp">
      <Filter>Zip\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ZipDataInfo.cpp">
      <Filter>Zip\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ZipException.cpp">
      <Filter>Zip\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ZipFileInfo.cpp">
      <Filter>Zip\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ZipLocalFileHeader.cpp">
      <Filter>Zip\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ZipStream.cpp">
      <Filter>Zip\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ZipUtil.cpp">
      <Filter>Zip\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Add.cpp">
      <Filter>Manipulation\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Delete.cpp">
      <Filter>Manipulation\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Keep.cpp">
      <Filter>Manipulation\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Rename.cpp">
      <Filter>Manipulation\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Replace.cpp">
      <Filter>Manipulation\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ZipManipulator.cpp">
      <Filter>Manipulation\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ZipOperation.cpp">
      <Filter>Manipulation\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>