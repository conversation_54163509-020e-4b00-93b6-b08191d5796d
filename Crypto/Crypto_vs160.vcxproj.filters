<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Cipher">
      <UniqueIdentifier>{a91f07bc-ca03-4f8c-8db5-a6d27c90c583}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cipher\Header Files">
      <UniqueIdentifier>{944ca446-b1c9-4d0e-8e1d-342305925d77}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cipher\Source Files">
      <UniqueIdentifier>{fd1860dc-1a07-4bc9-913b-ab9f75e29b98}</UniqueIdentifier>
    </Filter>
    <Filter Include="RSA">
      <UniqueIdentifier>{ed59fda0-ba3d-476f-8bb3-888ef4fdf3a5}</UniqueIdentifier>
    </Filter>
    <Filter Include="RSA\Header Files">
      <UniqueIdentifier>{06cd83f3-f104-4df6-9afc-82ee1133842b}</UniqueIdentifier>
    </Filter>
    <Filter Include="RSA\Source Files">
      <UniqueIdentifier>{507397c4-6ea6-48ea-b1a8-fc67a472e682}</UniqueIdentifier>
    </Filter>
    <Filter Include="Certificate">
      <UniqueIdentifier>{96290761-551c-450d-9c40-0ad606d841c0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Certificate\Header Files">
      <UniqueIdentifier>{a35346d7-41b5-4457-b628-185b3889e957}</UniqueIdentifier>
    </Filter>
    <Filter Include="Certificate\Source Files">
      <UniqueIdentifier>{d3385b52-894f-43a4-98c7-43f6863af9a4}</UniqueIdentifier>
    </Filter>
    <Filter Include="CryptoCore">
      <UniqueIdentifier>{7e9c7be2-f096-4a9d-aa38-4fb9d02367e2}</UniqueIdentifier>
    </Filter>
    <Filter Include="CryptoCore\Header Files">
      <UniqueIdentifier>{07023853-f234-43fc-80a9-8b6b442f154a}</UniqueIdentifier>
    </Filter>
    <Filter Include="CryptoCore\Source Files">
      <UniqueIdentifier>{ec9c6040-51f0-4756-bf05-7ed4af3dd874}</UniqueIdentifier>
    </Filter>
    <Filter Include="Digest">
      <UniqueIdentifier>{3283a946-98a0-4c74-8243-87dcf33b3029}</UniqueIdentifier>
    </Filter>
    <Filter Include="Digest\Header Files">
      <UniqueIdentifier>{651a0fb2-f8ea-4784-9fa1-d24775e657f9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Digest\Source Files">
      <UniqueIdentifier>{23c0a476-c1d4-42e9-984b-c3ddad2225b9}</UniqueIdentifier>
    </Filter>
    <Filter Include="EC">
      <UniqueIdentifier>{0978c184-dda0-4325-ac4b-6c36fcd57c26}</UniqueIdentifier>
    </Filter>
    <Filter Include="EC\Header Files">
      <UniqueIdentifier>{db81741a-9afa-4e69-a690-175b29385c2e}</UniqueIdentifier>
    </Filter>
    <Filter Include="EC\Source Files">
      <UniqueIdentifier>{0e505bee-7a85-449c-9d9a-d70fce01004b}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Crypto\Cipher.h">
      <Filter>Cipher\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\CipherFactory.h">
      <Filter>Cipher\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\CipherImpl.h">
      <Filter>Cipher\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\CipherKey.h">
      <Filter>Cipher\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\CipherKeyImpl.h">
      <Filter>Cipher\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\CryptoStream.h">
      <Filter>Cipher\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\CryptoTransform.h">
      <Filter>Cipher\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\RSACipherImpl.h">
      <Filter>RSA\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\RSADigestEngine.h">
      <Filter>RSA\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\RSAKey.h">
      <Filter>RSA\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\RSAKeyImpl.h">
      <Filter>RSA\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\PKCS12Container.h">
      <Filter>Certificate\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\X509Certificate.h">
      <Filter>Certificate\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\Crypto.h">
      <Filter>CryptoCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\CryptoException.h">
      <Filter>CryptoCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\EVPCipherImpl.h">
      <Filter>CryptoCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\EVPPKey.h">
      <Filter>CryptoCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\KeyPair.h">
      <Filter>CryptoCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\KeyPairImpl.h">
      <Filter>CryptoCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\OpenSSLInitializer.h">
      <Filter>CryptoCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\DigestEngine.h">
      <Filter>Digest\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\ECDSADigestEngine.h">
      <Filter>EC\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\ECKey.h">
      <Filter>EC\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\ECKeyImpl.h">
      <Filter>EC\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Crypto\Envelope.h">
      <Filter>EC\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\Cipher.cpp">
      <Filter>Cipher\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CipherFactory.cpp">
      <Filter>Cipher\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CipherImpl.cpp">
      <Filter>Cipher\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CipherKey.cpp">
      <Filter>Cipher\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CipherKeyImpl.cpp">
      <Filter>Cipher\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CryptoStream.cpp">
      <Filter>Cipher\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CryptoTransform.cpp">
      <Filter>Cipher\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RSACipherImpl.cpp">
      <Filter>RSA\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RSADigestEngine.cpp">
      <Filter>RSA\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RSAKey.cpp">
      <Filter>RSA\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RSAKeyImpl.cpp">
      <Filter>RSA\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PKCS12Container.cpp">
      <Filter>Certificate\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\X509Certificate.cpp">
      <Filter>Certificate\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CryptoException.cpp">
      <Filter>CryptoCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\EVPCipherImpl.cpp">
      <Filter>CryptoCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\EVPPKey.cpp">
      <Filter>CryptoCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KeyPair.cpp">
      <Filter>CryptoCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KeyPairImpl.cpp">
      <Filter>CryptoCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OpenSSLInitializer.cpp">
      <Filter>CryptoCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DigestEngine.cpp">
      <Filter>Digest\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ECDSADigestEngine.cpp">
      <Filter>EC\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ECKey.cpp">
      <Filter>EC\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ECKeyImpl.cpp">
      <Filter>EC\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Envelope.cpp">
      <Filter>EC\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>