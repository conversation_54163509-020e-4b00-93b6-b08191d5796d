vc.project.guid = 4D6E42AE-EB6A-47EB-A186-B8A183FABCF7
vc.project.name = TestSuite
vc.project.target = TestSuite
vc.project.type = testsuite
vc.project.pocobase = ..\\..\\..
vc.project.platforms = Win32
vc.project.vcpkg = true
vc.project.configurations = debug_shared, release_shared, debug_static_mt, release_static_mt, debug_static_md, release_static_md
vc.project.prototype = TestSuite_vs90.vcproj
mysql = ${vc.project.pocobase}\\mysql
vc.project.compiler.include = ${mysql}\\include;${vc.project.pocobase}\\Foundation\\include; \
	${vc.project.pocobase}\\Data\\include
vc.project.compiler.additionalOptions = /Zc:__cplusplus
vc.project.linker.dependencies = ws2_32.lib iphlpapi.lib
vc.project.linker.dependencies.debug_shared =
vc.project.linker.dependencies.release_shared =
vc.project.linker.dependencies.debug_static_md = Crypt32.lib Secur32.lib shlwapi.lib
vc.project.linker.dependencies.release_static_md = Crypt32.lib Secur32.lib shlwapi.lib
vc.project.linker.dependencies.debug_static_mt = Crypt32.lib Secur32.lib shlwapi.lib
vc.project.linker.dependencies.release_static_mt = Crypt32.lib Secur32.lib shlwapi.lib
