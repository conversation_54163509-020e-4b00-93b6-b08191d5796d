<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="Data"
	ProjectGUID="{240E83C3-368D-11DB-9FBC-00123FC423B5}"
	RootNamespace="Data"
	Keyword="Win32Proj"
	TargetFrameworkVersion="0"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="debug_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_USRDLL;Data_EXPORTS"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies=""
				OutputFile="..\bin\PocoDatad.dll"
				LinkIncremental="2"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\lib"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="..\bin\PocoDatad.pdb"
				SubSystem="1"
				ImportLibrary="..\lib\PocoDatad.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_USRDLL;Data_EXPORTS"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies=""
				OutputFile="..\bin\PocoData.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				ImportLibrary="..\lib\PocoData.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\lib\PocoDataMTd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoDataMTd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="0"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoDataMT.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\lib\PocoDataMDd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoDataMDd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoDataMD.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="DataCore"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\include\Poco\Data\AbstractBinder.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\AbstractBinding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\AbstractExtraction.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\AbstractExtractor.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\AbstractPreparation.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\AbstractPreparator.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\AbstractSessionImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\AutoTransaction.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Binding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Bulk.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\BulkBinding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\BulkExtraction.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Column.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Connector.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Constants.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Data.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\DataException.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Date.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\DynamicDateTime.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\DynamicLOB.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Extraction.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\JSONRowFormatter.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Limit.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\LOB.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\LOBStream.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\MetaColumn.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Position.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\data\Preparation.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Range.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\RecordSet.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Row.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\RowFilter.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\RowFormatter.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\RowIterator.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Session.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\SessionFactory.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\SessionImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\SimpleRowFormatter.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\SQLParser.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Statement.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\StatementCreator.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\StatementImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Time.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Transaction.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\Transcoder.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\TypeHandler.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\AbstractBinder.cpp"
					>
				</File>
				<File
					RelativePath=".\src\AbstractBinding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\AbstractExtraction.cpp"
					>
				</File>
				<File
					RelativePath=".\src\AbstractExtractor.cpp"
					>
				</File>
				<File
					RelativePath=".\src\AbstractPreparation.cpp"
					>
				</File>
				<File
					RelativePath=".\src\AbstractPreparator.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Bulk.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Connector.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DataException.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Date.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DynamicLOB.cpp"
					>
				</File>
				<File
					RelativePath=".\src\JSONRowFormatter.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Limit.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MetaColumn.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Position.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Range.cpp"
					>
				</File>
				<File
					RelativePath=".\src\RecordSet.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Row.cpp"
					>
				</File>
				<File
					RelativePath=".\src\RowFilter.cpp"
					>
				</File>
				<File
					RelativePath=".\src\RowFormatter.cpp"
					>
				</File>
				<File
					RelativePath=".\src\RowIterator.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Session.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SessionFactory.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SessionImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SimpleRowFormatter.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Statement.cpp"
					>
				</File>
				<File
					RelativePath=".\src\StatementCreator.cpp"
					>
				</File>
				<File
					RelativePath=".\src\StatementImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Time.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Transaction.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Transcoder.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="SessionPooling"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\include\Poco\Data\PooledSessionHolder.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\PooledSessionImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\SessionPool.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\SessionPoolContainer.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\PooledSessionHolder.cpp"
					>
				</File>
				<File
					RelativePath=".\src\PooledSessionImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SessionPool.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SessionPoolContainer.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="SQLParser"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\SQLParser\src\SQLParser.h"
					>
				</File>
				<File
					RelativePath=".\SQLParser\src\SQLParserResult.h"
					>
				</File>
				<File
					RelativePath=".\SQLParser\src\sqlparser_win.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\SQLParser\src\SQLParser.cpp"
					>
				</File>
				<File
					RelativePath=".\SQLParser\src\SQLParserResult.cpp"
					>
				</File>
			</Filter>
			<Filter
				Name="parser"
				>
				<Filter
					Name="Header Files"
					>
					<File
						RelativePath=".\SQLParser\src\parser\bison_parser.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\parser\flex_lexer.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\parser\parser_typedef.h"
						>
					</File>
				</Filter>
				<Filter
					Name="Source Files"
					>
					<File
						RelativePath=".\SQLParser\src\parser\bison_parser.cpp"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\parser\flex_lexer.cpp"
						>
					</File>
				</Filter>
			</Filter>
			<Filter
				Name="sql"
				>
				<Filter
					Name="Header Files"
					>
					<File
						RelativePath=".\SQLParser\src\sql\AlterStatement.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\ColumnType.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\CreateStatement.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\DeleteStatement.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\DropStatement.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\ExecuteStatement.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\ExportStatement.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\Expr.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\ImportStatement.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\InsertStatement.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\PrepareStatement.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\SelectStatement.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\ShowStatement.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\SQLStatement.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\statements.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\Table.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\TransactionStatement.h"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\UpdateStatement.h"
						>
					</File>
				</Filter>
				<Filter
					Name="Source Files"
					>
					<File
						RelativePath=".\SQLParser\src\sql\CreateStatement.cpp"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\Expr.cpp"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\PrepareStatement.cpp"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\SQLStatement.cpp"
						>
					</File>
					<File
						RelativePath=".\SQLParser\src\sql\statements.cpp"
						>
					</File>
				</Filter>
			</Filter>
			<Filter
				Name="util"
				>
				<Filter
					Name="Header Files"
					>
					<File
						RelativePath=".\SQLParser\src\util\sqlhelper.h"
						>
					</File>
				</Filter>
				<Filter
					Name="Source Files"
					>
					<File
						RelativePath=".\SQLParser\src\util\sqlhelper.cpp"
						>
					</File>
				</Filter>
			</Filter>
		</Filter>
		<Filter
			Name="Logging"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\include\Poco\Data\ArchiveStrategy.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Data\SQLChannel.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\ArchiveStrategy.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SQLChannel.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<File
			RelativePath="..\DLLVersion.rc"
			>
			<FileConfiguration
				Name="debug_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="debug_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
