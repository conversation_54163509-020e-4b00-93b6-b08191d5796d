<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{4264152E-DD30-358F-8ADE-F360C0602A72}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>PocoDataSQLiteExternal</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PocoDataSQLiteExternal.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PocoDataSQLiteExternal</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PocoDataSQLiteExternal.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PocoDataSQLiteExternal</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">PocoDataSQLiteExternal.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">PocoDataSQLiteExternal</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">PocoDataSQLiteExternal.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">PocoDataSQLiteExternal</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Data\include;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4996;4244;4018</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_USRDLL;SQLite_EXPORTS;POCO_UNBUNDLED;SQLITE_THREADSAFE=1;SQLITE_ENABLE_FTS5;SQLITE_HAS_CODEC;_DEBUG;_CRT_SECURE_NO_WARNINGS;_SCL_SECURE_NO_WARNINGS;CMAKE_INTDIR="Debug";PocoDataSQLiteExternal_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_USRDLL;SQLite_EXPORTS;POCO_UNBUNDLED;SQLITE_THREADSAFE=1;SQLITE_ENABLE_FTS5;SQLITE_HAS_CODEC;_CRT_SECURE_NO_WARNINGS;_SCL_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Debug\";PocoDataSQLiteExternal_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Data\include;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Data\include;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\CWorkspace\poco-poco-1.14.2-master\lib64\PocoFoundationd.lib;D:\CWorkspace\poco-poco-1.14.2-master\lib64\PocoDatad.lib;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Debug\lib\sqlite3.lib;ws2_32.lib;iphlpapi.lib;crypt32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build/lib/Debug/PocoDataSQLiteExternal.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build/bin/Debug/PocoDataSQLiteExternal.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Data\include;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4996;4244;4018</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_USRDLL;SQLite_EXPORTS;POCO_UNBUNDLED;SQLITE_THREADSAFE=1;SQLITE_ENABLE_FTS5;SQLITE_HAS_CODEC;_CRT_SECURE_NO_WARNINGS;_SCL_SECURE_NO_WARNINGS;CMAKE_INTDIR="Release";PocoDataSQLiteExternal_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_USRDLL;SQLite_EXPORTS;POCO_UNBUNDLED;SQLITE_THREADSAFE=1;SQLITE_ENABLE_FTS5;SQLITE_HAS_CODEC;_CRT_SECURE_NO_WARNINGS;_SCL_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Release\";PocoDataSQLiteExternal_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Data\include;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Data\include;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\CWorkspace\poco-poco-1.14.2-master\lib64\PocoFoundationd.lib;D:\CWorkspace\poco-poco-1.14.2-master\lib64\PocoDatad.lib;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Debug\lib\sqlite3.lib;ws2_32.lib;iphlpapi.lib;crypt32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build/lib/Release/PocoDataSQLiteExternal.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build/bin/Release/PocoDataSQLiteExternal.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Data\include;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4996;4244;4018</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_USRDLL;SQLite_EXPORTS;POCO_UNBUNDLED;SQLITE_THREADSAFE=1;SQLITE_ENABLE_FTS5;SQLITE_HAS_CODEC;_CRT_SECURE_NO_WARNINGS;_SCL_SECURE_NO_WARNINGS;CMAKE_INTDIR="MinSizeRel";PocoDataSQLiteExternal_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_USRDLL;SQLite_EXPORTS;POCO_UNBUNDLED;SQLITE_THREADSAFE=1;SQLITE_ENABLE_FTS5;SQLITE_HAS_CODEC;_CRT_SECURE_NO_WARNINGS;_SCL_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"MinSizeRel\";PocoDataSQLiteExternal_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Data\include;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Data\include;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\CWorkspace\poco-poco-1.14.2-master\lib64\PocoFoundationd.lib;D:\CWorkspace\poco-poco-1.14.2-master\lib64\PocoDatad.lib;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Debug\lib\sqlite3.lib;ws2_32.lib;iphlpapi.lib;crypt32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build/lib/MinSizeRel/PocoDataSQLiteExternal.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build/bin/MinSizeRel/PocoDataSQLiteExternal.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Data\include;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4996;4244;4018</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_USRDLL;SQLite_EXPORTS;POCO_UNBUNDLED;SQLITE_THREADSAFE=1;SQLITE_ENABLE_FTS5;SQLITE_HAS_CODEC;_CRT_SECURE_NO_WARNINGS;_SCL_SECURE_NO_WARNINGS;CMAKE_INTDIR="RelWithDebInfo";PocoDataSQLiteExternal_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_USRDLL;SQLite_EXPORTS;POCO_UNBUNDLED;SQLITE_THREADSAFE=1;SQLITE_ENABLE_FTS5;SQLITE_HAS_CODEC;_CRT_SECURE_NO_WARNINGS;_SCL_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"RelWithDebInfo\";PocoDataSQLiteExternal_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Data\include;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Foundation\include;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\..\..\Data\include;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\CWorkspace\poco-poco-1.14.2-master\lib64\PocoFoundationd.lib;D:\CWorkspace\poco-poco-1.14.2-master\lib64\PocoDatad.lib;D:\BWorkspace\TestFindGame\Thirdparty\Sqlcipher\Debug\lib\sqlite3.lib;ws2_32.lib;iphlpapi.lib;crypt32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build/lib/RelWithDebInfo/PocoDataSQLiteExternal.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build/bin/RelWithDebInfo/PocoDataSQLiteExternal.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite -BD:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeCCompiler.cmake;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeCXXCompiler.cmake;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeRCCompiler.cmake;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeSystem.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompilerABI.c;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystem.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite -BD:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeCCompiler.cmake;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeCXXCompiler.cmake;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeRCCompiler.cmake;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeSystem.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompilerABI.c;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystem.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite -BD:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeCCompiler.cmake;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeCXXCompiler.cmake;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeRCCompiler.cmake;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeSystem.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompilerABI.c;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystem.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\SWEngineer\python311\Lib\site-packages\cmake\data\bin\cmake.exe -SD:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite -BD:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build --check-stamp-file D:/CWorkspace/poco-poco-1.14.2-master/Data/SQLite/final_build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeCCompiler.cmake;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeCXXCompiler.cmake;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeRCCompiler.cmake;D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\3.29.2\CMakeSystem.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCCompilerABI.c;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeRCInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystem.cmake.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\MSVC.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\Windows.cmake;D:\SWEngineer\python311\Lib\site-packages\cmake\data\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\src\Binder.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\src\Connector.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\src\Extractor.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\src\Notifier.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\src\SessionImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\src\SQLiteException.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\src\SQLiteStatementImpl.cpp" />
    <ClCompile Include="D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\src\Utility.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\CWorkspace\poco-poco-1.14.2-master\Data\SQLite\final_build\ZERO_CHECK.vcxproj">
      <Project>{B249CD61-FD80-396A-B95D-ADAD2CB0B8A4}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>