#line 1 "flex_lexer.cpp"

#line 3 "flex_lexer.cpp"

#if defined(_WIN32) || defined(_WIN64)
#pragma warning(disable : 4996)
#pragma warning(disable : 4267)
#endif

#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 6
#define YY_FLEX_SUBMINOR_VERSION 4
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

#ifdef yy_create_buffer
#define hsql__create_buffer_ALREADY_DEFINED
#else
#define yy_create_buffer hsql__create_buffer
#endif

#ifdef yy_delete_buffer
#define hsql__delete_buffer_ALREADY_DEFINED
#else
#define yy_delete_buffer hsql__delete_buffer
#endif

#ifdef yy_scan_buffer
#define hsql__scan_buffer_ALREADY_DEFINED
#else
#define yy_scan_buffer hsql__scan_buffer
#endif

#ifdef yy_scan_string
#define hsql__scan_string_ALREADY_DEFINED
#else
#define yy_scan_string hsql__scan_string
#endif

#ifdef yy_scan_bytes
#define hsql__scan_bytes_ALREADY_DEFINED
#else
#define yy_scan_bytes hsql__scan_bytes
#endif

#ifdef yy_init_buffer
#define hsql__init_buffer_ALREADY_DEFINED
#else
#define yy_init_buffer hsql__init_buffer
#endif

#ifdef yy_flush_buffer
#define hsql__flush_buffer_ALREADY_DEFINED
#else
#define yy_flush_buffer hsql__flush_buffer
#endif

#ifdef yy_load_buffer_state
#define hsql__load_buffer_state_ALREADY_DEFINED
#else
#define yy_load_buffer_state hsql__load_buffer_state
#endif

#ifdef yy_switch_to_buffer
#define hsql__switch_to_buffer_ALREADY_DEFINED
#else
#define yy_switch_to_buffer hsql__switch_to_buffer
#endif

#ifdef yypush_buffer_state
#define hsql_push_buffer_state_ALREADY_DEFINED
#else
#define yypush_buffer_state hsql_push_buffer_state
#endif

#ifdef yypop_buffer_state
#define hsql_pop_buffer_state_ALREADY_DEFINED
#else
#define yypop_buffer_state hsql_pop_buffer_state
#endif

#ifdef yyensure_buffer_stack
#define hsql_ensure_buffer_stack_ALREADY_DEFINED
#else
#define yyensure_buffer_stack hsql_ensure_buffer_stack
#endif

#ifdef yylex
#define hsql_lex_ALREADY_DEFINED
#else
#define yylex hsql_lex
#endif

#ifdef yyrestart
#define hsql_restart_ALREADY_DEFINED
#else
#define yyrestart hsql_restart
#endif

#ifdef yylex_init
#define hsql_lex_init_ALREADY_DEFINED
#else
#define yylex_init hsql_lex_init
#endif

#ifdef yylex_init_extra
#define hsql_lex_init_extra_ALREADY_DEFINED
#else
#define yylex_init_extra hsql_lex_init_extra
#endif

#ifdef yylex_destroy
#define hsql_lex_destroy_ALREADY_DEFINED
#else
#define yylex_destroy hsql_lex_destroy
#endif

#ifdef yyget_debug
#define hsql_get_debug_ALREADY_DEFINED
#else
#define yyget_debug hsql_get_debug
#endif

#ifdef yyset_debug
#define hsql_set_debug_ALREADY_DEFINED
#else
#define yyset_debug hsql_set_debug
#endif

#ifdef yyget_extra
#define hsql_get_extra_ALREADY_DEFINED
#else
#define yyget_extra hsql_get_extra
#endif

#ifdef yyset_extra
#define hsql_set_extra_ALREADY_DEFINED
#else
#define yyset_extra hsql_set_extra
#endif

#ifdef yyget_in
#define hsql_get_in_ALREADY_DEFINED
#else
#define yyget_in hsql_get_in
#endif

#ifdef yyset_in
#define hsql_set_in_ALREADY_DEFINED
#else
#define yyset_in hsql_set_in
#endif

#ifdef yyget_out
#define hsql_get_out_ALREADY_DEFINED
#else
#define yyget_out hsql_get_out
#endif

#ifdef yyset_out
#define hsql_set_out_ALREADY_DEFINED
#else
#define yyset_out hsql_set_out
#endif

#ifdef yyget_leng
#define hsql_get_leng_ALREADY_DEFINED
#else
#define yyget_leng hsql_get_leng
#endif

#ifdef yyget_text
#define hsql_get_text_ALREADY_DEFINED
#else
#define yyget_text hsql_get_text
#endif

#ifdef yyget_lineno
#define hsql_get_lineno_ALREADY_DEFINED
#else
#define yyget_lineno hsql_get_lineno
#endif

#ifdef yyset_lineno
#define hsql_set_lineno_ALREADY_DEFINED
#else
#define yyset_lineno hsql_set_lineno
#endif

#ifdef yyget_column
#define hsql_get_column_ALREADY_DEFINED
#else
#define yyget_column hsql_get_column
#endif

#ifdef yyset_column
#define hsql_set_column_ALREADY_DEFINED
#else
#define yyset_column hsql_set_column
#endif

#ifdef yywrap
#define hsql_wrap_ALREADY_DEFINED
#else
#define yywrap hsql_wrap
#endif

#ifdef yyget_lval
#define hsql_get_lval_ALREADY_DEFINED
#else
#define yyget_lval hsql_get_lval
#endif

#ifdef yyset_lval
#define hsql_set_lval_ALREADY_DEFINED
#else
#define yyset_lval hsql_set_lval
#endif

#ifdef yyget_lloc
#define hsql_get_lloc_ALREADY_DEFINED
#else
#define yyget_lloc hsql_get_lloc
#endif

#ifdef yyset_lloc
#define hsql_set_lloc_ALREADY_DEFINED
#else
#define yyset_lloc hsql_set_lloc
#endif

#ifdef yyalloc
#define hsql_alloc_ALREADY_DEFINED
#else
#define yyalloc hsql_alloc
#endif

#ifdef yyrealloc
#define hsql_realloc_ALREADY_DEFINED
#else
#define yyrealloc hsql_realloc
#endif

#ifdef yyfree
#define hsql_free_ALREADY_DEFINED
#else
#define yyfree hsql_free
#endif

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>

/* end standard C headers. */

/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L

/* C99 says to define __STDC_LIMIT_MACROS before including stdint.h,
 * if you want the limit (max/min) macros for int types. 
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t; 
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

#if (__cplusplus >= 201703L)
#if !__has_include(<stdint.h>)
/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#ifndef SIZE_MAX
#define SIZE_MAX               (~(size_t)0)
#endif
#endif // (__cplusplus >= 201703L)
#endif // !__has_include(<stdint.h>)
#endif /* ! C99 */

#endif /* ! FLEXINT_H */

/* begin standard C++ headers. */

/* TODO: this is always defined, so inline it */
#define yyconst const

#if defined(__GNUC__) && __GNUC__ >= 3
#define yynoreturn __attribute__((__noreturn__))
#else
#define yynoreturn
#endif

/* Returned upon end-of-file. */
#define YY_NULL 0

/* Promotes a possibly negative, possibly signed char to an
 *   integer in range [0..255] for use as an array index.
 */
#define YY_SC_TO_UI(c) ((YY_CHAR) (c))

/* An opaque pointer. */
#ifndef YY_TYPEDEF_YY_SCANNER_T
#define YY_TYPEDEF_YY_SCANNER_T
typedef void* yyscan_t;
#endif

/* For convenience, these vars (plus the bison vars far below)
   are macros in the reentrant scanner. */
#define yyin yyg->yyin_r
#define yyout yyg->yyout_r
#define yyextra yyg->yyextra_r
#define yyleng yyg->yyleng_r
#define yytext yyg->yytext_r
#define yylineno (YY_CURRENT_BUFFER_LVALUE->yy_bs_lineno)
#define yycolumn (YY_CURRENT_BUFFER_LVALUE->yy_bs_column)
#define yy_flex_debug yyg->yy_flex_debug_r

/* Enter a start condition.  This macro really ought to take a parameter,
 * but we do it the disgusting crufty way forced on us by the ()-less
 * definition of BEGIN.
 */
#define BEGIN yyg->yy_start = 1 + 2 *
/* Translate the current start state into a value that can be later handed
 * to BEGIN to return to the state.  The YYSTATE alias is for lex
 * compatibility.
 */
#define YY_START ((yyg->yy_start - 1) / 2)
#define YYSTATE YY_START
/* Action number for EOF rule of a given start state. */
#define YY_STATE_EOF(state) (YY_END_OF_BUFFER + state + 1)
/* Special action meaning "start processing a new file". */
#define YY_NEW_FILE yyrestart( yyin , yyscanner )
#define YY_END_OF_BUFFER_CHAR 0

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

/* The state buf must be large enough to hold one state per character in the main buffer.
 */
#define YY_STATE_BUF_SIZE   ((YY_BUF_SIZE + 2) * sizeof(yy_state_type))

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

#define EOB_ACT_CONTINUE_SCAN 0
#define EOB_ACT_END_OF_FILE 1
#define EOB_ACT_LAST_MATCH 2
    
    #define YY_LESS_LINENO(n)
    #define YY_LINENO_REWIND_TO(ptr)
    
/* Return all but the first "n" matched characters back to the input stream. */
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		*yy_cp = yyg->yy_hold_char; \
		YY_RESTORE_YY_MORE_OFFSET \
		yyg->yy_c_buf_p = yy_cp = yy_bp + yyless_macro_arg - YY_MORE_ADJ; \
		YY_DO_BEFORE_ACTION; /* set up yytext again */ \
		} \
	while ( 0 )
#define unput(c) yyunput( c, yyg->yytext_ptr , yyscanner )

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
	{
	FILE *yy_input_file;

	char *yy_ch_buf;		/* input buffer */
	char *yy_buf_pos;		/* current position in input buffer */

	/* Size of input buffer in bytes, not including room for EOB
	 * characters.
	 */
	int yy_buf_size;

	/* Number of characters read into yy_ch_buf, not including EOB
	 * characters.
	 */
	int yy_n_chars;

	/* Whether we "own" the buffer - i.e., we know we created it,
	 * and can realloc() it to grow it, and should free() it to
	 * delete it.
	 */
	int yy_is_our_buffer;

	/* Whether this is an "interactive" input source; if so, and
	 * if we're using stdio for input, then we want to use getc()
	 * instead of fread(), to make sure we stop fetching input after
	 * each newline.
	 */
	int yy_is_interactive;

	/* Whether we're considered to be at the beginning of a line.
	 * If so, '^' rules will be active on the next match, otherwise
	 * not.
	 */
	int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */

	/* Whether to try to fill the input buffer when we reach the
	 * end of it.
	 */
	int yy_fill_buffer;

	int yy_buffer_status;

#define YY_BUFFER_NEW 0
#define YY_BUFFER_NORMAL 1
	/* When an EOF's been seen but there's still some text to process
	 * then we mark the buffer as YY_EOF_PENDING, to indicate that we
	 * shouldn't try reading from the input source any more.  We might
	 * still have a bunch of tokens to match, though, because of
	 * possible backing-up.
	 *
	 * When we actually see the EOF, we change the status to "new"
	 * (via yyrestart()), so that the user can continue scanning by
	 * just pointing yyin at a new input file.
	 */
#define YY_BUFFER_EOF_PENDING 2

	};
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

/* We provide macros for accessing buffer states in case in the
 * future we want to put the buffer states in a more general
 * "scanner state".
 *
 * Returns the top of the stack, or NULL.
 */
#define YY_CURRENT_BUFFER ( yyg->yy_buffer_stack \
                          ? yyg->yy_buffer_stack[yyg->yy_buffer_stack_top] \
                          : NULL)
/* Same as previous macro, but useful when we know that the buffer stack is not
 * NULL or when we need an lvalue. For internal use only.
 */
#define YY_CURRENT_BUFFER_LVALUE yyg->yy_buffer_stack[yyg->yy_buffer_stack_top]

void yyrestart ( FILE *input_file , yyscan_t yyscanner );
void yy_switch_to_buffer ( YY_BUFFER_STATE new_buffer , yyscan_t yyscanner );
YY_BUFFER_STATE yy_create_buffer ( FILE *file, int size , yyscan_t yyscanner );
void yy_delete_buffer ( YY_BUFFER_STATE b , yyscan_t yyscanner );
void yy_flush_buffer ( YY_BUFFER_STATE b , yyscan_t yyscanner );
void yypush_buffer_state ( YY_BUFFER_STATE new_buffer , yyscan_t yyscanner );
void yypop_buffer_state ( yyscan_t yyscanner );

static void yyensure_buffer_stack ( yyscan_t yyscanner );
static void yy_load_buffer_state ( yyscan_t yyscanner );
static void yy_init_buffer ( YY_BUFFER_STATE b, FILE *file , yyscan_t yyscanner );
#define YY_FLUSH_BUFFER yy_flush_buffer( YY_CURRENT_BUFFER , yyscanner)

YY_BUFFER_STATE yy_scan_buffer ( char *base, yy_size_t size , yyscan_t yyscanner );
YY_BUFFER_STATE yy_scan_string ( const char *yy_str , yyscan_t yyscanner );
YY_BUFFER_STATE yy_scan_bytes ( const char *bytes, int len , yyscan_t yyscanner );

void *yyalloc ( yy_size_t , yyscan_t yyscanner );
void *yyrealloc ( void *, yy_size_t , yyscan_t yyscanner );
void yyfree ( void * , yyscan_t yyscanner );

#define yy_new_buffer yy_create_buffer
#define yy_set_interactive(is_interactive) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){ \
        yyensure_buffer_stack (yyscanner); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE , yyscanner); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_is_interactive = is_interactive; \
	}
#define yy_set_bol(at_bol) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){\
        yyensure_buffer_stack (yyscanner); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE , yyscanner); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_at_bol = at_bol; \
	}
#define YY_AT_BOL() (YY_CURRENT_BUFFER_LVALUE->yy_at_bol)

/* Begin user sect3 */

#define hsql_wrap(yyscanner) (/*CONSTCOND*/1)
#define YY_SKIP_YYWRAP
typedef flex_uint8_t YY_CHAR;

typedef int yy_state_type;

#define yytext_ptr yytext_r

static yy_state_type yy_get_previous_state ( yyscan_t yyscanner );
static yy_state_type yy_try_NUL_trans ( yy_state_type current_state  , yyscan_t yyscanner);
static int yy_get_next_buffer ( yyscan_t yyscanner );
static void yynoreturn yy_fatal_error ( const char* msg , yyscan_t yyscanner );

/* Done after the current pattern has been matched and before the
 * corresponding action - sets up yytext.
 */
#define YY_DO_BEFORE_ACTION \
	yyg->yytext_ptr = yy_bp; \
	yyleng = (int) (yy_cp - yy_bp); \
	yyg->yy_hold_char = *yy_cp; \
	*yy_cp = '\0'; \
	yyg->yy_c_buf_p = yy_cp;
#define YY_NUM_RULES 186
#define YY_END_OF_BUFFER 187
/* This struct is not used in this scanner,
   but its presence is necessary. */
struct yy_trans_info
	{
	flex_int32_t yy_verify;
	flex_int32_t yy_nxt;
	};
static const flex_int16_t yy_accept[1332] =
    {   0,
        0,    0,  183,  183,    2,    2,  187,  185,    4,    4,
      185,  185,  174,  181,  174,  174,  178,  174,  174,  174,
      180,  180,  180,  180,  180,  180,  180,  180,  180,  180,
      180,  180,  180,  180,  180,  180,  180,  180,  180,  180,
      180,  180,  180,  180,  174,  183,  184,    2,    2,    3,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    4,  169,    0,    1,    0,
      176,  175,  178,  171,  170,  168,  172,  180,  180,  180,

      180,  180,  180,   12,  180,  180,  180,   19,  180,  180,
      180,  180,  180,  180,  180,  180,  180,  180,  180,  180,
      180,  180,  180,  180,  180,  180,  180,  180,  180,  180,
      180,  180,  180,   71,  180,  180,   74,   83,  180,  180,
      180,  180,  180,  180,  180,  180,  180,  101,  180,  180,
      106,  109,  110,  180,  180,  180,  180,  180,  180,  180,
      180,  180,  180,  180,  180,  180,  180,  180,  180,  180,
      180,  180,  180,  146,  180,  180,  180,  180,  180,  180,
      180,  180,  180,  173,  183,  182,    2,    2,    2,    2,
        1,    2,    2,    2,    2,    2,    2,    2,    2,    2,

        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,  179,    0,  175,    5,
      180,    7,  180,  180,   10,  180,   13,  180,  180,  180,

      180,  180,  180,  180,  180,  180,  180,  180,  180,  180,
      180,  180,  180,   34,  180,  180,  180,  180,  180,  180,
      180,  180,  180,  180,   48,  180,  180,  180,  180,  180,
      180,  180,  180,  180,  180,   59,  180,  180,  180,  180,
      180,  180,  180,  180,  180,  180,  180,  180,  180,   78,
      180,  180,   86,  180,  180,  180,  180,  180,  180,  180,
      180,  180,  180,  102,  180,  180,  180,  107,  180,  180,
      180,  180,  180,  180,  180,  180,  180,  180,  180,  180,
      180,  180,  180,  180,  180,  132,  180,  180,  180,  180,
      180,  180,  180,  180,  180,  180,  180,  147,  180,  180,

      180,  180,  180,  180,  180,  180,  180,  180,  180,  180,
      180,  180,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,

        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    0,  180,
      180,  180,  180,  180,  180,  180,  180,  180,   20,  180,
       22,   23,   24,  180,  180,  180,   29,  180,  180,  180,
       32,   35,  180,  180,  180,  180,  180,   41,  180,  180,
      180,   46,   47,  180,  180,  180,  180,  180,  180,  180,
       56,  180,  180,  180,   61,   62,  180,  180,   66,  180,
       68,   69,  180,  180,  180,  180,  180,  180,   82,  180,

       85,   87,   88,  180,   90,  180,  180,   93,  180,  180,
      180,  180,  180,  104,  180,  180,  180,  180,  113,  180,
      180,  116,  180,  180,  180,  180,  121,  180,  180,  180,
      180,  126,  180,  180,  180,  180,  134,  135,  180,  180,
      180,  180,  180,  142,  143,  144,  180,  149,  180,  180,
      180,  180,  180,  180,  180,  180,  180,  159,  180,  161,
      180,  163,  164,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,

        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    0,    6,
        8,  180,   11,  180,   15,  180,  180,  180,  180,  180,

      180,  180,  180,  180,   31,  180,  180,  180,  180,  180,
      180,   40,  180,  180,  180,  180,  180,  180,  180,  180,
      180,  180,   55,   57,  180,  180,  180,   64,  180,   70,
       72,  180,   75,   76,  180,  180,  180,  180,   89,   91,
      180,   94,   95,  180,   98,  180,  180,  180,  180,  111,
      112,  180,  180,  180,  180,  180,  120,  180,  180,  124,
      180,  180,  180,  180,  133,  180,  180,  180,  139,  180,
      180,  180,  180,  180,  152,  180,  180,  180,  156,  180,
      180,  180,  162,  165,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,

        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        0,  180,   14,  180,   17,  180,  180,  180,   25,   27,
      180,   30,  180,  180,  180,  180,  180,   39,  180,   43,

      180,   45,   49,   50,  180,   52,  180,  180,  180,   60,
       63,   65,   67,   73,   77,  180,  180,  180,   84,   92,
       96,   99,  180,  103,  180,  108,  180,  180,  180,  180,
      180,  122,  180,  180,  127,  129,  131,  180,  137,  180,
      140,  180,  180,  180,  180,  180,  153,  154,  155,  157,
      180,  180,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,

        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    0,    9,   16,   18,   21,  180,
       26,   28,  180,  180,  180,   37,   38,  180,  180,   51,
       53,   54,  180,   79,  180,  180,   97,  100,  180,  180,
      180,  180,  118,  119,  180,  180,  128,  130,  180,  138,
      180,  180,  180,  180,  180,  158,  160,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,

        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        0,  180,    0,   33,  180,   42,   44,  180,  180,   81,
      105,  180,  180,  180,  123,  125,  136,  180,  180,  180,
      150,  180,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    0,  180,    0,  180,   58,   80,
      180,  115,  117,  141,  145,  180,  151,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        0,    0,    0,   36,  114,  180,    2,    2,    2,    2,
        2,    2,    0,    0,  166,  148,    2,    2,    2,    2,

        0,    0,    2,    2,    0,    0,    2,    2,    0,    0,
        2,    2,    0,    0,    2,    2,    0,    0,    2,    2,
        0,  167,    2,    2,    0,    2,    0,    2,  177,    2,
        0
    } ;

static const YY_CHAR yy_ec[256] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    2,    3,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    2,    4,    5,    1,    1,    6,    1,    7,    6,
        6,    6,    6,    6,    8,    9,    6,   10,   11,   12,
       13,   14,   15,   16,   17,   18,   19,    6,    6,   20,
       21,   22,    6,    1,   23,   24,   25,   26,   27,   28,
       29,   30,   31,   32,   33,   34,   35,   36,   37,   38,
       39,   40,   41,   42,   43,   44,   45,   46,   47,   48,
        6,    1,    6,    6,   49,    1,   50,   51,   52,   53,

       54,   55,   56,   57,   58,   59,   60,   61,   62,   63,
       64,   65,   66,   67,   68,   69,   70,   71,   72,   73,
       74,   75,    6,   76,    6,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,

        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1
    } ;

static const YY_CHAR yy_meta[77] =
    {   0,
        1,    1,    2,    1,    3,    1,    4,    1,    1,    5,
        5,    5,    5,    5,    5,    5,    5,    5,    5,    1,
        1,    1,    5,    5,    5,    5,    5,    5,    5,    5,
        5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
        5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
        5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
        5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
        5,    5,    5,    5,    5,    1
    } ;

static const flex_int16_t yy_base[1339] =
    {   0,
        0,    0,  839,  837,   76,    0,  842, 8771,  151,  153,
      785,    0, 8771, 8771,  149,  148,  160,  159,  783,  782,
      156,  156,  165,  210,  202,  255,  151,  163,  265,  152,
      171,  215,  218,  244,  295,  257,    0,  309,  349,  392,
      163,  279,  226,  180,  723,    0,  791,    0,  237,  251,
      775,  743,    0,    0,  243,  378,  451,  237,  682,  680,
      470,  546,  600,  652,  700,  752,  384,  458,  795,  466,
      532,  533,  534,  846,  895,  944,  547,  602,  990, 1042,
      307,  650,  587,  651,  596,  301, 8771,  666, 8771,  657,
     1107, 1117, 1128, 8771, 8771, 8771, 8771,    0,  218,  243,

      300,  328,  250,  305,  379,  315,  319,    0,  376,  354,
      694,  389,  345,  440,  710,  387,  388,  436,  439,  462,
      469,  763,  465,  467,  465,  542,  480,  484,  498,  521,
      531,  553,  572,    0,  588,  588,  654,  592,  601,  588,
      608,  651,  717,  598,  649,  655,  651,  707,  660,  716,
      715,    0,  719,  708,  754,  744,  762,  760,  757,  802,
      768,  792,  770,  846,  810,  776,  812,  799,  817,  821,
      838,  821,  816,  816,  854,  861,  829,  848,  853,  894,
      872,  859,  879, 8771,    0, 8771,    0,  396,    0,  663,
        0,  636, 1138, 1148, 1159,    0,    0,    0,    0,  906,

      942,  955, 1015, 1156, 1034, 1155, 1202, 1152, 1040, 1195,
     1199, 1232, 1272, 1257, 1272, 1301, 1353, 1350, 1361, 1388,
     1402, 1413, 1443, 1492, 1437, 1479, 1491, 1528, 1536, 1536,
     1561, 1575, 1577, 1590, 1618, 1621, 1629, 1668, 1717, 1639,
     1682, 1683, 1707, 1764, 1816, 1762, 1714, 1808, 1817, 1857,
     1860, 1881, 1904, 1873, 1917, 1940, 1773, 1952, 1984, 1993,
     1991, 2027, 2040, 2046, 2076, 2125, 2098, 2089, 2137, 2138,
     2176, 2173, 2207, 2191, 2225, 2247, 2263, 2310, 2271, 2295,
     2314, 2325, 2339, 2363, 2375,    0, 8771,  601, 2440,    0,
      882,    0,  900,  896,    0,  921,    0,  912,  925,  918,

      941,  943,  951, 1158,  951,  960,  972,  984,  984, 1009,
      992,  995, 1009,  998, 1047, 1054, 1063, 1210, 1159, 1160,
     1152, 1210, 1209, 1223,    0, 1231, 1229, 1262, 1248, 1256,
     1256, 1256, 1271, 1279, 1286, 1287, 1292, 1307, 1318, 1301,
     1320, 1321, 1311, 1316, 1325, 1324, 1339, 1346, 1347, 1403,
     1339, 1370,    0, 1368, 1401, 1414, 1427, 1464, 1429, 1432,
     1421, 1427, 1439,    0, 1471, 1465, 1468, 1486, 1504, 1505,
     1493, 1648, 1512, 1555, 1516, 1547, 1553, 1582, 1574, 1587,
     1607, 1601, 1620, 1628, 1643,    0, 1637, 1651, 1663, 1684,
     1683, 1697, 1715, 1729, 1737, 1752, 1762,    0, 1756, 1807,

     1768, 1809, 1772, 1790, 1797, 1802, 1830, 1811, 1823, 1872,
     1867, 1870,    0,  600, 2450, 2443, 2444, 2445, 2446, 2373,
     2480, 2488, 2489, 2500, 2528, 2536, 2541, 2543, 2557, 2606,
     2582, 2607, 2620, 2656, 2632, 2668, 2666, 2697, 2709, 2711,
     2725, 2750, 2768, 2765, 2800, 2809, 2818, 2844, 2859, 2857,
     2862, 2907, 2898, 2912, 2912, 2933, 2957, 2954, 2982, 3008,
     2980, 3029, 3033, 3037, 3075, 3079, 3078, 3123, 3126, 3135,
     3161, 3176, 3179, 3191, 3205, 3217, 3229, 3244, 3248, 3272,
     3273, 3292, 3317, 3369, 3295, 3331, 3366, 3330, 3391, 3394,
     3419, 3426, 3440, 3454, 3465, 3479, 3494, 3546, 3494, 3596,

     3529, 3572, 3513, 3624, 3583, 3632, 3645, 3668, 3680, 3685,
     3688, 3693, 3729, 3734, 3737, 3736, 3744, 3780, 3784, 3793,
     3826, 3829, 3846, 3849, 3875, 3878, 3898, 3903, 3900, 3948,
     3919, 3947, 3975, 3989, 4003, 4019, 4033, 4057,  582, 1908,
     1917, 1913, 1914, 1924, 1930, 1941, 1933, 1944,    0, 1950,
        0,    0, 1979, 1977, 1983, 1977,    0, 1976, 1981, 2001,
     1991,    0, 2001, 2008, 2013, 2024, 2048, 2032, 2050, 2047,
     2050,    0,    0, 2059, 2066, 2072, 2085, 2105, 2107, 2119,
        0, 2137, 2144, 2159,    0,    0, 2166, 2152,    0, 2183,
        0, 2191, 2209, 2199, 2198, 2214, 2221, 2311,    0, 2230,

        0,    0,    0, 2235,    0, 2248, 2263,    0, 2265, 2374,
     2266, 2276, 2300,    0, 2311, 2319, 2351, 2356,    0, 2366,
     2373,    0, 2381, 2388, 2397, 2451,    0, 2444, 2445, 2444,
     2464,    0, 2498, 2503, 2517, 2517,    0,    0, 2536, 2553,
     2554, 2560, 2559,    0,    0, 2560, 2594,    0, 2614, 2598,
     2609, 2646, 2669, 2655, 2672, 2681, 2687,    0, 2678,    0,
     2696,    0, 2700,  544, 4068, 4071, 4096, 4115, 4118, 4125,
     4120, 4161, 4169, 4172, 4181, 4210, 4211, 4229, 4251, 4263,
     4264, 4265, 4293, 4300, 4317, 4318, 4343, 4343, 4359, 4390,
     4401, 4425, 4443, 4451, 4468, 4484, 4487, 4493, 4529, 4535,

     4543, 4573, 4576, 4597, 4504, 4598, 4624, 4626, 4652, 4650,
     4664, 4682, 4696, 4694, 4701, 4731, 4737, 4748, 4749, 4783,
     4791, 4794, 4839, 4835, 4836, 4877, 4880, 4881, 4921, 4924,
     4925, 4964, 4969, 4972, 4971, 4974, 5014, 5014, 5022, 5058,
     5066, 5067, 5096, 5108, 5111, 5120, 5149, 5150, 5168, 5194,
     5202, 5203, 5216, 5242, 5244, 5268, 5256, 5290, 5295, 5299,
     5312, 5334, 5342, 5341, 5347, 5366, 5388, 5391, 5396, 5410,
     5398, 5417, 5441, 5447, 5455, 5471, 5490, 5504, 5501, 5532,
     5544, 5549, 5583, 5584, 5585, 5597, 5598, 5629,  543,    0,
        0, 2701,    0, 2724,    0, 2726, 2713, 2739, 2764, 2769,

     2765, 2761, 2769, 2784,    0, 2780, 2804, 2813, 2828, 2819,
     2858,    0, 2869, 2859, 2869, 2887, 2923, 2909, 2913, 2919,
     2952, 2959,    0,    0, 2960, 2965, 2989, 2992, 3020,    0,
        0, 3012,    0,    0, 3015, 3032, 3036, 3027,    0,    0,
     3077,    0,    0, 3090, 3077, 3105, 3087, 3101, 3090,    0,
        0, 3107, 3095, 3143, 3134, 3138,    0, 3158, 3173,    0,
     3191, 3197, 3198, 3204,    0, 3230, 3242, 3247, 3233, 3258,
     3286, 3313, 3315, 3316,    0, 3347, 3354, 3356,    0, 3349,
     3385, 3389,    0,    0,  536, 5632, 5633, 5673, 5674, 5685,
     5686, 5724, 5725, 5743, 5738, 5778, 5770, 5793, 5810, 5813,

     5818, 5812, 5832, 5862, 5871, 5869, 5870, 5896, 5920, 5921,
     5925, 5945, 5950, 5969, 5994, 5971, 6018, 6042, 6023, 6064,
     6072, 6098, 6025, 6107, 6113, 6137, 6143, 6151, 6167, 6181,
     6195, 6220, 6209, 6225, 6249, 6255, 6273, 6279, 6297, 6308,
     6315, 6345, 6351, 6364, 6392, 6395, 6406, 6412, 6446, 6454,
     6482, 6490, 6504, 6516, 6528, 6540, 6558, 6570, 6584, 6598,
     6572, 6596, 6637, 6628, 6635, 6653, 6671, 6695, 6701, 6714,
     6725, 6752, 6754, 6778, 6786, 6788, 6807, 6831, 6828, 6837,
      526, 3399,    0, 3396,    0, 3401, 3417, 3409, 3416,    0,
     3436,    0, 3440, 3456, 3480, 3477, 3482,    0, 3520,    0,

     3520,    0,    0,    0, 3533,    0, 3542, 3538, 3551,    0,
        0,    0,    0,    0,    0, 3544, 3558, 3566,    0,    0,
     3554,    0, 3565,    0, 3591,    0, 3581, 3596, 3605, 3622,
     3606,    0, 3630, 3642, 3630, 3631,    0, 3647,    0, 3662,
        0, 3678, 3698, 3701, 3731, 3784,    0,    0,    0,    0,
     3775, 3785,  525, 6866, 6875, 6873, 6882, 6891, 6884, 6920,
     6927, 6935, 6936, 6971, 6977, 6980, 7022, 7018, 7031, 7062,
     7073, 7076, 7104, 7117, 7118, 7148, 7156, 7161, 7161, 7197,
     7205, 7210, 7224, 7246, 7249, 7254, 7268, 7290, 7298, 7316,
     7324, 7338, 7343, 7359, 7367, 7373, 7403, 7397, 7421, 7441,

     7446, 7454, 7476, 7490, 7504, 7518, 7525, 7533, 7539, 7563,
     7569, 7577, 7593, 7612, 7636, 7635, 7659, 7680, 7677, 7683,
     7701, 7724, 7727, 7743,  529,    0,    0,    0,    0, 3796,
        0,    0,  436, 3797, 3805,    0,    0, 3805, 3802,    0,
        0,    0, 3828,    0, 3849, 3845,    0,    0, 3841, 3861,
     3880, 3886,    0,    0, 3917, 3937,    0,    0, 3934,    0,
     3938, 3956, 3957, 3974, 3977,    0,    0,  527, 7757, 7771,
     7785, 7799, 7620, 7810, 7824, 7863, 7848, 7866, 7878, 7892,
     7904, 7907, 7916, 7942, 7955, 7957, 7971, 7990, 7987, 8011,
     8020, 8046, 8058, 8063, 8062, 8065, 8095, 8106, 8137, 8119,

     8150, 8173, 8186, 8197, 8194, 8227, 8244, 8245, 8247, 8279,
      526, 3973, 4081,    0, 3972,    0,    0, 3990, 3982,    0,
        0, 3989, 4018, 4029,    0,    0,    0, 4021, 4035, 4057,
        0, 4063,  524, 8298, 4259, 8293, 8317, 8342, 8343, 8356,
     8357, 8371, 8396, 8397, 8406, 8412, 8442, 8448, 8456, 8481,
     8495, 8500, 8503, 8525,  514,  438, 4062, 4083,    0,    0,
     4072,    0,    0,    0,    0, 4116,    0,  506, 8563, 4119,
     8544, 8549, 8578, 8551, 8570, 8594, 8605, 8613, 8633, 8638,
      470, 4161, 4142,    0,    0, 4154,  468, 4396, 4168, 8652,
     8657, 8659,  465, 4221, 8771,    0,  418, 4227,    0, 8667,

      418, 4213,  390, 4214,  388, 4223,  386, 4242,  347, 4275,
      343, 4301,  338, 4306,  335, 4320,  334, 4329,  332, 4357,
      303, 8771,  293,    0,  299,  286,  248,  243, 8771,    0,
     8771, 8740, 8745,  201, 8750, 8755, 8760, 8765
    } ;

static const flex_int16_t yy_def[1339] =
    {   0,
     1331,    1, 1332, 1332, 1331,    5, 1331, 1331, 1331, 1331,
     1331, 1333, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1331, 1335, 1331, 1336, 1336, 1331,
     1336, 1337, 1336, 1336, 1336, 1336, 1336, 1336, 1336, 1336,
     1338, 1338,   62,   62,   62,   63,   65,   62,   65,   62,
       62,   62,   62,   63,   63,   63,   62,   62,   62,   62,
       65,   62,   62,   62, 1336, 1331, 1331, 1333, 1331, 1331,
     1331, 1331, 1331, 1331, 1331, 1331, 1331, 1334, 1334, 1334,

     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1331, 1335, 1331, 1336, 1336, 1336, 1337,
     1336, 1336, 1336, 1336, 1336, 1336, 1336, 1336, 1336,   62,

       62,   62,   65,   65,   65,   65,   65,   65,   62,   62,
       65,   65,   65,   62,   62,   62,   65,   65,   65,   62,
       65,   65,   65,   62,   65,   65,   62,   65,   62,   65,
       62,   62,   65,   65,   65,   65,   62,   62,   65,   65,
       62,   62,   62,   62,   65,   65,   65,   65,   65,   65,
       65,   65,   65,   65,   65,   65,   62,   62,   62,   62,
       65,   65,   65,   65,   65,   65,   62,   62,   62,   62,
       62,   62,   65,   62,   62,   62,   63,   62,   62,   62,
       65,   62,   62,   62,   62, 1336, 1331, 1331, 1331, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,

     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,

     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1336, 1336, 1336,   62,   62,   62,   62,   65,
       65,   65,   65,   62,   62,   62,   62,   65,   65,   62,
       62,   62,   62,   62,   62,   62,   65,   65,   62,   65,
       65,   62,   62,   62,   62,   62,   62,   62,   62,   62,
       62,   62,   62,   62,   65,   65,   65,   65,   62,   62,
       65,   65,   65,   65,   65,   65,   65,   62,   62,   62,
       62,   62,   62,   62,   62,   62,   62,   65,   65,   65,
       62,   62,   62,   62,   62,   62,   62,   62,   62,   62,
       62,   65,   65,   65,   62,   62,   62,   62,   65,   65,

       65,   65,   65,   65,   65,   65,   65,   65,   62,   62,
       62,   62,   62,   62,   62,   65,   65,   65,   65,   65,
       65,   65,   62,   62,   65,   65,   62,   62,   62,   62,
       65,   65,   65,   65,   65,   65,   65,   65, 1331, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,

     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1336,   65,   65,   62,   62,   62,   65,
       62,   65,   62,   62,   62,   62,   62,   62,   62,   62,
       62,   62,   62,   65,   62,   62,   62,   65,   65,   65,
       65,   65,   65,   65,   62,   65,   65,   65,   65,   65,

       65,   65,   65,   65,   62,   62,   62,   62,   62,   62,
       62,   62,   62,   62,   65,   65,   65,   62,   62,   65,
       65,   65,   65,   65,   65,   65,   65,   65,   65,   65,
       65,   62,   62,   62,   65,   65,   65,   62,   62,   62,
       62,   62,   62,   62,   62,   62,   62,   62,   62,   62,
       62,   62,   62,   62,   62,   62,   62,   62,   65,   65,
       62,   62,   62,   65,   62,   62,   62,   62,   62,   62,
       65,   65,   65,   65,   65,   65,   65,   65,   65,   65,
       62,   62,   62,   62,   62,   62,   62,   65, 1331, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,

     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1336,   65,   65,   65,   65,   62,
       62,   62,   62,   62,   62,   62,   65,   65,   62,   62,

       62,   65,   62,   62,   62,   65,   62,   62,   62,   62,
       65,   62,   62,   62,   62,   65,   62,   62,   62,   62,
       62,   62,   65,   65,   65,   65,   65,   65,   65,   65,
       65,   62,   65,   65,   65,   65,   65,   65,   65,   62,
       65,   65,   65,   65,   65,   65,   65,   62,   62,   62,
       62,   62,   62,   62,   62,   62,   62,   62,   62,   62,
       62,   62,   62,   62,   65,   65,   65,   65,   65,   65,
       65,   62,   62,   62,   62,   65,   65,   65,   65,   65,
     1331, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,

     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1336,   62,   62,   65,   65,   65,   62,   62,
       65,   65,   65,   65,   65,   65,   65,   65,   65,   65,
       65,   65,   65,   65,   65,   65,   62,   62,   65,   65,
       62,   62,   62,   62,   62,   62,   62,   62,   62,   62,
       62,   62,   65,   65,   65,   65,   65,   65,   65,   62,

       62,   62,   62,   62,   62,   62,   65,   65,   65,   65,
       65,   65,   65,   65,   65,   65,   65,   65,   65,   65,
       65,   65,   65,   65, 1331, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1336,   65,   65,
       65,   65,   62,   62,   62,   62,   62,   62,   62,   62,
       62,   62,   62,   62,   62,   65,   65,   65,   65,   65,
       65,   65,   62,   62,   65,   65,   65,   65,   65,   65,

       65,   65,   65,   65,   65,   65,   62,   62,   62,   62,
     1331, 1334, 1331, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334, 1334,
     1334, 1334, 1336,   62, 1336,   62,   62,   62,   62,   62,
       62,   62,   62,   62,   65,   65,   65,   65,   65,   62,
       62,   62,   62,   62, 1331, 1334, 1331, 1334, 1334, 1334,
     1334, 1334, 1334, 1334, 1334, 1334, 1334, 1336,   62, 1336,
       62,   62,   62,   65,   65,   65,   65,   65,   62,   62,
     1331, 1331, 1331, 1334, 1334, 1334, 1336, 1336, 1336,   62,
       62,   65, 1331, 1331, 1331, 1334, 1336, 1336, 1336,   65,

     1331, 1331, 1336, 1336, 1331, 1331, 1336, 1336, 1331, 1331,
     1336, 1336, 1331, 1331, 1336, 1336, 1331, 1331, 1336, 1336,
     1331, 1331, 1336, 1336, 1331, 1336, 1331, 1336, 1331, 1336,
        0, 1331, 1331, 1331, 1331, 1331, 1331, 1331
    } ;

static const flex_int16_t yy_nxt[8848] =
    {   0,
        8,    9,   10,   11,   12,   13,   14,   15,   16,   17,
       17,   17,   17,   17,   17,   17,   17,   17,   17,   18,
       19,   20,   21,   22,   23,   24,   25,   26,   27,   28,
       29,   30,   31,   32,   33,   34,   35,   36,   37,   38,
       39,   40,   41,   42,   43,   37,   44,   37,    8,   21,
       22,   23,   24,   25,   26,   27,   28,   29,   30,   31,
       32,   33,   34,   35,   36,   37,   38,   39,   40,   41,
       42,   43,   37,   44,   37,   45,   48,   49,   50,   51,
       52,   53,   54,   55,   56,   57,   57,   57,   57,   57,
       57,   57,   57,   57,   57,   58,   59,   60,   61,   62,

       63,   64,   65,   66,   67,   68,   69,   70,   71,   72,
       73,   74,   75,   76,   77,   78,   79,   80,   81,   82,
       83,   77,   84,   77,   48,   61,   62,   63,   64,   65,
       66,   67,   68,   69,   70,   71,   72,   73,   74,   75,
       76,   77,   78,   79,   80,   81,   82,   83,   77,   84,
       77,   85,   86,   86,   86,   86,   89,   91,   91,   91,
       91,   91,   91,   91,   91,   91,   91,   90,   92,   93,
       93,   93,   93,   93,   93,   93,   93,   93,   93,   94,
       95,   99,  105,  100,  129,  131,  106,  109,  139,  101,
      130,  102,  107,  132,  110,  103,  104,  140,  176,  133,

      177,  111,  108,  178,  112,   98,  183,  113,   99,  105,
      100,  129,  131,  106,  109,  139,  101,  130,  102,  107,
      132,  110,  103,  104,  140,  176,  133,  177,  111,  108,
      178,  112,  114,  183,  113,  119,  115,  120,  188,   86,
      116,  141,  121,  290,  144,  142,  117,  122,  145,  118,
      191,  143,   86,   86,  146,  181,  182,  196,  197,  114,
     1330,  192,  119,  115,  120, 1329,  147,  116,  141,  121,
      290,  144,  142,  117,  122,  145,  118,  123,  143,  156,
      148,  146,  181,  182,  291,  124,  149,  150,  125,  296,
      157,  126,  134,  147,  127, 1328,  158,  128,  135,  136,

      137,  179,   86,   86,  123,  138,  156,  148, 1327,  180,
     1326,  291,  124,  149,  150,  125,  296,  157,  126,  134,
     1325,  127,  151,  158,  128,  135,  136,  137,  179,  297,
      152,  159,  138,  292,  153,  160,  180,  154,  155,  161,
      200,  293,  278,  301,  279,  162, 1323,  280, 1321,  151,
      294, 1319,  200,  295, 1317,  302,  297,  152,  159, 1315,
      292,  153,  160, 1313,  154,  155,  161,  200,  293,  278,
      301,  279,  162,  163,  280,  164,  305,  294,  165,  200,
      295,  166,  302,  167,  312,  168,  169,  193,  193,  193,
      193,  193,  193,  193,  193,  193,  193,  188,   86, 1311,

      163, 1309,  164,  305, 1307,  165,  298,  299,  166,  303,
      167,  312,  168,  169,  170,  310,  304,  231,  171,  200,
      300,  172,  173,  232,  200,  311,  320,  321,  174,  200,
      322,  175, 1305,  298,  299, 1303,  303, 1213, 1213, 1282,
     1282,  170,  310,  304,  231,  171,  200,  300,  172,  173,
      232,  200,  311,  320,  321,  174,  200,  322,  175,  194,
      195,  195,  195,  195,  195,  195,  195,  195,  195,  195,
      187,  187,  323,  187,  187,  187,  187,  187,  187,  324,
      233,  313, 1301, 1297,  200, 1293,  314,  325,  234,  187,
      187,  187,  200,  326,  235,  201,  200,  202,  332,  323,

      333,  334,  241,  203,  200,  204,  324,  233,  313,  205,
      206,  200,  200,  314,  325,  234,  337,  338, 1287,  200,
      326,  235,  201,  200,  202,  332, 1281,  333,  334,  241,
      203,  200,  204, 1268,  339, 1255,  205,  206, 1233,  200,
     1211, 1168, 1125,  337,  338,  187,  187,  187, 1053,  187,
      187,  187,  187,  187,  187,  981,  885,  340,  242,  243,
      246,  339,  200,  244,  247,  187,  187,  187,  200,  245,
      248,  341,  207,  200,  342,  335,  208,  200,  200,  200,
      200,  336,  209,  200,  340,  242,  243,  246,  343,  200,
      244,  247,  210,  200,  789,  200,  245,  248,  341,  207,

      200,  342,  335,  208,  200,  200,  200,  200,  336,  209,
      200,  664,  539,  200,  344,  343,  283,  284,  345,  210,
      200,  187,  211,  200,  261,  346,  200,  351,  262,  212,
      200,  352,  263,  200,  353,  354,  213,  360,  264,  214,
      200,  344,  215,  283,  284,  345,  200,  414,  200,  211,
      200,  261,  346,  200,  351,  262,  212,  200,  352,  263,
      200,  353,  354,  213,  360,  264,  214,  413,  288,  215,
      287,  286,  281,  200,  216,  200,  200,  285,  217,  347,
      282,  200,  218,  355,  361,  356,  200,  200,  219,  348,
      362,  220,  363,  366,  349,  350,  200,  200,  200,  281,

      199,  216,  198,  200,  285,  217,  347,  282,  200,  218,
      355,  361,  356,  200,  200,  219,  348,  362,  220,  363,
      366,  349,  350,  200,  200,  200,  200,  306,  307,  308,
      200,  309,  315,  221,  316,  222,  200,  317,  367,  357,
      223,  358,  368,  318,  369,  224,  200,  187,  364,  370,
      319,  365,  359,  200,  306,  307,  308,  200,  309,  315,
      221,  316,  222,  200,  317,  367,  357,  223,  358,  368,
      318,  369,  224,  200,  225,  364,  370,  319,  365,  359,
      371,  200,  226,  372,  373,  227,  374,  327,  228,  328,
      375,  229,  376,  329,  230,  189,  380,  186,  184,  383,

      330,  225,   97,   96,  331,   87,  389,  371,  200,  226,
      372,  373,  227,  374,  327,  228,  328,  375,  229,  376,
      329,  230,  236,  380,  377,  381,  383,  330,  237,  238,
      239,  331,  387,  389,  390,  240,  382,  378,  391,  392,
      200, 1331,  379,   47,  393,   47,  388,  396, 1331,  236,
      397,  377,  381,  398,  404,  237,  238,  239, 1331,  387,
     1331,  390,  240,  382,  378,  391,  392,  200,  249,  379,
      384,  393,  394,  388,  396,  200,  399,  397,  405,  385,
      398,  404,  250,  395,  401,  200,  406,  386,  251,  252,
     1331,  402,  407, 1331,  403,  249,  400,  384,  410,  394,

      411,  412,  200,  399, 1331,  405,  385, 1331,  540,  250,
      395,  401,  200,  406,  386,  251,  252,  200,  402,  407,
      408,  403,  253,  400,  200,  410,  541,  411,  412,  542,
      254,  200,  200,  409,  255,  540,  200,  256,  257, 1331,
     1331, 1331,  200,  543,  200, 1331, 1331,  408,  544,  253,
     1331,  200,  200,  541, 1331,  545,  542,  254,  200,  200,
      409,  255,  546,  200,  256,  257,  258,  416,  200,  200,
      543,  547,  200,  200, 1331,  544,  548,  259,  200,  200,
      200,  200,  545,  260,  549,  200,  200, 1331,  200,  546,
      553,  200, 1331,  258,  416,  200,  417, 1331,  547,  200,

      200,  200,  554,  548,  259,  200,  555,  200,  200, 1331,
      260,  549,  200,  200,  265,  200,  266,  553,  200,  267,
      200, 1331,  268,  417,  269,  556,  270,  271,  200,  554,
      557,  558,  559,  555,  560,  561,  200, 1331,  562, 1331,
     1331,  265, 1331,  266, 1331, 1331,  267,  200,  418,  268,
      200,  269,  556,  270,  271,  200,  419,  557,  558,  559,
      200,  560,  561,  200,  272,  562,  200,  200,  273,  200,
      200,  274,  275,  422,  200,  418,  428,  200,  276,  200,
      563,  277,  200,  419,  564,  565,  200,  200,  200, 1331,
     1331,  272, 1331,  200,  200,  273,  200,  200,  274,  275,

      422,  200, 1331,  428, 1331,  276,  200,  563,  277, 1331,
     1331,  564,  565,  200, 1331,  200,   91,   91,   91,   91,
       91,   91,   91,   91,   91,   91,  289,  289,  289,  289,
      289,  289,  289,  289,  289,  289,   92,   93,   93,   93,
       93,   93,   93,   93,   93,   93,   93,  193,  193,  193,
      193,  193,  193,  193,  193,  193,  193,  415,  415,  415,
      415,  415,  415,  415,  415,  415,  415,  194,  195,  195,
      195,  195,  195,  195,  195,  195,  195,  195,  420,  423,
      427,  421,  550,  568,  551,  200,  569,  200,  200,  200,
      200,  200,  200,  570, 1331,  200,  200,  200, 1331,  552,

      200,  200, 1331, 1331, 1331,  420,  423,  427,  421,  550,
      568,  551,  200,  569,  200,  200,  200,  200,  200,  200,
      570,  200,  200,  200,  200,  200,  552,  200,  200,  424,
      425,  200,  429,  571,  200,  200,  566,  200, 1331,  430,
     1331,  200,  200,  426,  200, 1331,  572,  200,  200,  573,
     1331,  567,  200,  574,  431,  575,  424,  425,  200,  429,
      571,  200,  200,  566,  200,  200,  430,  200,  200,  200,
      426,  200,  200,  572,  200, 1331,  573,  200,  567, 1331,
      574,  431,  575,  436, 1331, 1331,  576,  200,  577,  578,
     1331, 1331,  200,  437,  200,  579,  580,  581,  200,  200,

     1331,  582,  200,  200,  200,  432,  433,  434,  200,  435,
      436,  438,  200,  576,  200,  577,  578,  200,  200,  583,
      437,  584,  579,  580,  581,  200,  585,  200,  582,  200,
      200,  200,  432,  433,  434,  200,  435,  200,  438,  200,
      586,  587,  439,  588,  200,  200,  583,  440,  584,  589,
     1331,  590,  591,  585,  200,  592, 1331,  593,  200, 1331,
      594, 1331, 1331, 1331,  200,  595, 1331,  586,  587,  439,
      588, 1331,  596,  597,  440,  441,  589,  442,  590,  591,
      443,  600,  592,  200,  593,  200,  444,  594,  200,  446,
      447, 1331,  595,  445,  200,  200,  200, 1331,  200,  596,

      597,  200,  441,  448,  442,  601,  200,  443,  600,  602,
      200, 1331,  200,  444,  200,  200,  446,  447,  200, 1331,
      445,  200,  200,  200,  449,  200, 1331,  603,  200,  598,
      448, 1331,  601,  200,  200,  200,  602,  200,  451,  599,
     1331,  200,  450, 1331,  604,  200,  200,  200,  200, 1331,
     1331,  449,  605,  200,  603, 1331,  598,  608,  200, 1331,
      609,  200,  200,  610,  200,  451,  599,  452,  611,  450,
      458,  604,  200,  200,  200,  200,  200,  200,  200,  605,
      200,  612,  200,  200,  608,  200,  606,  609,  200, 1331,
      610, 1331, 1331,  613,  452,  611,  607,  458,  614,  200,

     1331, 1331, 1331,  200,  200,  200, 1331,  615,  612,  200,
      200, 1331,  459,  606,  200,  200,  453,  200,  454,  200,
      613,  200,  455,  607,  200,  614,  616,  460,  200,  456,
      617,  618,  619,  457,  615, 1331, 1331,  200,  200,  459,
     1331,  200, 1331,  453,  200,  454,  200,  622,  200,  455,
      625,  200, 1331,  616,  460,  200,  456,  617,  618,  619,
      457,  461,  200,  200,  200,  200,  200,  462,  200,  464,
     1331,  200,  463,  200,  622,  626,  200,  625, 1331,  623,
     1331,  200,  200, 1331, 1331, 1331,  627,  200,  461,  200,
      200,  200,  624,  200,  462,  200,  464,  465,  200,  463,

      200,  200,  626,  200,  628,  200,  623,  200,  200,  200,
      200,  466,  200,  627,  200,  629,  630,  467,  200,  624,
      468,  200,  200,  200,  465,  469, 1331, 1331,  200, 1331,
      200,  628,  200, 1331,  200,  200, 1331,  200,  466,  200,
      631,  632,  629,  630,  467, 1331,  633,  468,  200,  200,
      200,  200,  469,  200,  200,  200,  200,  200,  200,  471,
      470,  200,  200,  200,  634,  200,  200,  631,  632,  635,
      620, 1331,  200,  633,  477,  200,  636, 1331,  200,  200,
      200,  200,  200,  200,  200,  200,  471,  470,  200,  621,
      200,  634,  200,  200,  200,  637,  635,  620,  200,  200,

      638,  477,  200,  636,  200,  472,  200, 1331,  200,  200,
     1331,  200,  478,  200,  200, 1331,  621,  639,  200,  200,
     1331,  200,  637, 1331,  640,  200, 1331,  638,  200,  479,
     1331,  200,  472,  200,  480,  200,  200,  200,  641,  478,
      200,  200,  473,  200,  639,  200,  200,  200,  642,  487,
      200,  640,  474,  200,  200,  200,  479,  475,  476,  200,
      200,  480,  200, 1331,  200,  641,  643, 1331, 1331,  473,
      200, 1331, 1331, 1331,  200,  642,  487,  200,  644,  474,
      200,  200, 1331, 1331,  475,  476,  200,  645,  646,  200,
      200,  647, 1331,  643,  200,  200,  481,  200,  482,  497,

      200,  486,  200,  200,  650,  644, 1331,  200,  653,  200,
      200, 1331,  654, 1331,  645,  646, 1331,  200,  647,  200,
     1331,  200,  200,  481,  200,  482,  497,  200,  486,  200,
      200,  650,  655,  648,  200,  653,  200,  200,  483,  654,
      484,  200,  649,  488,  656,  651,  200,  652,  200,  200,
      200,  485,  200,  200,  657,  658,  200,  200,  489,  655,
      648,  200,  200, 1331,  659,  483, 1331,  484,  200,  649,
      488,  656,  651, 1331,  652,  200,  200,  200,  485,  200,
      200,  657,  658,  200,  200,  489, 1331, 1331,  200,  200,
      200,  659,  200,  492, 1331,  200,  662,  200,  490, 1331,

      200,  491,  200,  493, 1331,  200,  200,  660,  200,  663,
     1331,  661, 1331,  200,  200, 1331,  200,  200,  200,  200,
      492,  200,  200,  662,  200,  490,  200,  200,  491,  200,
      493,  494,  200,  200,  660,  200,  663,  200,  661,  200,
      200,  200,  495,  200,  200,  200, 1331,  790,  200,  200,
      200, 1331,  200,  200, 1331, 1331,  791,  200,  494,  792,
      793, 1331,  200,  794,  200,  795,  200,  796,  797,  495,
      798,  200,  799,  200,  790,  200,  200,  200,  200,  200,
      200,  496,  200,  791,  200,  200,  792,  793,  200,  200,
      794,  498,  795, 1331,  796,  797, 1331,  798,  200,  799,

      200,  800,  200, 1331, 1331,  200,  499,  200,  496,  200,
      200,  801,  200,  802,  200,  200,  803,  804,  498,  500,
      200,  805, 1331,  501,  200,  200,  502,  806,  800,  200,
      200,  200,  807,  499,  808, 1331,  200,  200,  801,  200,
      802,  200,  809,  803,  804, 1331,  500,  200,  805,  503,
      501,  200, 1331,  502,  806,  810,  200,  200,  200,  807,
      200,  808,  504,  200, 1331,  811,  200,  505,  506,  809,
      812,  813,  200,  200,  814,  200,  503,  815, 1331,  507,
      200,  200,  810,  816, 1331,  200,  200,  200, 1331,  504,
      508,  200,  811, 1331,  505,  506,  817,  812,  813,  200,

      200,  814,  200,  818,  815,  509,  507,  200,  200,  200,
      816,  200,  200,  200,  819,  200,  200,  508,  200,  515,
      513,  200, 1331,  817,  200,  200,  820,  821,  200,  822,
      818, 1331,  509, 1331,  514,  200,  200, 1331,  200, 1331,
     1331,  819,  200,  200,  200,  823,  515,  513,  200,  510,
     1331,  200,  200,  820,  821,  200,  822, 1331,  511,  516,
      200,  514,  200,  200,  200,  200,  512,  200,  200, 1331,
      200,  200,  823,  200,  200, 1331,  510,  517,  824, 1331,
      825,  826, 1331,  200,  200,  511,  516,  200,  827,  828,
      200,  200,  200,  512,  200,  200,  519,  200,  518,  200,

      200,  200,  200,  200,  517,  824,  200,  825,  826,  200,
      200,  200,  200, 1331, 1331,  827,  828,  522,  829,  200,
     1331,  200,  200,  519, 1331,  518,  200,  200, 1331,  200,
      200,  830, 1331,  200, 1331,  831,  200,  200,  832,  200,
      200,  520,  200,  833,  522,  829,  200,  200,  200,  200,
     1331,  200,  521,  834,  200,  200, 1331, 1331,  830,  523,
      835,  200,  831,  838,  200,  832, 1331,  200,  520,  200,
      833,  200, 1331,  200,  200, 1331,  839,  200,  200,  521,
      834,  840,  200,  200,  524,  525,  523,  835,  200,  841,
      838,  842,  200,  200, 1331,  845,  530,  200,  200,  200,

      200,  200,  200,  839,  200,  526, 1331,  200,  840, 1331,
      200,  524,  525, 1331, 1331,  846,  841,  200,  842,  200,
      200,  200,  845,  530,  200,  531,  200, 1331,  200,  200,
      847,  200,  526,  527,  200,  848,  200, 1331, 1331,  836,
      528,  200,  846,  529,  200,  849,  200,  532,  200,  200,
      837,  534,  531,  533,  200,  200,  200,  847,  200,  200,
      527,  200,  848,  200,  535,  536,  836,  528,  200,  200,
      529,  200,  849,  200,  532,  200,  200,  837,  534, 1331,
      533,  200,  200,  200, 1331,  200,  200, 1331,  200,  200,
      850,  535,  536,  200, 1331,  851,  200,  538,  200,  200,

      852,  200,  200,  853,  537,  200,  667,  854,  200,  200,
      855,  200,  200,  200,  843,  844,  200,  850,  200,  856,
      200,  200,  851, 1331,  538, 1331,  200,  852,  200, 1331,
      853,  537,  200,  667,  854,  200,  200,  855,  200, 1331,
      200,  843,  844, 1331, 1331,  200,  856, 1331,  200,  289,
      289,  289,  289,  289,  289,  289,  289,  289,  289,  415,
      415,  415,  415,  415,  415,  415,  415,  415,  415,  200,
      665,  200,  666,  200,  200,  200,  200,  857,  858,  200,
      200,  200,  200, 1331,  859,  860, 1331,  861, 1331,  200,
      200,  200,  200, 1331, 1331, 1331,  200,  665,  200,  666,

      200,  200,  200,  200,  857,  858,  200,  200,  200,  200,
      668,  859,  860,  200,  861,  200,  200,  200,  200,  200,
      200,  200,  200,  200,  200,  200,  200, 1331,  200,  200,
      200, 1331,  862,  200,  200, 1331,  669,  668,  863, 1331,
      200,  864,  200,  865, 1331, 1331,  200,  200,  200,  200,
      200,  200,  200,  200,  200,  200,  200,  200,  670,  862,
      200,  200,  200,  669,  200,  863,  200,  200,  864,  866,
      865,  672,  200,  200,  200, 1331,  673,  200,  200,  867,
      671,  200,  200,  200,  868,  670,  869,  200,  200,  200,
      674,  200,  200,  200,  200,  870,  866,  200,  672,  200,

      871,  200,  200,  673,  200,  200,  867,  671,  200,  200,
      200,  868,  200,  869,  200,  200, 1331,  674,  200,  200,
     1331,  678,  870, 1331,  200, 1331, 1331,  871,  200,  200,
      675, 1331,  676,  200,  872,  200,  200,  200,  873,  200,
      874, 1331,  200,  200,  875,  200,  200,  677,  678,  679,
      200, 1331,  200,  200,  680,  200,  200,  675,  200,  676,
      200,  872,  200,  200,  200,  873,  200,  874,  200,  200,
      200,  875, 1331,  200,  677, 1331,  679,  200,  682,  200,
      200,  680,  200,  200, 1331,  200,  200, 1331,  876,  200,
      683,  877,  200,  200,  200,  200,  878,  681,  200,  200,

      879,  200,  200, 1331,  200,  682,  684,  880, 1331,  200,
     1331,  200, 1331,  200,  200,  876,  881,  683,  877,  200,
      882,  200,  883,  878,  681,  200,  200,  879,  200,  200,
      200,  200,  200,  684,  880,  686,  685,  200,  200,  200,
      884,  200,  200,  881,  200,  200,  200,  882,  982,  883,
      983,  687,  984, 1331,  985,  200,  200,  200,  688,  200,
      200,  986,  686,  685,  200,  200,  200,  884, 1331,  200,
      200,  200,  200,  200, 1331,  982,  200,  983,  687,  984,
      689,  985,  200,  200, 1331,  688,  200,  200,  986,  987,
      690,  691,  200,  988,  200,  200,  200,  200,  200, 1331,

      989,  200,  990,  200,  200,  991,  692,  689, 1331, 1331,
      992,  200, 1331,  200,  200,  993,  987,  690,  691, 1331,
      988,  200,  200,  200,  693,  200,  200,  989,  200,  990,
      200,  200,  991,  692,  994,  694,  200,  992,  200,  200,
     1331,  200,  993, 1331,  200,  200,  200, 1331,  200,  995,
      996,  693,  997,  200,  200,  200, 1331,  200, 1331,  695,
     1331,  994,  694,  200,  200, 1331,  200,  696, 1331, 1331,
      200,  200,  200,  200,  200,  200,  995,  996, 1331,  997,
      200,  200,  200,  698,  998,  200,  695,  200,  200,  200,
      200,  200,  200,  200,  696,  200,  697,  200,  200,  999,

     1000,  200, 1331,  200, 1001,  200, 1331,  200,  200, 1331,
      698,  998,  200, 1002,  200,  200,  200,  200, 1331,  200,
      200, 1331,  200,  697,  700,  200,  999, 1000,  200,  699,
      200, 1001,  200,  200,  200,  200,  701,  200,  200, 1331,
     1002, 1331,  200,  200,  200,  200, 1331,  200,  200, 1003,
     1004,  700,  702,  200, 1005,  200,  699,  200,  200, 1006,
      200,  200, 1331,  701,  200,  200,  703, 1331,  200,  200,
      200,  200,  200,  200,  200,  200, 1003, 1004,  200,  702,
      200, 1005, 1007, 1008,  200,  200, 1006,  200, 1331,  200,
      200, 1331,  200,  703,  705,  200,  704,  200, 1331,  200,

      200, 1331,  200, 1331, 1009,  200, 1010, 1331,  706, 1007,
     1008, 1331,  200,  708,  200,  200,  200,  200,  200,  200,
      200,  705, 1011,  704,  200,  200,  200, 1331,  200,  200,
      707, 1009, 1012, 1010,  200,  706, 1331, 1331,  200,  200,
      708, 1331,  200, 1331,  200,  200, 1331,  200, 1013, 1011,
     1331, 1331,  200, 1014,  200,  200, 1015,  707, 1016, 1012,
     1019,  200,  200,  709,  200,  200,  200,  710,  200,  200,
      711,  200,  200,  200,  200, 1013, 1017,  200,  200, 1018,
     1014,  200,  200, 1015, 1331, 1016, 1331, 1019, 1331,  200,
      709,  200, 1331,  200,  710,  200,  200,  711,  712,  200,

      200,  200, 1020, 1017,  200,  200, 1018,  714,  200,  200,
      200,  200,  200,  200,  200,  200, 1021, 1022,  200,  200,
      200,  713, 1331,  200,  200,  712, 1331, 1023, 1024, 1020,
     1025, 1026, 1331, 1027,  714,  200, 1028,  200,  200,  200,
      200,  200,  200, 1021, 1022,  200,  200,  200,  713,  200,
      200,  200,  200,  715, 1023, 1024,  200, 1025, 1026,  200,
     1027,  200,  200, 1028, 1331,  200, 1331,  716, 1029,  200,
     1331,  200,  200, 1030,  717, 1331,  200, 1031, 1331,  200,
      715,  200, 1331,  200, 1032, 1331,  200,  200,  200,  200,
     1331,  200,  200,  718,  716, 1029,  200,  200,  200,  200,

     1030,  717,  200, 1033, 1031,  720,  200,  200,  200,  200,
     1331, 1032,  719, 1034,  200,  200, 1331,  721,  200, 1035,
      718,  200,  200, 1036,  200,  200, 1331,  200, 1331,  200,
     1033,  722,  720,  200,  200,  200,  200,  200, 1331,  719,
     1034,  200,  200,  723,  721, 1037, 1035,  200,  200,  200,
     1036,  200,  200,  724,  200,  200, 1331, 1331,  722,  200,
     1038, 1331,  200,  200,  200,  200, 1331, 1039,  200, 1040,
      723,  725, 1037, 1041,  200,  200, 1331,  200,  200,  726,
      724,  200,  200,  200,  200, 1331,  200, 1038,  200,  200,
      200, 1331,  200,  200, 1039, 1331, 1040, 1042,  725,  728,

     1041, 1331,  200,  200,  200,  200,  726,  200,  200,  200,
      200,  200,  200,  727, 1331,  200,  200,  200,  200,  200,
      200,  200,  729,  733, 1042,  200,  728, 1043,  200, 1331,
      200,  200,  200, 1331,  200, 1044,  200, 1045,  200,  200,
      727,  200,  730,  200,  200,  200,  200,  200,  200,  729,
      733, 1046,  200,  200, 1043,  200,  200,  200,  200,  734,
      200,  200, 1044,  200, 1045,  200,  200,  200,  200,  730,
      200,  736, 1331, 1047,  200, 1331,  200,  200, 1046, 1048,
      200, 1331, 1049,  200,  200, 1331,  734,  200,  200, 1050,
      200,  731,  200,  200,  200,  200,  200, 1331,  736,  200,

     1047,  732,  200,  200,  200,  200, 1048, 1051,  735, 1049,
     1331, 1052,  200, 1331, 1331,  200, 1050,  200,  731,  200,
      200,  200,  200,  200,  200, 1126,  200,  200,  732,  200,
      200, 1127,  200,  737, 1051,  735, 1128,  200, 1052,  200,
      200,  738,  200, 1129,  200,  200, 1331,  200,  200,  200,
     1130,  200, 1126, 1331,  200,  200, 1131,  200, 1127,  739,
      737,  200, 1331, 1128,  200,  200,  200,  200,  738, 1132,
     1129,  200,  200,  200, 1331,  200,  200, 1130, 1331,  740,
      200, 1133,  200, 1131, 1331,  200,  739,  200,  200,  200,
     1134,  742,  200,  200,  741,  200, 1132, 1331,  200,  200,

      200,  200,  200, 1331, 1135,  743,  740,  200, 1133,  200,
     1136,  200,  200, 1331,  200,  200,  200, 1134,  742, 1331,
      200,  741,  200, 1137,  200,  200,  200,  200,  200,  747,
      200, 1135,  743,  744,  200, 1331,  200, 1136,  200,  200,
      200, 1331,  200, 1138, 1139, 1331,  752,  200,  200, 1331,
     1137,  200,  200,  200,  200, 1331,  747,  200,  200, 1140,
      744,  200,  200,  750,  200, 1331,  200,  200,  745,  200,
     1138, 1139,  200,  752,  200,  200,  200, 1141, 1331, 1142,
      200, 1143,  200, 1144, 1145,  200, 1140,  746, 1146,  200,
      750,  200,  200, 1331, 1147,  745,  200, 1331, 1148,  200,

      751,  200, 1331,  200, 1141,  200, 1142,  200, 1143,  200,
     1144, 1145,  200, 1149,  746, 1146,  200,  200,  200,  200,
      748, 1147, 1150,  200,  754, 1148, 1151,  751,  200,  200,
     1331,  200,  200,  749,  200, 1152,  200, 1331, 1331,  200,
     1149,  200, 1331,  200,  200,  200,  753,  748, 1153, 1150,
      200,  754, 1154, 1151, 1155,  200,  200,  200,  200,  200,
      749,  755, 1152,  200,  200,  200, 1156,  200,  200,  200,
     1157, 1158,  200,  753, 1331, 1153, 1331,  200,  756, 1154,
      200, 1155, 1159, 1331,  200,  200,  200, 1331,  755, 1331,
      200,  200,  200, 1156,  200, 1160,  200, 1157, 1158,  200,

     1161,  200, 1331,  200,  200,  756,  758,  200,  757, 1159,
      200,  200,  200,  200,  760,  200,  200,  200,  200,  200,
     1162,  759, 1160,  200,  200, 1163,  200, 1161,  200,  200,
      200,  200, 1331,  758,  200,  757, 1331,  200,  200,  200,
      200,  760,  200,  200, 1331,  200,  200, 1162,  759, 1331,
      200,  200, 1163,  200, 1331,  200,  200, 1331,  200,  200,
      200,  200, 1331,  200,  200,  200,  200,  200,  761,  764,
      200,  200, 1164,  200,  763,  200,  200,  200,  762,  200,
      200,  200,  200,  200,  200,  765,  200,  200, 1331,  200,
      200,  200,  200, 1331,  200,  761,  764,  200,  200, 1164,

      200,  763,  200,  200,  200,  762,  200,  200,  200, 1165,
      200,  200,  765,  200, 1166,  200,  200,  767, 1167,  200,
      200,  766, 1212, 1214,  200,  200,  200, 1215,  200,  200,
      768, 1216, 1331,  200, 1331, 1331, 1165, 1331,  200, 1331,
      200, 1166,  200, 1217,  767, 1167,  200,  200,  766, 1212,
     1214,  200,  200,  200, 1215,  200,  200,  768, 1216,  200,
      200,  200,  200, 1218,  770,  200,  200,  769, 1331,  200,
     1217,  200,  771, 1219,  200,  200,  200, 1331, 1220,  200,
     1221, 1331,  200, 1331, 1331,  200,  200, 1222,  200,  200,
     1218,  770,  200,  200,  769,  200,  200, 1331,  200,  771,

     1219,  200,  200,  200,  773, 1220,  200, 1221,  200,  200,
      772,  200,  200,  774, 1222,  200, 1223, 1331,  200,  200,
      200, 1224,  200,  200,  200, 1331,  200, 1331,  200,  200,
      200,  773, 1331,  200,  775,  200,  778,  772,  200,  776,
      774,  777,  200, 1223,  200,  200,  200,  200, 1224,  200,
      200,  200,  200,  200,  780,  200,  200,  200, 1225,  200,
      200,  775, 1331,  778,  200, 1331,  776, 1331,  777, 1226,
      779,  200, 1331,  200,  200, 1227,  200, 1228,  200,  200,
      200,  780,  200, 1331,  200, 1225,  200,  200, 1331,  781,
     1229,  200,  200, 1331,  200, 1331, 1226,  779, 1230,  782,

     1231,  200, 1227, 1232, 1228,  200, 1331,  200,  200,  200,
      200,  200, 1256, 1258,  200,  200,  781, 1229, 1259,  200,
      200,  200,  200, 1260,  200, 1230,  782, 1231, 1261,  200,
     1232, 1331, 1331,  783,  200,  200,  200,  200,  200, 1256,
     1258, 1331,  200,  200,  784, 1259, 1331,  200,  200,  200,
     1260,  200,  200, 1262,  785, 1261,  200, 1263,  786,  200,
      783,  200,  787,  200,  200,  200,  200, 1264,  200, 1331,
      200,  784, 1265,  200, 1331,  200, 1331, 1331,  200,  200,
     1262,  785, 1213, 1213, 1263,  786,  200, 1266, 1267,  787,
      200,  200,  200,  200, 1264,  200,  788,  200, 1283, 1265,

      200,  200,  200,  200,  200,  200,  200,  886,  200, 1284,
      887,  200, 1285,  200, 1266, 1267,  200,  200, 1331,  200,
     1257, 1331,  200,  788,  200, 1283,  200, 1331,  200,  200,
      200,  200,  200,  200,  886,  200, 1284,  887,  200, 1285,
      200,  200,  888,  200,  200,  200,  892, 1257,  200,  200,
      200,  200, 1286,  200,  200, 1289,  200,  890,  200,  200,
      891,  889, 1282, 1282,  200,  200,  200, 1331,  200,  888,
      200,  200,  200,  892, 1331,  200, 1331,  200,  200, 1286,
     1331,  200, 1289,  200,  890,  200, 1295,  891,  889, 1296,
     1331,  200,  200,  200,  200,  894,  893,  200,  200,  200,

     1331,  200,  200,  895, 1294,  200,  200,  200,  200, 1331,
     1331,  200, 1299, 1295, 1331,  200, 1296,  200,  200, 1331,
     1331,  200,  894,  893, 1331,  200,  200,  200,  200,  200,
      895, 1294,  200,  200,  200,  200,  200,  200,  200, 1299,
      200,  200,  200, 1302,  200,  200,  200,  200, 1331, 1304,
     1331,  896, 1306, 1308,  200,  200,  200,  200, 1331,  200,
     1235, 1213, 1331,  200,  200,  200, 1331,  200,  200, 1310,
     1302, 1331, 1331,  200,  200,  200, 1304,  200,  896, 1306,
     1308,  200,  200,  200,  200,  897,  200,  200, 1312,  200,
      200,  200,  200,  898,  200,  200, 1310,  200, 1270,  200,

      200,  200,  200,  899,  200, 1314, 1331, 1331,  200,  200,
      200,  200,  897, 1331,  200, 1312,  200,  200,  200,  200,
      898,  200,  200,  200,  200, 1270,  200,  200,  200,  200,
      899, 1316, 1314,  200,  900,  200,  200,  200,  200,  200,
      901, 1318, 1331,  902,  200,  200,  200,  200,  200, 1331,
      200, 1331, 1331,  200,  200, 1320,  200, 1322, 1316,  903,
      200,  900,  200,  200,  200, 1331,  200,  901, 1318,  200,
      902,  200,  200,  200,  200,  200,  904, 1331,  200,  200,
      200,  200, 1320,  200, 1322, 1324,  903, 1331,  200,  200,
      200,  200,  200,  905,  200, 1331,  200, 1288, 1282,  200,

      200, 1331, 1331,  904,  200,  200,  200, 1331, 1331, 1331,
      200, 1331, 1324, 1331, 1331,  200,  200, 1331, 1331,  200,
      905,  200, 1331,  200, 1331,  200,  200, 1331, 1331, 1331,
      200,  200,  906, 1331,  200,  200,  200, 1331, 1331, 1298,
     1331,  200,  907, 1331, 1331, 1331,  200,  908, 1331, 1331,
      200, 1331,  200, 1331, 1331, 1331, 1331,  200,  200,  906,
      200,  200,  200,  200, 1331,  200, 1298, 1331,  200,  907,
      200, 1331, 1331,  200,  908,  910,  200, 1331,  200, 1331,
     1331, 1331,  909,  200,  200,  200,  200,  200,  200, 1331,
     1331,  200,  200, 1331,  200, 1331,  200,  200,  911, 1331,

     1331, 1331,  910,  200,  200,  200, 1331, 1331, 1331,  909,
      200,  200, 1331,  200,  200,  200, 1331,  912,  200,  200,
      200,  200,  200,  200,  200,  911,  200,  200,  200,  200,
      919,  200,  200,  200,  200, 1331, 1331, 1331,  200, 1331,
      200,  200, 1331, 1331,  912, 1331,  200,  200, 1331,  200,
      200,  200, 1331,  200,  200,  200,  200,  919, 1331,  200,
      200,  200,  200, 1331,  200,  200,  913,  200,  200,  200,
      200, 1331,  914, 1331,  200,  200,  200,  200,  200, 1331,
      200, 1331, 1331,  200, 1331,  915, 1331, 1331,  200,  200,
     1331,  200, 1331,  913, 1331,  200,  200,  200,  917,  914,

     1331,  200,  200,  200, 1331,  200,  200,  200,  200,  200,
      200,  200,  915,  200,  916,  200,  200, 1331,  200,  918,
     1331,  200, 1331, 1331,  200,  917, 1331, 1331,  200, 1331,
      200, 1331,  200,  200,  200,  200,  200,  200,  200, 1331,
      200,  916,  200,  200,  200,  200,  918, 1331,  200, 1331,
      200,  200,  200, 1331,  200,  200,  200,  200, 1331,  200,
      200,  200,  921, 1331,  200,  920, 1331, 1331, 1331,  200,
      200,  200,  200, 1331,  922, 1331,  200,  200,  200,  200,
      200,  200,  200,  200, 1331, 1331,  200,  200,  200,  921,
      200, 1331,  920, 1331,  200, 1331,  200,  200,  200,  200,

      200,  922, 1331,  200,  923,  200, 1331,  200,  200,  200,
      200, 1331,  200,  200, 1331,  200, 1331,  200,  200, 1331,
      200,  200,  200,  200,  200,  200,  200,  200,  200, 1331,
      200,  923,  200,  924,  200,  200,  925,  200, 1331,  200,
      200,  200,  200, 1331, 1331,  200,  200,  200, 1331,  200,
     1331,  200, 1331,  200, 1331,  200, 1331,  200, 1331,  200,
      924,  200, 1331,  925,  200, 1331,  200,  200,  200,  200,
      200,  200,  200,  200,  927,  200,  200,  926,  200,  200,
     1331, 1331,  200, 1331,  200,  200, 1331, 1331,  928, 1331,
     1331,  200, 1331,  200,  200,  200, 1331,  200,  200,  200,

     1331,  927,  200,  200,  926,  200,  200, 1331, 1331,  200,
     1331,  200,  200, 1331, 1331,  928,  200, 1331,  200, 1331,
     1331,  200,  200,  200,  200, 1331,  200,  200,  929,  200,
      930,  200, 1331,  931,  200, 1331,  200, 1331, 1331,  200,
     1331, 1331, 1331,  200, 1331,  200, 1331, 1331, 1331, 1331,
      200,  200, 1331,  200,  200,  929,  200,  930,  200, 1331,
      931,  200, 1331,  200, 1331, 1331,  200,  932,  200,  934,
      200,  200,  200, 1331,  200,  200,  200, 1331,  933,  200,
      200,  200, 1331, 1331,  200, 1331, 1331, 1331, 1331, 1331,
     1331, 1331, 1331, 1331,  932,  200,  934,  200,  200,  200,

     1331,  200,  200,  200, 1331,  933,  200,  200,  200, 1331,
      200,  200,  200,  200,  200,  200,  200,  200, 1331, 1331,
      200,  200,  200, 1331, 1331,  200,  200, 1331, 1331, 1331,
     1331, 1331, 1331, 1331, 1331, 1331, 1331,  200, 1331,  200,
      200,  200,  200,  200,  200, 1331, 1331,  200,  200,  200,
     1331, 1331,  200,  200,  200, 1331,  200,  200,  936,  200,
      200,  200,  935, 1331,  200,  200,  200, 1331, 1331,  200,
      200, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,
     1331,  200, 1331,  200,  200,  936,  200,  200,  200,  935,
      937,  200,  200,  200,  200,  200,  200,  200,  938,  200,

      200, 1331,  200,  941,  200,  200,  200,  200,  200,  200,
      200,  939,  940, 1331,  200,  200,  200,  937,  200,  200,
     1331,  200,  200, 1331, 1331,  938,  200,  200, 1331,  200,
      941,  200,  200,  200,  200,  200,  200,  200,  939,  940,
      200,  200,  200,  200,  943,  200,  200,  200,  200,  200,
      200, 1331,  200,  942,  200, 1331, 1331, 1331,  200,  200,
      200, 1331, 1331, 1331, 1331, 1331, 1331,  200,  200, 1331,
     1331,  943, 1331, 1331,  200,  200,  200,  200, 1331,  200,
      942,  200,  944, 1331,  200,  200,  200,  200,  200, 1331,
     1331, 1331,  945,  200,  200,  200,  200,  200, 1331, 1331,

     1331, 1331,  200,  200,  200, 1331,  946, 1331, 1331,  944,
     1331,  200,  200,  200, 1331,  200, 1331, 1331, 1331,  945,
      200,  200,  200,  200,  200, 1331,  200, 1331, 1331,  200,
      200,  200,  200,  946,  200,  947, 1331,  200,  200,  200,
      200,  200,  200, 1331,  200,  948,  200,  200, 1331,  200,
      949, 1331, 1331,  200,  200, 1331,  200,  200, 1331,  200,
     1331,  200,  947, 1331,  200,  200,  200, 1331,  200,  200,
     1331,  200,  948,  200,  200,  200,  950,  949, 1331,  200,
      200,  200, 1331,  200,  200,  200,  200, 1331, 1331, 1331,
      951, 1331, 1331,  200,  200,  200,  200, 1331,  200, 1331,

     1331, 1331,  200,  950,  200, 1331,  200,  200, 1331, 1331,
     1331, 1331,  200,  200,  200, 1331,  952,  951, 1331, 1331,
      200,  200,  200,  200,  200,  200, 1331, 1331,  953,  200,
      200,  200,  200,  200, 1331, 1331, 1331, 1331,  200,  200,
      200,  200,  200,  952, 1331, 1331,  200,  200,  200,  200,
      954,  200,  200, 1331, 1331,  953,  200,  200, 1331,  200,
      200, 1331,  200, 1331, 1331,  200,  200,  200,  200,  200,
      200, 1331,  200,  200,  200,  200,  200,  954,  200,  200,
      200,  955,  200, 1331, 1331,  956,  200, 1331,  200,  200,
      200,  957,  200, 1331,  200,  200, 1331,  200,  200,  200,

     1331,  200,  200, 1331,  200,  200, 1331,  200,  955,  200,
     1331, 1331,  956,  200,  200,  200,  200,  200,  957,  200,
      200,  200, 1331,  960,  958,  200,  200, 1331,  200,  200,
      959,  200,  200, 1331,  200,  200,  200, 1331,  961,  200,
      200,  200,  200,  200,  200, 1331, 1331,  200,  200, 1331,
      960,  958, 1331,  200, 1331,  200, 1331,  959,  200,  200,
      200,  200,  200,  200,  200,  961,  200,  200,  200,  200,
      200,  200,  200,  963,  962,  200,  200,  200,  200, 1331,
      200,  200, 1331,  200, 1331,  200,  200,  200,  200, 1331,
     1331,  200,  200,  200, 1331,  200,  964,  200, 1331,  200,

      963,  962,  200,  200,  200,  200, 1331,  200,  200, 1331,
      200, 1331,  200,  200,  965,  200, 1331,  200,  200,  200,
      200,  200,  200,  964,  200, 1331,  200,  966, 1331,  200,
     1331,  200,  200,  200,  200, 1331,  200,  200,  967,  200,
      200,  965,  200,  200,  200,  200,  200, 1331,  200,  200,
      200,  200,  200,  200,  966, 1331,  200,  968,  200,  200,
      200,  200,  200,  200,  200,  967, 1331,  200, 1331,  200,
      200,  969, 1331,  200,  200, 1331,  200,  200, 1331,  200,
      200,  200,  200,  200,  968, 1331,  200,  200,  200,  200,
      200, 1331,  200, 1331, 1331,  200, 1331,  970,  969, 1331,

      200,  200, 1331,  200,  200, 1331,  971,  200,  200,  200,
     1331,  200, 1331,  200,  200,  200,  200,  200, 1331,  200,
     1331, 1331,  200,  200,  970,  200,  973,  200, 1331, 1331,
      200,  200,  972,  971,  200,  200,  200,  200,  200,  200,
     1331,  200,  974,  200,  200, 1331,  200, 1331, 1331,  200,
      200, 1331,  200,  973, 1331, 1331, 1331,  200, 1331,  972,
      975,  200,  200,  200,  200,  200,  200,  200,  200,  974,
      976,  200,  200,  200,  200,  200,  200,  200,  977,  200,
      200, 1331, 1331, 1331, 1331,  200, 1331,  975, 1331, 1331,
      200, 1331,  200, 1331,  200,  200, 1331,  976, 1331,  200,

     1331,  200,  200, 1331,  200,  977,  200,  200, 1331,  200,
      200,  200,  200,  200,  200,  200, 1331,  200, 1331,  200,
      200,  200,  200,  979,  200, 1331,  978,  200,  200,  200,
      200,  200, 1331,  200,  200, 1331,  200,  200,  200, 1331,
      200,  200,  200,  200,  200, 1331,  200,  200,  200, 1331,
      979,  200, 1331,  978,  200,  200,  200,  200,  200, 1331,
      200,  200,  200, 1331,  200,  200,  200,  200,  200,  980,
      200,  200,  200,  200,  200, 1331, 1331,  200,  200, 1331,
     1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,  200,
     1331,  200,  200,  200,  200,  200,  980, 1331, 1331,  200,

      200,  200, 1331, 1331,  200,  200,  200,  200,  200,  200,
     1331, 1055,  200,  200,  200,  200,  200, 1331,  200,  200,
     1054,  200,  200, 1331, 1331, 1331, 1331, 1331, 1331, 1331,
     1331,  200,  200,  200,  200,  200,  200, 1331, 1055,  200,
      200,  200,  200,  200, 1331,  200,  200, 1054,  200,  200,
     1056,  200, 1331, 1331,  200,  200, 1331, 1331,  200,  200,
      200,  200, 1331, 1059,  200, 1058, 1057, 1331,  200,  200,
      200,  200, 1331,  200,  200, 1331, 1331, 1056,  200,  200,
     1331,  200,  200, 1331,  200, 1331, 1331,  200,  200,  200,
     1059,  200, 1058, 1057, 1331,  200,  200,  200,  200, 1331,

      200,  200, 1060,  200,  200, 1061,  200, 1331,  200, 1331,
      200,  200, 1331, 1331,  200,  200,  200, 1331, 1331, 1331,
     1331, 1331, 1331, 1331,  200, 1331,  200, 1331,  200, 1060,
      200,  200, 1061,  200, 1062,  200,  200,  200,  200, 1064,
      200,  200,  200,  200,  200,  200, 1063, 1065,  200,  200,
     1331,  200,  200,  200,  200,  200,  200,  200,  200,  200,
      200, 1062, 1066,  200,  200,  200, 1064,  200,  200, 1331,
      200,  200,  200, 1063, 1065,  200,  200, 1331,  200,  200,
     1331,  200, 1331,  200,  200,  200,  200, 1331,  200, 1066,
     1331,  200,  200, 1068, 1331,  200, 1070,  200, 1067, 1331,

      200,  200, 1069, 1331,  200,  200,  200,  200,  200,  200,
     1331, 1331, 1331, 1331,  200,  200,  200,  200, 1331,  200,
     1068, 1331,  200, 1070,  200, 1067,  200,  200,  200, 1069,
     1331,  200,  200,  200,  200,  200,  200, 1331, 1331, 1331,
     1331,  200,  200,  200,  200, 1331,  200,  200, 1331,  200,
     1071,  200, 1331,  200, 1331, 1331,  200,  200,  200,  200,
     1073, 1331, 1072, 1331, 1331,  200,  200,  200, 1331,  200,
      200, 1074, 1331,  200,  200,  200, 1075, 1071,  200, 1331,
      200,  200, 1331,  200,  200,  200,  200, 1073, 1331, 1072,
     1331,  200,  200,  200,  200,  200,  200,  200, 1074,  200,

     1331, 1331,  200, 1075,  200,  200,  200,  200,  200, 1331,
     1076, 1078, 1331,  200, 1331,  200,  200, 1331,  200, 1331,
      200, 1331,  200,  200,  200, 1331,  200, 1331, 1331, 1331,
      200,  200,  200,  200, 1331, 1077, 1331, 1076, 1078, 1331,
      200, 1331,  200,  200,  200, 1331, 1331,  200, 1079,  200,
     1331,  200, 1331,  200,  200, 1331, 1331,  200, 1083,  200,
      200, 1331, 1077, 1331,  200,  200, 1080,  200,  200,  200,
      200,  200,  200, 1331, 1331, 1079,  200, 1331,  200, 1331,
      200,  200, 1331, 1331, 1331, 1083,  200,  200,  200, 1331,
      200,  200,  200, 1080,  200,  200,  200,  200,  200,  200,

      200, 1331,  200, 1331, 1331,  200, 1331, 1331,  200, 1331,
      200, 1331, 1331, 1331, 1331,  200, 1081,  200,  200, 1331,
     1331,  200, 1331, 1331,  200,  200, 1331,  200,  200,  200,
     1331, 1331, 1331, 1331,  200,  200, 1331,  200, 1331, 1082,
      200, 1085,  200, 1081,  200,  200,  200, 1084,  200, 1331,
     1331,  200,  200,  200, 1331,  200, 1331, 1331,  200, 1331,
     1331,  200, 1331, 1331, 1331, 1331, 1082,  200, 1085,  200,
      200,  200,  200,  200, 1084,  200,  200,  200,  200,  200,
      200, 1331,  200,  200,  200,  200,  200, 1331,  200, 1331,
     1331,  200, 1086, 1331, 1331, 1331,  200,  200, 1331,  200,

      200, 1331,  200,  200,  200,  200, 1331,  200, 1331,  200,
      200,  200,  200,  200,  200,  200,  200, 1331,  200, 1086,
     1331,  200, 1331,  200, 1331, 1331,  200,  200,  200,  200,
      200, 1331, 1331, 1331,  200,  200, 1087, 1331, 1331,  200,
      200,  200,  200,  200,  200, 1331, 1088, 1331,  200, 1089,
      200, 1331, 1090,  200,  200,  200,  200,  200, 1091, 1331,
      200, 1331,  200, 1087, 1331,  200,  200,  200, 1331,  200,
      200,  200, 1331, 1088, 1331, 1331, 1089,  200, 1331, 1090,
     1331,  200,  200,  200,  200, 1091, 1331,  200,  200,  200,
      200, 1331,  200,  200,  200,  200, 1331,  200, 1092, 1331,

      200, 1331, 1331, 1331, 1331, 1331,  200, 1331,  200,  200,
     1331,  200,  200,  200,  200,  200,  200,  200,  200,  200,
     1331,  200,  200, 1331,  200, 1092, 1331,  200, 1331, 1331,
      200, 1331,  200,  200, 1093,  200, 1331,  200,  200,  200,
      200,  200,  200, 1331,  200,  200,  200, 1331,  200, 1331,
      200,  200, 1331, 1331,  200, 1094, 1331,  200, 1331,  200,
      200, 1093, 1331, 1331,  200,  200, 1331, 1095, 1331,  200,
     1331,  200, 1331, 1331, 1331,  200, 1331,  200,  200, 1331,
      200,  200, 1094, 1331,  200,  200,  200,  200, 1331, 1331,
      200,  200, 1096, 1097, 1095, 1331,  200,  200, 1331,  200,

     1331, 1331, 1331, 1331,  200,  200, 1331,  200, 1331,  200,
     1331,  200,  200,  200, 1331, 1331, 1331,  200,  200, 1096,
     1097, 1331, 1331,  200,  200,  200,  200,  200,  200, 1331,
      200,  200,  200, 1098, 1331,  200,  200,  200, 1099,  200,
      200,  200,  200, 1331, 1331, 1331,  200, 1331,  200, 1331,
     1331,  200,  200, 1331,  200,  200, 1331,  200,  200,  200,
     1098, 1331,  200, 1331,  200, 1099,  200,  200,  200,  200,
     1331, 1331,  200,  200, 1331,  200,  200, 1331,  200, 1101,
      200, 1331,  200, 1331,  200,  200, 1331, 1100, 1331, 1331,
      200, 1331,  200, 1331, 1331, 1331, 1331, 1331, 1331,  200,

      200, 1331, 1331,  200, 1331, 1331, 1101,  200,  200,  200,
     1331,  200,  200, 1331, 1100, 1331,  200,  200,  200,  200,
      200, 1102, 1331, 1331, 1331, 1331,  200,  200,  200, 1103,
      200, 1331, 1331, 1331,  200,  200,  200, 1331, 1331,  200,
      200, 1331, 1104,  200, 1331,  200,  200,  200, 1102, 1331,
      200, 1331,  200,  200,  200,  200, 1103,  200, 1105, 1331,
     1331,  200,  200,  200,  200, 1331,  200,  200, 1331, 1104,
      200, 1331, 1331,  200,  200, 1331,  200,  200, 1331,  200,
     1106,  200, 1331, 1331,  200, 1105,  200, 1331,  200,  200,
     1331,  200, 1107,  200,  200, 1331,  200,  200,  200, 1331,

      200,  200,  200,  200,  200, 1331,  200, 1106,  200, 1108,
      200,  200, 1331,  200,  200,  200,  200, 1331,  200, 1107,
      200,  200,  200,  200,  200,  200, 1110,  200,  200,  200,
      200,  200,  200,  200,  200,  200, 1108,  200, 1331, 1109,
     1331,  200,  200,  200,  200,  200, 1331,  200, 1331,  200,
     1112,  200, 1331, 1110,  200,  200, 1331,  200,  200,  200,
     1331,  200, 1111,  200,  200, 1331, 1109,  200,  200,  200,
      200,  200, 1331,  200,  200, 1113, 1331, 1112, 1331, 1331,
      200,  200, 1331,  200, 1331,  200,  200, 1331,  200, 1111,
      200,  200, 1114,  200,  200,  200, 1331,  200,  200, 1331,

      200,  200, 1113, 1331,  200, 1331,  200,  200, 1331, 1331,
      200,  200, 1115,  200, 1331,  200,  200, 1116, 1331, 1114,
      200, 1331, 1331, 1117, 1331,  200, 1331, 1331,  200, 1331,
      200,  200, 1331,  200,  200,  200,  200, 1331,  200, 1115,
      200,  200, 1331,  200, 1116, 1331,  200,  200, 1331, 1118,
     1117, 1331, 1331, 1331,  200,  200, 1331,  200,  200,  200,
      200,  200,  200,  200, 1331,  200, 1331,  200,  200, 1331,
      200, 1331, 1331,  200,  200, 1331, 1118, 1331, 1119, 1120,
      200,  200,  200, 1331,  200,  200,  200,  200,  200, 1331,
      200, 1331,  200, 1331, 1331, 1331, 1331,  200,  200, 1331,

      200, 1331, 1331, 1331, 1121, 1119, 1120,  200,  200,  200,
     1331,  200,  200, 1331,  200,  200,  200,  200, 1331, 1331,
     1331,  200,  200,  200,  200,  200, 1331,  200, 1122, 1123,
     1331, 1121,  200,  200, 1331,  200, 1331, 1331, 1331,  200,
      200,  200,  200,  200, 1331, 1331, 1331,  200,  200,  200,
      200,  200,  200, 1124, 1331, 1122, 1123, 1331, 1331,  200,
      200,  200, 1331,  200,  200, 1331,  200,  200,  200,  200,
      200,  200,  200,  200,  200, 1331,  200,  200, 1331,  200,
     1124, 1331,  200, 1331, 1331, 1331, 1331, 1331,  200, 1331,
      200,  200, 1169,  200, 1331,  200,  200,  200,  200,  200,

      200,  200,  200,  200,  200,  200,  200, 1331, 1170,  200,
     1172,  200,  200,  200,  200,  200, 1331,  200,  200, 1169,
      200,  200,  200,  200,  200, 1331, 1171,  200,  200,  200,
      200,  200,  200,  200, 1331, 1170,  200, 1172,  200,  200,
      200,  200,  200, 1331,  200,  200,  200,  200,  200,  200,
      200,  200, 1331, 1171,  200, 1331,  200,  200,  200, 1331,
      200, 1173,  200,  200, 1331, 1331,  200, 1174,  200, 1175,
      200,  200,  200,  200, 1331,  200,  200,  200, 1331, 1331,
      200,  200, 1331,  200, 1331, 1331, 1331,  200, 1173,  200,
     1331, 1331, 1331,  200, 1174,  200, 1175,  200,  200,  200,

     1331, 1331,  200,  200,  200, 1331,  200,  200,  200, 1331,
      200,  200,  200,  200, 1177,  200,  200,  200, 1176, 1331,
      200, 1331,  200, 1331, 1331,  200, 1331, 1331, 1331, 1331,
     1331,  200, 1331,  200, 1331, 1331, 1331,  200,  200,  200,
      200, 1177,  200,  200,  200, 1176, 1178,  200, 1331,  200,
     1331, 1179,  200,  200, 1331,  200, 1331,  200,  200, 1331,
     1331, 1331,  200,  200,  200, 1331,  200,  200, 1331, 1331,
     1331,  200, 1180, 1178, 1331, 1331,  200, 1331, 1179, 1331,
      200, 1331,  200, 1331,  200,  200, 1331, 1331, 1331,  200,
      200,  200, 1331,  200,  200,  200, 1181,  200,  200, 1180,

     1331, 1331,  200,  200, 1331, 1331,  200,  200,  200,  200,
     1331,  200, 1331,  200, 1331, 1331,  200, 1331,  200, 1331,
     1331,  200,  200, 1181,  200, 1331, 1331, 1331, 1182,  200,
     1331, 1331, 1331,  200,  200,  200,  200,  200,  200,  200,
      200, 1331, 1331,  200,  200,  200, 1331, 1331,  200,  200,
      200,  200,  200,  200, 1331, 1182, 1331,  200,  200, 1331,
     1331, 1331,  200,  200,  200, 1331,  200, 1331, 1331, 1331,
     1331,  200, 1331, 1331, 1331, 1331,  200,  200,  200,  200,
      200,  200, 1183,  200,  200,  200,  200,  200,  200,  200,
      200,  200,  200,  200,  200, 1331, 1184,  200, 1331, 1331,

     1331,  200,  200, 1331, 1331, 1331,  200,  200,  200, 1183,
      200, 1331, 1331,  200,  200,  200, 1331, 1331,  200,  200,
      200,  200, 1331, 1184,  200, 1331, 1331, 1331,  200,  200,
      200,  200,  200,  200,  200, 1186,  200,  200, 1185, 1331,
      200,  200,  200, 1331, 1331, 1331,  200, 1331, 1331, 1331,
      200,  200, 1331, 1331,  200, 1331,  200,  200,  200,  200,
      200, 1331, 1186,  200,  200, 1185, 1331,  200,  200,  200,
      200, 1331,  200,  200, 1331,  200,  200,  200,  200,  200,
      200,  200,  200,  200,  200,  200, 1331,  200, 1331, 1331,
      200, 1331,  200, 1331,  200,  200, 1331,  200,  200,  200,

      200, 1331,  200,  200,  200, 1331,  200,  200, 1331,  200,
     1331,  200,  200, 1331,  200, 1331,  200,  200, 1331,  200,
      200,  200,  200, 1331, 1188,  200,  200,  200,  200, 1187,
     1331,  200, 1331, 1331,  200, 1331,  200, 1331, 1189, 1331,
     1331,  200,  200,  200,  200, 1331,  200,  200, 1331, 1331,
      200, 1188,  200,  200,  200,  200, 1187, 1331, 1331, 1331,
      200,  200,  200,  200,  200, 1189, 1331, 1331,  200,  200,
      200,  200, 1331,  200,  200, 1331,  200,  200,  200,  200,
     1331,  200, 1331, 1190,  200, 1331, 1331,  200,  200,  200,
     1331,  200,  200, 1331,  200,  200, 1331,  200, 1331,  200,

     1191,  200,  200,  200,  200,  200,  200,  200,  200, 1331,
     1190,  200,  200,  200, 1331,  200, 1331, 1331,  200,  200,
     1331,  200, 1331, 1331, 1331, 1192,  200, 1191, 1331,  200,
      200,  200,  200,  200,  200,  200,  200,  200,  200,  200,
      200, 1331,  200,  200, 1331,  200, 1331, 1331,  200, 1331,
     1331, 1331, 1192, 1331,  200, 1331,  200,  200, 1331,  200,
     1331,  200, 1193,  200,  200,  200,  200,  200, 1331,  200,
      200, 1194,  200, 1331, 1331,  200, 1195,  200, 1331, 1331,
     1196,  200,  200,  200,  200, 1331, 1331,  200,  200, 1193,
      200, 1331,  200,  200,  200, 1331, 1331, 1331, 1194,  200,

      200, 1331,  200, 1195,  200, 1331,  200, 1196, 1331,  200,
     1331,  200,  200, 1331,  200, 1331,  200,  200, 1331,  200,
      200, 1331, 1197, 1331, 1331, 1331,  200,  200, 1198,  200,
      200, 1331, 1331,  200,  200, 1331,  200, 1331, 1331,  200,
      200, 1331, 1199,  200,  200, 1331, 1331,  200,  200, 1197,
      200, 1331, 1331,  200,  200, 1198, 1331,  200,  200, 1331,
      200,  200, 1331,  200,  200, 1200,  200,  200,  200, 1199,
      200,  200,  200, 1201,  200,  200, 1331,  200,  200,  200,
     1331,  200, 1331, 1331,  200,  200, 1331,  200, 1331, 1331,
     1331,  200, 1200,  200, 1331,  200,  200,  200, 1202,  200,

     1201,  200,  200,  200,  200,  200,  200, 1331,  200,  200,
     1203,  200,  200, 1331,  200, 1331, 1331,  200, 1331, 1331,
     1331, 1331,  200,  200, 1331, 1202,  200, 1331,  200,  200,
      200,  200, 1331,  200, 1204,  200,  200, 1203,  200,  200,
     1331,  200, 1331, 1331,  200,  200, 1234,  200, 1331,  200,
      200, 1331,  200,  200, 1331,  200,  200,  200, 1205, 1206,
      200, 1204, 1331, 1331, 1331,  200,  200, 1331,  200,  200,
      200,  200,  200, 1234,  200,  200,  200,  200, 1331,  200,
      200,  200, 1331,  200,  200, 1205, 1206, 1331, 1331, 1331,
     1331, 1331,  200,  200,  200,  200,  200,  200,  200,  200,

     1207, 1331,  200,  200,  200, 1208, 1331,  200,  200, 1331,
      200, 1331,  200,  200, 1331,  200,  200,  200,  200,  200,
      200,  200,  200,  200, 1331,  200,  200, 1207,  200, 1331,
     1331,  200, 1208, 1331,  200, 1331,  200,  200, 1331,  200,
      200,  200,  200,  200,  200,  200,  200,  200, 1331,  200,
      200, 1331,  200, 1331, 1331,  200, 1331,  200, 1331,  200,
      200,  200,  200,  200,  200, 1331, 1209,  200,  200,  200,
     1331, 1331,  200,  200, 1331, 1331, 1210, 1331,  200, 1331,
     1331, 1331, 1331,  200,  200, 1331,  200,  200,  200,  200,
      200,  200,  200, 1209,  200, 1331,  200,  200, 1331,  200,

     1331, 1331,  200, 1210,  200,  200,  200, 1331, 1331, 1331,
      200,  200, 1331, 1331, 1331,  200,  200,  200,  200,  200,
      200, 1331, 1331, 1331,  200,  200, 1331, 1331, 1331,  200,
      200,  200,  200,  200,  200, 1331,  200, 1331,  200,  200,
      200, 1331, 1331,  200,  200,  200,  200,  200, 1331, 1331,
      200, 1331,  200, 1331,  200, 1331,  200,  200, 1331,  200,
      200,  200, 1331,  200, 1235, 1213,  200,  200, 1331, 1331,
      200,  200, 1331,  200, 1236, 1331, 1331,  200,  200, 1331,
     1331,  200, 1331,  200,  200, 1331, 1331,  200, 1237,  200,
     1331, 1331,  200,  200,  200, 1331,  200,  200, 1331,  200,

     1331, 1236,  200, 1331,  200,  200, 1331, 1331,  200,  200,
     1331,  200,  200, 1331,  200, 1237,  200, 1331,  200,  200,
      200,  200,  200,  200,  200, 1331,  200, 1331,  200,  200,
     1238,  200, 1331,  200,  200,  200,  200,  200,  200,  200,
      200,  200,  200,  200, 1331,  200,  200, 1331, 1239,  200,
      200,  200,  200,  200, 1331,  200, 1331, 1238, 1331, 1331,
      200,  200,  200, 1331,  200,  200, 1331,  200,  200,  200,
      200, 1331,  200,  200, 1331, 1239, 1331,  200,  200,  200,
      200,  200, 1331, 1331, 1331,  200, 1331, 1331,  200,  200,
      200,  200, 1240, 1331, 1331,  200, 1331,  200, 1331,  200,

     1331,  200,  200, 1331,  200,  200,  200, 1331,  200, 1331,
     1331,  200,  200, 1331, 1241,  200,  200,  200,  200, 1240,
     1242, 1331,  200,  200,  200,  200, 1331,  200,  200,  200,
      200,  200,  200,  200, 1331,  200, 1331, 1331,  200, 1331,
     1331, 1241, 1331,  200,  200, 1331,  200, 1242, 1331,  200,
      200,  200,  200,  200,  200,  200,  200,  200, 1331,  200,
      200, 1331,  200, 1331, 1331,  200, 1331, 1331, 1331, 1331,
     1331,  200, 1331,  200, 1331, 1331, 1331, 1331,  200,  200,
      200,  200,  200,  200, 1244, 1243,  200,  200,  200,  200,
     1331,  200,  200,  200,  200,  200, 1331, 1246,  200, 1245,

      200, 1331,  200, 1331,  200,  200,  200,  200,  200,  200,
      200, 1244, 1243,  200, 1331,  200,  200, 1331,  200, 1331,
      200,  200,  200, 1331, 1246,  200, 1245,  200,  200,  200,
      200,  200,  200, 1331,  200,  200,  200,  200, 1331,  200,
      200,  200, 1331, 1331, 1331, 1331,  200, 1247, 1331, 1331,
     1331,  200,  200, 1331,  200,  200, 1331,  200, 1331,  200,
     1331, 1331,  200, 1331,  200, 1331,  200,  200,  200, 1248,
      200, 1331,  200,  200, 1247, 1331, 1331,  200,  200,  200,
     1331,  200,  200,  200, 1331,  200,  200, 1331, 1331, 1331,
      200,  200, 1331, 1331, 1331,  200, 1248,  200, 1331,  200,

     1331, 1331, 1331, 1331,  200, 1331,  200, 1331,  200,  200,
      200, 1331,  200,  200, 1249, 1331, 1331,  200,  200,  200,
     1331,  200,  200, 1331, 1331, 1331,  200,  200, 1251,  200,
      200,  200,  200,  200,  200,  200, 1250,  200, 1331,  200,
      200, 1249,  200, 1331, 1331,  200,  200, 1331,  200, 1331,
     1331, 1331, 1331,  200,  200, 1251,  200,  200,  200,  200,
      200,  200,  200, 1250,  200, 1331,  200,  200, 1252,  200,
     1253, 1254,  200,  200,  200,  200, 1331,  200, 1331, 1331,
      200,  200, 1331,  200, 1331, 1331, 1331,  200, 1331,  200,
      200,  200, 1331,  200,  200, 1252, 1331, 1253, 1254,  200,

      200,  200,  200, 1331,  200,  200, 1331,  200,  200,  200,
      200, 1331, 1331, 1331, 1331,  200, 1331,  200,  200,  200,
      200, 1331, 1331,  200,  200,  200, 1331, 1331,  200,  200,
     1331, 1331,  200, 1331,  200, 1331,  200, 1269, 1331,  200,
     1331, 1331,  200,  200,  200, 1331,  200,  200, 1331, 1331,
      200,  200,  200,  200, 1331,  200,  200, 1331, 1271, 1331,
     1331,  200, 1331,  200, 1269, 1331,  200, 1331,  200,  200,
      200,  200,  200,  200,  200, 1331, 1331, 1331,  200,  200,
      200, 1331,  200,  200, 1272, 1271,  200,  200,  200,  200,
      200, 1331,  200,  200, 1331,  200,  200,  200, 1273,  200,

      200,  200,  200,  200, 1331,  200,  200,  200, 1331,  200,
      200, 1272, 1331,  200,  200,  200,  200,  200, 1331,  200,
      200, 1331,  200,  200,  200, 1273,  200,  200,  200,  200,
      200, 1331,  200,  200,  200, 1331, 1274, 1331, 1331,  200,
     1276, 1275,  200,  200,  200,  200,  200,  200, 1331,  200,
      200,  200,  200,  200,  200, 1331, 1331,  200, 1331,  200,
      200, 1331, 1331, 1274, 1331, 1331,  200, 1276, 1275,  200,
      200, 1331,  200,  200,  200,  200, 1331,  200,  200,  200,
     1331,  200,  200,  200,  200, 1331, 1331,  200,  200,  200,
     1331,  200, 1331,  200, 1331, 1331,  200, 1331, 1331, 1331,

     1331,  200,  200, 1331,  200, 1331, 1331,  200,  200,  200,
      200,  200, 1331, 1331,  200,  200,  200,  200,  200, 1331,
      200,  200, 1331,  200, 1331,  200,  200, 1277,  200,  200,
     1279,  200, 1278,  200,  200, 1331,  200, 1331,  200,  200,
     1331,  200, 1331, 1331,  200, 1331,  200, 1331,  200,  200,
     1280,  200,  200,  200, 1277,  200,  200, 1279,  200, 1278,
      200,  200, 1331,  200, 1288, 1282,  200, 1331,  200, 1331,
     1290,  200, 1331,  200,  200,  200,  200, 1280,  200,  200,
      200, 1331,  200, 1331,  200,  200,  200, 1331,  200,  200,
      200, 1291, 1331,  200, 1331,  200,  200, 1290,  200,  200,

     1331,  200,  200,  200,  200,  200,  200,  200,  200,  200,
      200,  200,  200,  200,  200,  200,  200,  200, 1291, 1331,
      200, 1331,  200,  200,  200, 1331,  200,  200, 1331,  200,
      200,  200,  200, 1331,  200,  200,  200,  200,  200,  200,
      200,  200,  200, 1331, 1331,  200,  200, 1331,  200, 1331,
      200,  200, 1331,  200,  200, 1331,  200, 1331,  200,  200,
     1331,  200, 1331,  200,  200,  200,  200,  200,  200, 1292,
     1331, 1331,  200,  200,  200,  200, 1331,  200,  200,  200,
      200, 1331,  200,  200,  200,  200,  200,  200,  200, 1331,
      200,  200,  200,  200, 1300,  200, 1292, 1331,  200,  200,

      200,  200,  200,  200,  200,  200,  200,  200, 1331,  200,
      200,  200,  200, 1331,  200,  200, 1331, 1331, 1331,  200,
      200, 1300, 1331, 1331, 1331,  200,  200,  200, 1331,  200,
      200,  200, 1331, 1331,  200, 1331, 1331, 1331, 1331,  200,
       46,   46,   46,   46,   46,   88, 1331, 1331,   88,   88,
      185,  185,  185, 1331,  185,  187, 1331,  187,  187,  187,
      190, 1331,  190,  190,  190,  200, 1331,  200,  200,  200,
        7, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,
     1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,
     1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,

     1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,
     1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,
     1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,
     1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,
     1331, 1331, 1331, 1331, 1331, 1331, 1331
    } ;

static const flex_int16_t yy_chk[8848] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    5,    5,    5,    5,
        5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
        5,    5,    5,    5,    5,    5,    5,    5,    5,    5,

        5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
        5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
        5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
        5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
        5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
        5,    5,    9,    9,   10,   10,   15,   16,   16,   16,
       16,   16,   16,   16,   16,   16,   16,   15,   17,   17,
       17,   17,   17,   17,   17,   17,   17,   17,   17,   18,
       18,   21,   22,   21,   27,   28,   22,   23,   30,   21,
       27,   21,   22,   28,   23,   21,   21,   31,   41,   28,

       41,   23,   22,   41,   23, 1334,   44,   23,   21,   22,
       21,   27,   28,   22,   23,   30,   21,   27,   21,   22,
       28,   23,   21,   21,   31,   41,   28,   41,   23,   22,
       41,   23,   24,   44,   23,   25,   24,   25,   49,   49,
       24,   32,   25,   99,   33,   32,   24,   25,   33,   24,
       55,   32,   50,   50,   33,   43,   43,   58,   58,   24,
     1328,   55,   25,   24,   25, 1327,   34,   24,   32,   25,
       99,   33,   32,   24,   25,   33,   24,   26,   32,   36,
       34,   33,   43,   43,  100,   26,   34,   34,   26,  103,
       36,   26,   29,   34,   26, 1326,   36,   26,   29,   29,

       29,   42,   86,   86,   26,   29,   36,   34, 1325,   42,
     1323,  100,   26,   34,   34,   26,  103,   36,   26,   29,
     1321,   26,   35,   36,   26,   29,   29,   29,   42,  104,
       35,   38,   29,  101,   35,   38,   42,   35,   35,   38,
       81,  101,   81,  106,   81,   38, 1319,   81, 1317,   35,
      102, 1315,   81,  102, 1313,  107,  104,   35,   38, 1311,
      101,   35,   38, 1309,   35,   35,   38,   81,  101,   81,
      106,   81,   38,   39,   81,   39,  110,  102,   39,   81,
      102,   39,  107,   39,  113,   39,   39,   56,   56,   56,
       56,   56,   56,   56,   56,   56,   56,  188,  188, 1307,

       39, 1305,   39,  110, 1303,   39,  105,  105,   39,  109,
       39,  113,   39,   39,   40,  112,  109,   67,   40,   67,
      105,   40,   40,   67,   67,  112,  116,  116,   40,   67,
      117,   40, 1301,  105,  105, 1297,  109, 1133, 1133, 1256,
     1256,   40,  112,  109,   67,   40,   67,  105,   40,   40,
       67,   67,  112,  116,  116,   40,   67,  117,   40,   57,
       57,   57,   57,   57,   57,   57,   57,   57,   57,   57,
       61,   61,  118,   61,   61,   61,   61,   61,   61,  119,
       68,  114, 1293, 1287,   68, 1281,  114,  120,   68,   61,
       61,   61,   70,  121,   68,   61,   70,   61,  123,  118,

      124,  125,   70,   61,   68,   61,  119,   68,  114,   61,
       61,   68,   70,  114,  120,   68,  127,  128, 1268,   70,
      121,   68,   61,   70,   61,  123, 1255,  124,  125,   70,
       61,   68,   61, 1233,  129, 1211,   61,   61, 1168,   70,
     1125, 1053,  981,  127,  128,   61,   62,   62,  885,   62,
       62,   62,   62,   62,   62,  789,  664,  130,   71,   72,
       73,  129,   71,   72,   73,   62,   62,   62,   71,   72,
       73,  131,   62,   77,  131,  126,   62,   77,   71,   72,
       73,  126,   62,   77,  130,   71,   72,   73,  132,   71,
       72,   73,   62,   77,  539,   71,   72,   73,  131,   62,

       77,  131,  126,   62,   77,   71,   72,   73,  126,   62,
       77,  414,  288,   83,  133,  132,   83,   83,  135,   62,
       77,   62,   63,   83,   78,  136,   63,  138,   78,   63,
       63,  139,   78,   83,  140,  141,   63,  144,   78,   63,
       83,  133,   63,   83,   83,  135,   63,  192,   78,   63,
       83,   78,  136,   63,  138,   78,   63,   63,  139,   78,
       83,  140,  141,   63,  144,   78,   63,  190,   90,   63,
       88,   85,   82,   63,   64,   78,   82,   84,   64,  137,
       82,   84,   64,  142,  145,  142,   82,   84,   64,  137,
      146,   64,  147,  149,  137,  137,   82,   84,   64,   82,

       60,   64,   59,   82,   84,   64,  137,   82,   84,   64,
      142,  145,  142,   82,   84,   64,  137,  146,   64,  147,
      149,  137,  137,   82,   84,   64,   65,  111,  111,  111,
       65,  111,  115,   65,  115,   65,   65,  115,  150,  143,
       65,  143,  151,  115,  153,   65,   65,   52,  148,  154,
      115,  148,  143,   65,  111,  111,  111,   65,  111,  115,
       65,  115,   65,   65,  115,  150,  143,   65,  143,  151,
      115,  153,   65,   65,   66,  148,  154,  115,  148,  143,
      155,   66,   66,  156,  157,   66,  158,  122,   66,  122,
      158,   66,  159,  122,   66,   51,  161,   47,   45,  163,

      122,   66,   20,   19,  122,   11,  166,  155,   66,   66,
      156,  157,   66,  158,  122,   66,  122,  158,   66,  159,
      122,   66,   69,  161,  160,  162,  163,  122,   69,   69,
       69,  122,  165,  166,  167,   69,  162,  160,  168,  169,
       69,    7,  160,    4,  170,    3,  165,  172,    0,   69,
      173,  160,  162,  174,  177,   69,   69,   69,    0,  165,
        0,  167,   69,  162,  160,  168,  169,   69,   74,  160,
      164,  170,  171,  165,  172,   74,  175,  173,  178,  164,
      174,  177,   74,  171,  176,   74,  179,  164,   74,   74,
        0,  176,  179,    0,  176,   74,  175,  164,  181,  171,

      182,  183,   74,  175,    0,  178,  164,    0,  291,   74,
      171,  176,   74,  179,  164,   74,   74,   75,  176,  179,
      180,  176,   75,  175,   75,  181,  293,  182,  183,  294,
       75,   75,  200,  180,   75,  291,  200,   75,   75,    0,
        0,    0,  200,  296,   75,    0,    0,  180,  298,   75,
        0,   75,  200,  293,    0,  299,  294,   75,   75,  200,
      180,   75,  300,  200,   75,   75,   76,  201,  201,  200,
      296,  301,  201,   76,    0,  298,  302,   76,  201,  200,
       76,  202,  299,   76,  303,  202,   76,    0,  201,  300,
      305,  202,    0,   76,  201,  201,  202,    0,  301,  201,

       76,  202,  306,  302,   76,  201,  307,   76,  202,    0,
       76,  303,  202,   76,   79,  201,   79,  305,  202,   79,
       79,    0,   79,  202,   79,  308,   79,   79,  202,  306,
      309,  310,  311,  307,  312,  313,   79,    0,  314,    0,
        0,   79,    0,   79,    0,    0,   79,   79,  203,   79,
      203,   79,  308,   79,   79,  203,  203,  309,  310,  311,
      203,  312,  313,   79,   80,  314,  209,  205,   80,  205,
      209,   80,   80,  205,  205,  203,  209,  203,   80,  205,
      315,   80,  203,  203,  316,  317,  209,  203,   80,    0,
        0,   80,    0,  209,  205,   80,  205,  209,   80,   80,

      205,  205,    0,  209,    0,   80,  205,  315,   80,    0,
        0,  316,  317,  209,    0,   80,   91,   91,   91,   91,
       91,   91,   91,   91,   91,   91,   92,   92,   92,   92,
       92,   92,   92,   92,   92,   92,   93,   93,   93,   93,
       93,   93,   93,   93,   93,   93,   93,  193,  193,  193,
      193,  193,  193,  193,  193,  193,  193,  194,  194,  194,
      194,  194,  194,  194,  194,  194,  194,  195,  195,  195,
      195,  195,  195,  195,  195,  195,  195,  195,  204,  206,
      208,  204,  304,  319,  304,  208,  320,  208,  206,  204,
      206,  204,  208,  321,    0,  206,  204,  208,    0,  304,

      206,  204,    0,    0,    0,  204,  206,  208,  204,  304,
      319,  304,  208,  320,  208,  206,  204,  206,  204,  208,
      321,  210,  206,  204,  208,  210,  304,  206,  204,  207,
      207,  210,  211,  322,  211,  207,  318,  207,    0,  211,
        0,  210,  207,  207,  211,    0,  323,  207,  210,  324,
        0,  318,  210,  326,  212,  327,  207,  207,  210,  211,
      322,  211,  207,  318,  207,  212,  211,  212,  210,  207,
      207,  211,  212,  323,  207,    0,  324,  212,  318,    0,
      326,  212,  327,  214,    0,    0,  328,  214,  329,  330,
        0,    0,  212,  214,  212,  331,  332,  333,  215,  212,

        0,  334,  215,  214,  212,  213,  213,  213,  215,  213,
      214,  215,  213,  328,  214,  329,  330,  213,  215,  335,
      214,  336,  331,  332,  333,  215,  337,  216,  334,  215,
      214,  216,  213,  213,  213,  215,  213,  216,  215,  213,
      338,  339,  216,  340,  213,  215,  335,  216,  336,  341,
        0,  342,  343,  337,  216,  344,    0,  345,  216,    0,
      346,    0,    0,    0,  216,  347,    0,  338,  339,  216,
      340,    0,  348,  349,  216,  217,  341,  217,  342,  343,
      217,  351,  344,  218,  345,  218,  217,  346,  217,  218,
      218,    0,  347,  217,  219,  218,  219,    0,  217,  348,

      349,  219,  217,  219,  217,  352,  219,  217,  351,  354,
      218,    0,  218,  217,  220,  217,  218,  218,  220,    0,
      217,  219,  218,  219,  220,  217,    0,  355,  219,  350,
      219,    0,  352,  219,  220,  221,  354,  221,  222,  350,
        0,  220,  221,    0,  356,  220,  222,  221,  222,    0,
        0,  220,  357,  222,  355,    0,  350,  359,  222,    0,
      360,  220,  221,  361,  221,  222,  350,  223,  362,  221,
      225,  356,  225,  222,  221,  222,  223,  225,  223,  357,
      222,  363,  225,  223,  359,  222,  358,  360,  223,    0,
      361,    0,    0,  365,  223,  362,  358,  225,  366,  225,

        0,    0,    0,  223,  225,  223,    0,  367,  363,  225,
      223,    0,  226,  358,  226,  223,  224,  227,  224,  226,
      365,  227,  224,  358,  226,  366,  368,  227,  224,  224,
      369,  370,  371,  224,  367,    0,    0,  227,  224,  226,
        0,  226,    0,  224,  227,  224,  226,  373,  227,  224,
      375,  226,    0,  368,  227,  224,  224,  369,  370,  371,
      224,  228,  229,  228,  227,  224,  229,  228,  228,  230,
        0,  230,  229,  228,  373,  376,  230,  375,    0,  374,
        0,  230,  229,    0,    0,    0,  377,  231,  228,  229,
      228,  231,  374,  229,  228,  228,  230,  231,  230,  229,

      228,  232,  376,  230,  378,  232,  374,  231,  230,  229,
      233,  232,  233,  377,  231,  379,  380,  233,  231,  374,
      233,  232,  233,  234,  231,  234,    0,    0,  232,    0,
      234,  378,  232,    0,  231,  234,    0,  233,  232,  233,
      381,  382,  379,  380,  233,    0,  383,  233,  232,  233,
      234,  235,  234,  235,  236,  237,  236,  234,  235,  237,
      235,  236,  234,  235,  384,  237,  236,  381,  382,  385,
      372,    0,  240,  383,  240,  237,  387,    0,  235,  240,
      235,  236,  237,  236,  240,  235,  237,  235,  236,  372,
      235,  384,  237,  236,  238,  388,  385,  372,  238,  240,

      389,  240,  237,  387,  238,  238,  240,    0,  241,  242,
        0,  240,  241,  242,  238,    0,  372,  390,  241,  242,
        0,  238,  388,    0,  391,  238,    0,  389,  241,  242,
        0,  238,  238,  243,  243,  241,  242,  243,  392,  241,
      242,  238,  239,  243,  390,  241,  242,  247,  393,  247,
      239,  391,  239,  243,  247,  241,  242,  239,  239,  247,
      243,  243,  239,    0,  243,  392,  394,    0,    0,  239,
      243,    0,    0,    0,  247,  393,  247,  239,  395,  239,
      243,  247,    0,    0,  239,  239,  247,  396,  397,  239,
      244,  399,    0,  394,  244,  246,  244,  246,  244,  257,

      244,  246,  246,  257,  401,  395,    0,  246,  403,  257,
      244,    0,  404,    0,  396,  397,    0,  244,  399,  257,
        0,  244,  246,  244,  246,  244,  257,  244,  246,  246,
      257,  401,  405,  400,  246,  403,  257,  244,  245,  404,
      245,  248,  400,  248,  406,  402,  257,  402,  248,  245,
      249,  245,  249,  248,  407,  408,  245,  249,  249,  405,
      400,  245,  249,    0,  409,  245,    0,  245,  248,  400,
      248,  406,  402,    0,  402,  248,  245,  249,  245,  249,
      248,  407,  408,  245,  249,  249,    0,    0,  245,  249,
      250,  409,  250,  251,    0,  251,  411,  250,  250,    0,

      251,  250,  250,  252,    0,  251,  254,  410,  254,  412,
        0,  410,    0,  254,  252,    0,  252,  250,  254,  250,
      251,  252,  251,  411,  250,  250,  252,  251,  250,  250,
      252,  253,  251,  254,  410,  254,  412,  253,  410,  253,
      254,  252,  255,  252,  253,  254,    0,  540,  252,  253,
      255,    0,  255,  252,    0,    0,  541,  255,  253,  542,
      543,    0,  255,  544,  253,  545,  253,  546,  547,  255,
      548,  253,  550,  256,  540,  256,  253,  255,  258,  255,
      256,  256,  258,  541,  255,  256,  542,  543,  258,  255,
      544,  258,  545,    0,  546,  547,    0,  548,  258,  550,

      256,  553,  256,    0,    0,  258,  259,  256,  256,  258,
      259,  554,  256,  555,  259,  258,  556,  558,  258,  260,
      259,  559,    0,  260,  261,  258,  261,  560,  553,  260,
      259,  261,  561,  259,  563,    0,  261,  259,  554,  260,
      555,  259,  564,  556,  558,    0,  260,  259,  559,  262,
      260,  261,    0,  261,  560,  565,  260,  259,  261,  561,
      262,  563,  262,  261,    0,  566,  260,  262,  263,  564,
      567,  568,  262,  263,  569,  263,  262,  570,    0,  264,
      263,  264,  565,  571,    0,  263,  264,  262,    0,  262,
      264,  264,  566,    0,  262,  263,  574,  567,  568,  262,

      263,  569,  263,  575,  570,  265,  264,  263,  264,  265,
      571,  265,  263,  264,  576,  268,  265,  264,  264,  268,
      267,  265,    0,  574,  267,  268,  577,  578,  267,  579,
      575,    0,  265,    0,  267,  268,  265,    0,  265,    0,
        0,  576,  268,  265,  267,  580,  268,  267,  265,  266,
        0,  267,  268,  577,  578,  267,  579,    0,  266,  269,
      266,  267,  268,  269,  270,  266,  266,  269,  270,    0,
      266,  267,  580,  269,  270,    0,  266,  270,  582,    0,
      583,  584,    0,  269,  270,  266,  269,  266,  587,  588,
      269,  270,  266,  266,  269,  270,  272,  266,  271,  272,

      269,  270,  271,  272,  270,  582,  271,  583,  584,  272,
      269,  270,  271,    0,    0,  587,  588,  274,  590,  272,
        0,  274,  271,  272,    0,  271,  272,  274,    0,  271,
      272,  592,    0,  271,    0,  593,  272,  274,  594,  271,
      273,  273,  273,  595,  274,  590,  272,  273,  274,  271,
        0,  275,  273,  596,  274,  275,    0,    0,  592,  275,
      597,  275,  593,  600,  274,  594,    0,  273,  273,  273,
      595,  275,    0,  276,  273,    0,  604,  276,  275,  273,
      596,  606,  275,  276,  276,  277,  275,  597,  275,  607,
      600,  609,  277,  276,    0,  611,  279,  279,  275,  277,

      276,  279,  277,  604,  276,  277,    0,  279,  606,    0,
      276,  276,  277,    0,    0,  612,  607,  279,  609,  277,
      276,  280,  611,  279,  279,  280,  277,    0,  279,  277,
      613,  280,  277,  278,  279,  615,  278,    0,    0,  598,
      278,  280,  612,  278,  279,  616,  278,  281,  280,  281,
      598,  282,  280,  281,  281,  282,  278,  613,  280,  281,
      278,  282,  615,  278,  282,  283,  598,  278,  280,  283,
      278,  282,  616,  278,  281,  283,  281,  598,  282,    0,
      281,  281,  282,  278,    0,  283,  281,    0,  282,  284,
      617,  282,  283,  284,    0,  618,  283,  285,  282,  284,

      620,  285,  283,  621,  284,  285,  420,  623,  420,  284,
      624,  285,  283,  420,  610,  610,  284,  617,  420,  625,
      284,  285,  618,    0,  285,    0,  284,  620,  285,    0,
      621,  284,  285,  420,  623,  420,  284,  624,  285,    0,
      420,  610,  610,    0,    0,  420,  625,    0,  285,  289,
      289,  289,  289,  289,  289,  289,  289,  289,  289,  415,
      415,  415,  415,  415,  415,  415,  415,  415,  415,  416,
      417,  418,  419,  416,  417,  418,  419,  626,  628,  416,
      417,  418,  419,    0,  629,  630,    0,  631,    0,  416,
      417,  418,  419,    0,    0,    0,  416,  417,  418,  419,

      416,  417,  418,  419,  626,  628,  416,  417,  418,  419,
      422,  629,  630,  421,  631,  421,  416,  417,  418,  419,
      421,  422,  423,  422,  423,  421,  424,    0,  422,  423,
      424,    0,  633,  422,  423,    0,  424,  422,  634,    0,
      421,  635,  421,  636,    0,    0,  424,  421,  422,  423,
      422,  423,  421,  424,  425,  422,  423,  424,  425,  633,
      422,  423,  426,  424,  425,  634,  426,  427,  635,  639,
      636,  427,  426,  424,  425,    0,  428,  427,  428,  640,
      426,  425,  426,  428,  641,  425,  642,  427,  428,  426,
      429,  425,  429,  426,  427,  643,  639,  429,  427,  426,

      646,  425,  429,  428,  427,  428,  640,  426,  431,  426,
      428,  641,  431,  642,  427,  428,    0,  429,  431,  429,
        0,  431,  643,    0,  429,    0,    0,  646,  431,  429,
      430,    0,  430,  432,  647,  431,  430,  432,  649,  431,
      650,    0,  430,  432,  651,  431,  433,  430,  431,  432,
      433,    0,  430,  432,  433,  431,  433,  430,  435,  430,
      432,  647,  435,  430,  432,  649,  433,  650,  435,  430,
      432,  651,    0,  433,  430,    0,  432,  433,  435,  430,
      432,  433,  434,  433,    0,  435,  434,    0,  652,  435,
      436,  653,  434,  433,  436,  435,  654,  434,  436,  437,

      655,  437,  434,    0,  436,  435,  437,  656,    0,  434,
        0,  437,    0,  434,  436,  652,  657,  436,  653,  434,
      659,  436,  661,  654,  434,  436,  437,  655,  437,  434,
      438,  436,  438,  437,  656,  439,  438,  438,  437,  439,
      663,  436,  438,  657,  440,  439,  440,  659,  792,  661,
      794,  440,  796,    0,  797,  439,  440,  438,  441,  438,
      441,  798,  439,  438,  438,  441,  439,  663,    0,  438,
      441,  440,  439,  440,    0,  792,  442,  794,  440,  796,
      442,  797,  439,  440,    0,  441,  442,  441,  798,  799,
      443,  444,  441,  800,  443,  444,  442,  441,  443,    0,

      801,  444,  802,  442,  443,  803,  444,  442,    0,    0,
      804,  444,    0,  442,  443,  806,  799,  443,  444,    0,
      800,  443,  444,  442,  445,  443,  445,  801,  444,  802,
      445,  443,  803,  444,  807,  446,  445,  804,  444,  446,
        0,  443,  806,    0,  447,  446,  445,    0,  447,  808,
      809,  445,  810,  445,  447,  446,    0,  445,    0,  447,
        0,  807,  446,  445,  447,    0,  446,  448,    0,    0,
      448,  447,  446,  445,  448,  447,  808,  809,    0,  810,
      448,  447,  446,  450,  811,  449,  447,  450,  451,  449,
      448,  447,  451,  450,  448,  449,  449,  448,  451,  813,

      814,  448,    0,  450,  815,  449,    0,  448,  451,    0,
      450,  811,  449,  816,  450,  451,  449,  448,    0,  451,
      450,    0,  449,  449,  453,  451,  813,  814,  453,  452,
      450,  815,  449,  452,  453,  451,  454,  452,  454,    0,
      816,    0,  454,  452,  453,  455,    0,  455,  454,  817,
      818,  453,  455,  452,  819,  453,  452,  455,  454,  820,
      452,  453,    0,  454,  452,  454,  456,    0,  456,  454,
      452,  453,  455,  456,  455,  454,  817,  818,  456,  455,
      452,  819,  821,  822,  455,  454,  820,  458,    0,  458,
      457,    0,  457,  456,  458,  456,  457,  457,    0,  458,

      456,    0,  457,    0,  825,  456,  826,    0,  459,  821,
      822,    0,  459,  461,  458,  461,  458,  457,  459,  457,
      461,  458,  827,  457,  457,  461,  458,    0,  459,  457,
      460,  825,  828,  826,  460,  459,    0,    0,  460,  459,
      461,    0,  461,    0,  460,  459,    0,  461,  829,  827,
        0,    0,  461,  832,  460,  459,  835,  460,  836,  828,
      838,  460,  462,  462,  462,  460,  463,  463,  463,  462,
      464,  460,  464,  463,  462,  829,  837,  464,  463,  837,
      832,  460,  464,  835,    0,  836,    0,  838,    0,  462,
      462,  462,    0,  463,  463,  463,  462,  464,  465,  464,

      463,  462,  841,  837,  464,  463,  837,  467,  465,  464,
      465,  467,  466,  467,  466,  465,  844,  845,  467,  466,
      465,  466,    0,  467,  466,  465,    0,  846,  847,  841,
      848,  849,    0,  852,  467,  465,  853,  465,  467,  466,
      467,  466,  465,  844,  845,  467,  466,  465,  466,  468,
      467,  466,  469,  468,  846,  847,  469,  848,  849,  468,
      852,  470,  469,  853,    0,  470,    0,  469,  854,  468,
        0,  470,  469,  855,  470,    0,  468,  856,    0,  469,
      468,  470,    0,  469,  858,    0,  468,  471,  470,  469,
        0,  471,  470,  471,  469,  854,  468,  471,  470,  469,

      855,  470,  472,  859,  856,  473,  472,  471,  470,  473,
        0,  858,  472,  861,  471,  473,    0,  474,  471,  862,
      471,  474,  472,  863,  471,  473,    0,  474,    0,  472,
      859,  475,  473,  472,  471,  475,  473,  474,    0,  472,
      861,  475,  473,  476,  474,  864,  862,  476,  474,  472,
      863,  475,  473,  476,  474,  477,    0,    0,  475,  477,
      866,    0,  475,  476,  474,  477,    0,  867,  475,  868,
      476,  477,  864,  869,  476,  477,    0,  478,  475,  478,
      476,  479,  477,  479,  478,    0,  477,  866,  479,  478,
      476,    0,  477,  479,  867,    0,  868,  870,  477,  481,

      869,    0,  477,  481,  478,  480,  478,  480,  479,  481,
      479,  478,  480,  480,    0,  479,  478,  480,  482,  481,
      479,  485,  482,  485,  870,  485,  481,  871,  482,    0,
      481,  485,  480,    0,  480,  872,  481,  873,  482,  480,
      480,  485,  483,  483,  480,  482,  481,  483,  485,  482,
      485,  874,  485,  483,  871,  482,  488,  486,  485,  486,
      488,  486,  872,  483,  873,  482,  488,  486,  485,  483,
      483,  488,    0,  876,  483,    0,  488,  486,  874,  877,
      483,    0,  878,  488,  486,    0,  486,  488,  486,  880,
      483,  484,  487,  488,  486,  484,  487,    0,  488,  484,

      876,  484,  487,  488,  486,  484,  877,  881,  487,  878,
        0,  882,  487,    0,    0,  484,  880,  489,  484,  487,
      490,  489,  484,  487,  490,  982,  484,  489,  484,  487,
      490,  984,  484,  489,  881,  487,  986,  489,  882,  487,
      490,  491,  484,  987,  489,  491,    0,  490,  489,  491,
      988,  490,  982,    0,  489,  491,  989,  490,  984,  492,
      489,  492,    0,  986,  489,  491,  492,  490,  491,  991,
      987,  492,  491,  493,    0,  493,  491,  988,    0,  493,
      493,  993,  491,  989,    0,  493,  492,  494,  492,  494,
      994,  495,  491,  492,  494,  495,  991,    0,  492,  494,

      493,  495,  493,    0,  995,  496,  493,  493,  993,  496,
      996,  495,  493,    0,  494,  496,  494,  994,  495,    0,
      497,  494,  495,  997,  497,  496,  494,  499,  495,  499,
      497,  995,  496,  497,  499,    0,  496,  996,  495,  499,
      497,    0,  496,  999, 1001,    0,  503,  497,  503,    0,
      997,  497,  496,  503,  499,    0,  499,  497,  503, 1005,
      497,  499,  501,  501,  501,    0,  499,  497,  498,  501,
      999, 1001,  498,  503,  501,  503,  498, 1007,    0, 1008,
      503, 1009,  498, 1016, 1017,  503, 1005,  498, 1018,  501,
      501,  501,  498,    0, 1021,  498,  501,    0, 1023,  498,

      502,  501,    0,  498, 1007,  502, 1008,  502, 1009,  498,
     1016, 1017,  502, 1025,  498, 1018,  505,  502,  505,  498,
      500, 1021, 1027,  505,  505, 1023, 1028,  502,  505,  500,
        0,  500,  502,  500,  502, 1029,  500,    0,    0,  502,
     1025,  500,    0,  505,  502,  505,  504,  500, 1030, 1027,
      505,  505, 1031, 1028, 1033,  505,  500,  504,  500,  504,
      500,  506, 1029,  500,  504,  506, 1034,  506,  500,  504,
     1035, 1036,  506,  504,    0, 1030,    0,  506,  507, 1031,
      507, 1033, 1038,    0,  504,  507,  504,    0,  506,    0,
      507,  504,  506, 1034,  506, 1040,  504, 1035, 1036,  506,

     1042,  508,    0,  508,  506,  507,  509,  507,  508, 1038,
      509,  510,  507,  508,  511,  510,  509,  507,  511,  512,
     1043,  510, 1040,  512,  511, 1044,  509, 1042,  508,  512,
      508,  510,    0,  509,  511,  508,    0,  509,  510,  512,
      508,  511,  510,  509,    0,  511,  512, 1043,  510,    0,
      512,  511, 1044,  509,    0,  513,  512,    0,  510,  513,
      514,  511,    0,  515,  514,  513,  512,  515,  513,  516,
      514,  516, 1045,  515,  515,  513,  516,  517,  514,  517,
      514,  516,  513,  515,  517,  517,  513,  514,    0,  517,
      515,  514,  513,    0,  515,  513,  516,  514,  516, 1045,

      515,  515,  513,  516,  517,  514,  517,  514,  516, 1046,
      515,  517,  517,  518, 1051,  518,  517,  519, 1052,  519,
      518,  518, 1130, 1134,  519,  518,  520, 1135,  520,  519,
      520, 1138,    0,  520,    0,    0, 1046,    0,  520,    0,
      518, 1051,  518, 1139,  519, 1052,  519,  518,  518, 1130,
     1134,  519,  518,  520, 1135,  520,  519,  520, 1138,  521,
      520,  521,  522, 1143,  522,  520,  521,  521,    0,  522,
     1139,  521,  523, 1145,  522,  524,  523,    0, 1146,  524,
     1149,    0,  523,    0,    0,  524,  521, 1150,  521,  522,
     1143,  522,  523,  521,  521,  524,  522,    0,  521,  523,

     1145,  522,  524,  523,  526, 1146,  524, 1149,  525,  523,
      525,  526,  524,  526, 1150,  525, 1151,    0,  526,  523,
      525, 1152,  524,  526,  527,    0,  529,    0,  527,  528,
      529,  526,    0,  528,  527,  525,  529,  525,  526,  528,
      526,  528,  525, 1151,  527,  526,  529,  525, 1152,  528,
      526,  527,  531,  529,  531,  527,  528,  529, 1155,  531,
      528,  527,    0,  529,  531,    0,  528,    0,  528, 1156,
      530,  527,    0,  529,  530, 1159,  528, 1161,  530,  531,
      532,  531,  532,    0,  530, 1155,  531,  532,    0,  532,
     1162,  531,  532,    0,  530,    0, 1156,  530, 1163,  533,

     1164,  530, 1159, 1165, 1161,  530,    0,  532,  533,  532,
      533,  530, 1212, 1215,  532,  533,  532, 1162, 1218,  532,
      533,  530,  534, 1219,  534, 1163,  533, 1164, 1222,  534,
     1165,    0,    0,  534,  534,  533,  535,  533,  535, 1212,
     1215,    0,  533,  535,  535, 1218,    0,  533,  535,  534,
     1219,  534,  536, 1223,  536, 1222,  534, 1224,  536,  536,
      534,  534,  537,  535,  536,  535,  537, 1228,  537,    0,
      535,  535, 1229,  537,    0,  535,    0,    0,  537,  536,
     1223,  536, 1213, 1213, 1224,  536,  536, 1230, 1232,  537,
      538,  536,  538,  537, 1228,  537,  538,  538, 1257, 1229,

      537,  665,  538,  665,  666,  537,  666,  665,  665, 1258,
      666,  666, 1261,  665, 1230, 1232,  666,  538,    0,  538,
     1213,    0,  667,  538,  538, 1257,  667,    0,  665,  538,
      665,  666,  667,  666,  665,  665, 1258,  666,  666, 1261,
      665,  668,  667,  666,  669,  668,  671, 1213,  669,  667,
      671,  668, 1266,  667,  669, 1270,  671,  669,  670,  667,
      670,  668, 1282, 1282,  669,  670,  671,    0,  668,  667,
      670,  669,  668,  671,    0,  669,    0,  671,  668, 1266,
        0,  669, 1270,  671,  669,  670, 1283,  670,  668, 1286,
        0,  669,  670,  671,  672,  673,  672,  670,  674,  673,

        0,  672,  674,  675, 1282,  673,  672,  675,  674,    0,
        0,  675, 1289, 1283,    0,  673, 1286,  675,  674,    0,
        0,  672,  673,  672,    0,  674,  673,  675,  672,  674,
      675, 1282,  673,  672,  675,  674,  676,  677,  675, 1289,
      676,  677,  673, 1294,  675,  674,  676,  677,    0, 1298,
        0,  678, 1302, 1304,  675,  678,  676,  677,    0,  678,
     1235, 1235,    0,  676,  677,  678,    0,  676,  677, 1306,
     1294,    0,    0,  676,  677,  678, 1298,  679,  678, 1302,
     1304,  679,  678,  676,  677,  679,  678,  679, 1308,  680,
      681,  682,  678,  680,  681,  682, 1306,  679, 1235,  680,

      681,  682,  678,  681,  679, 1310,    0,    0,  679,  680,
      681,  682,  679,    0,  679, 1308,  680,  681,  682,  683,
      680,  681,  682,  683,  679, 1235,  680,  681,  682,  683,
      681, 1312, 1310,  684,  683,  684,  680,  681,  682,  683,
      684, 1314,    0,  685,  686,  684,  683,  685,  686,    0,
      683,    0,    0,  685,  686, 1316,  683, 1318, 1312,  686,
      684,  683,  684,  685,  686,    0,  683,  684, 1314,  687,
      685,  686,  684,  687,  685,  686,  688,    0,  688,  687,
      685,  686, 1316,  688, 1318, 1320,  686,    0,  688,  687,
      685,  686,  689,  689,  689,    0,  687, 1288, 1288,  689,

      687,    0,    0,  688,  689,  688,  687,    0,    0,    0,
      688,    0, 1320,    0,    0,  688,  687,    0,    0,  689,
      689,  689,    0,  690,    0,  690,  689,    0,    0,    0,
      690,  689,  690,    0,  691,  690,  691,    0,    0, 1288,
        0,  691,  691,    0,    0,    0,  691,  692,    0,    0,
      690,    0,  690,    0,    0,    0,    0,  690,  692,  690,
      692,  691,  690,  691,    0,  692, 1288,    0,  691,  691,
      692,    0,    0,  691,  692,  694,  693,    0,  693,    0,
        0,    0,  693,  693,  694,  692,  694,  692,  693,    0,
        0,  694,  692,    0,  695,    0,  694,  692,  695,    0,

        0,    0,  694,  693,  695,  693,    0,    0,    0,  693,
      693,  694,    0,  694,  695,  693,    0,  696,  694,  696,
      697,  695,  697,  694,  696,  695,  698,  697,  698,  696,
      705,  695,  697,  698,  705,    0,    0,    0,  698,    0,
      705,  695,    0,    0,  696,    0,  696,  697,    0,  697,
      705,  696,    0,  698,  697,  698,  696,  705,    0,  697,
      698,  705,  699,    0,  699,  698,  699,  705,  700,  699,
      700,    0,  700,    0,  699,  700,  701,  705,  701,    0,
      700,    0,    0,  701,    0,  701,    0,    0,  701,  699,
        0,  699,    0,  699,    0,  700,  699,  700,  703,  700,

        0,  699,  700,  701,    0,  701,  702,  700,  702,  703,
      701,  703,  701,  702,  702,  701,  703,    0,  702,  704,
        0,  703,    0,    0,  706,  703,    0,    0,  706,    0,
      704,    0,  704,  702,  706,  702,  703,  704,  703,    0,
      702,  702,  704,  703,  706,  702,  704,    0,  703,    0,
      707,  706,  708,    0,  707,  706,  708,  704,    0,  704,
      707,  706,  708,    0,  704,  707,    0,    0,    0,  704,
      707,  706,  708,    0,  709,    0,  710,  707,  709,  708,
      710,  707,  709,  708,    0,    0,  710,  707,  709,  708,
      711,    0,  707,    0,  711,    0,  710,  707,  709,  708,

      711,  709,    0,  710,  712,  709,    0,  710,  712,  709,
      711,    0,  712,  710,    0,  709,    0,  711,  712,    0,
      714,  711,  713,  710,  714,  709,  713,  711,  712,    0,
      714,  712,  713,  713,  715,  712,  715,  711,    0,  712,
      714,  715,  713,    0,    0,  712,  715,  714,    0,  713,
        0,  714,    0,  713,    0,  712,    0,  714,    0,  713,
      713,  715,    0,  715,  716,    0,  716,  714,  715,  713,
      717,  716,  717,  715,  718,  719,  716,  717,  718,  719,
        0,    0,  717,    0,  718,  719,    0,    0,  719,    0,
        0,  716,    0,  716,  718,  719,    0,  717,  716,  717,

        0,  718,  719,  716,  717,  718,  719,    0,    0,  717,
        0,  718,  719,    0,    0,  719,  720,    0,  720,    0,
        0,  718,  719,  720,  721,    0,  721,  722,  720,  722,
      721,  721,    0,  722,  722,    0,  721,    0,    0,  722,
        0,    0,    0,  720,    0,  720,    0,    0,    0,    0,
      720,  721,    0,  721,  722,  720,  722,  721,  721,    0,
      722,  722,    0,  721,    0,    0,  722,  723,  724,  725,
      724,  725,  723,    0,  723,  724,  725,    0,  723,  723,
      724,  725,    0,    0,  723,    0,    0,    0,    0,    0,
        0,    0,    0,    0,  723,  724,  725,  724,  725,  723,

        0,  723,  724,  725,    0,  723,  723,  724,  725,    0,
      726,  723,  726,  727,  728,  727,  728,  726,    0,    0,
      727,  728,  726,    0,    0,  727,  728,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,  726,    0,  726,
      727,  728,  727,  728,  726,    0,    0,  727,  728,  726,
        0,    0,  727,  728,  729,    0,  729,  730,  731,  730,
      731,  729,  729,    0,  730,  731,  729,    0,    0,  730,
      731,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,  729,    0,  729,  730,  731,  730,  731,  729,  729,
      732,  730,  731,  729,  732,  733,  730,  731,  734,  733,

      732,    0,  734,  736,  735,  733,  735,  736,  734,  736,
      732,  735,  735,    0,  736,  733,  735,  732,  734,  736,
        0,  732,  733,    0,    0,  734,  733,  732,    0,  734,
      736,  735,  733,  735,  736,  734,  736,  732,  735,  735,
      738,  736,  733,  735,  738,  734,  736,  737,  739,  737,
      738,    0,  739,  737,  737,    0,    0,    0,  739,  737,
      738,    0,    0,    0,    0,    0,    0,  738,  739,    0,
        0,  738,    0,    0,  737,  739,  737,  738,    0,  739,
      737,  737,  740,    0,  740,  739,  737,  738,  740,    0,
        0,    0,  741,  742,  740,  739,  741,  742,    0,    0,

        0,    0,  741,  742,  740,    0,  742,    0,    0,  740,
        0,  740,  741,  742,    0,  740,    0,    0,    0,  741,
      742,  740,  743,  741,  742,    0,  743,    0,    0,  741,
      742,  740,  743,  742,  744,  743,    0,  745,  744,  741,
      742,  745,  743,    0,  744,  745,  746,  745,    0,  743,
      746,    0,    0,  743,  744,    0,  746,  745,    0,  743,
        0,  744,  743,    0,  745,  744,  746,    0,  745,  743,
        0,  744,  745,  746,  745,  747,  748,  746,    0,  747,
      748,  744,    0,  746,  745,  747,  748,    0,    0,    0,
      749,    0,    0,  746,  749,  747,  748,    0,  749,    0,

        0,    0,  747,  748,  749,    0,  747,  748,    0,    0,
        0,    0,  747,  748,  749,    0,  750,  749,    0,    0,
      750,  749,  747,  748,  750,  749,    0,    0,  751,  752,
      750,  749,  751,  752,    0,    0,    0,    0,  751,  752,
      750,  749,  753,  750,    0,    0,  753,  750,  751,  752,
      753,  750,  753,    0,    0,  751,  752,  750,    0,  751,
      752,    0,  753,    0,    0,  751,  752,  750,  754,  753,
      755,    0,  754,  753,  755,  751,  752,  753,  754,  753,
      755,  754,  757,    0,    0,  755,  757,    0,  754,  753,
      755,  756,  757,    0,  756,  754,    0,  755,  756,  754,

        0,  755,  757,    0,  756,  754,    0,  755,  754,  757,
        0,    0,  755,  757,  756,  754,  758,  755,  756,  757,
      758,  756,    0,  760,  758,  756,  758,    0,  759,  757,
      759,  756,  760,    0,  760,  759,  758,    0,  761,  760,
      759,  756,  761,  758,  760,    0,    0,  758,  761,    0,
      760,  758,    0,  758,    0,  759,    0,  759,  761,  760,
      762,  760,  759,  758,  762,  761,  760,  759,  763,  761,
      762,  760,  763,  765,  764,  761,  764,  765,  763,    0,
      762,  764,    0,  765,    0,  761,  764,  762,  763,    0,
        0,  762,  766,  765,    0,  763,  766,  762,    0,  763,

      765,  764,  766,  764,  765,  763,    0,  762,  764,    0,
      765,    0,  766,  764,  767,  763,    0,  768,  767,  766,
      765,  768,  769,  766,  767,    0,  769,  768,    0,  766,
        0,  771,  769,  771,  767,    0,  770,  768,  771,  766,
      770,  767,  769,  771,  768,  767,  770,    0,  768,  769,
      772,  767,  772,  769,  768,    0,  770,  772,  771,  769,
      771,  767,  772,  770,  768,  771,    0,  770,    0,  769,
      771,  774,    0,  770,  773,    0,  773,  772,    0,  772,
      774,  773,  774,  770,  772,    0,  773,  774,  775,  772,
      775,    0,  774,    0,    0,  775,    0,  775,  774,    0,

      775,  773,    0,  773,  776,    0,  776,  774,  773,  774,
        0,  776,    0,  773,  774,  775,  776,  775,    0,  774,
        0,    0,  775,  777,  775,  777,  778,  775,    0,    0,
      777,  776,  777,  776,  779,  777,  779,  778,  776,  778,
        0,  779,  779,  776,  778,    0,  779,    0,    0,  778,
      777,    0,  777,  778,    0,    0,    0,  777,    0,  777,
      780,  779,  777,  779,  778,  780,  778,  780,  779,  779,
      781,  778,  780,  779,  781,  782,  778,  780,  782,  782,
      781,    0,    0,    0,    0,  782,    0,  780,    0,    0,
      781,    0,  780,    0,  780,  782,    0,  781,    0,  780,

        0,  781,  782,    0,  780,  782,  782,  781,    0,  783,
      784,  785,  782,  783,  784,  785,    0,  781,    0,  783,
      784,  785,  782,  786,  787,    0,  784,  786,  787,  783,
      784,  785,    0,  786,  787,    0,  783,  784,  785,    0,
      783,  784,  785,  786,  787,    0,  783,  784,  785,    0,
      786,  787,    0,  784,  786,  787,  783,  784,  785,    0,
      786,  787,  788,    0,  788,  886,  887,  886,  887,  788,
      786,  787,  886,  887,  788,    0,    0,  886,  887,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,  788,
        0,  788,  886,  887,  886,  887,  788,    0,    0,  886,

      887,  788,    0,    0,  886,  887,  888,  889,  888,  889,
        0,  890,  891,  888,  889,  890,  891,    0,  888,  889,
      888,  890,  891,    0,    0,    0,    0,    0,    0,    0,
        0,  890,  891,  888,  889,  888,  889,    0,  890,  891,
      888,  889,  890,  891,    0,  888,  889,  888,  890,  891,
      892,  893,    0,    0,  892,  893,    0,    0,  890,  891,
      892,  893,    0,  895,  895,  894,  893,    0,  895,  894,
      892,  893,    0,  894,  895,    0,    0,  892,  893,  894,
        0,  892,  893,    0,  895,    0,    0,  892,  893,  894,
      895,  895,  894,  893,    0,  895,  894,  892,  893,    0,

      894,  895,  896,  897,  896,  897,  894,    0,  896,    0,
      897,  895,    0,    0,  896,  897,  894,    0,    0,    0,
        0,    0,    0,    0,  896,    0,  898,    0,  898,  896,
      897,  896,  897,  898,  898,  896,  899,  897,  898,  900,
      899,  896,  897,  900,  901,  902,  899,  902,  901,  900,
        0,  896,  902,  898,  901,  898,  899,  902,  903,  900,
      898,  898,  903,  899,  901,  898,  900,  899,  903,    0,
      900,  901,  902,  899,  902,  901,  900,    0,  903,  902,
        0,  901,    0,  899,  902,  903,  900,    0,  904,  903,
        0,  901,  904,  905,    0,  903,  907,  905,  904,    0,

      907,  905,  906,    0,  906,  903,  907,  905,  904,  906,
        0,    0,    0,    0,  906,  904,  907,  905,    0,  904,
      905,    0,  908,  907,  905,  904,  908,  907,  905,  906,
        0,  906,  908,  907,  905,  904,  906,    0,    0,    0,
        0,  906,  908,  907,  905,    0,  909,  910,    0,  908,
      909,  910,    0,  908,    0,    0,  909,  910,  911,  908,
      911,    0,  910,    0,    0,  911,  909,  910,    0,  908,
      911,  912,    0,  909,  910,  912,  913,  909,  910,    0,
      913,  912,    0,  909,  910,  911,  913,  911,    0,  910,
        0,  912,  911,  909,  910,  914,  913,  911,  912,  914,

        0,    0,  912,  913,  916,  914,  916,  913,  912,    0,
      914,  916,    0,  913,    0,  914,  916,    0,  912,    0,
      915,    0,  914,  913,  915,    0,  914,    0,    0,    0,
      915,  916,  914,  916,    0,  915,    0,  914,  916,    0,
      915,    0,  914,  916,  917,    0,    0,  915,  917,  919,
        0,  915,    0,  919,  917,    0,    0,  915,  923,  919,
      923,    0,  915,    0,  917,  923,  918,  915,  918,  919,
      923,  917,  918,    0,    0,  917,  919,    0,  918,    0,
      919,  917,    0,    0,    0,  923,  919,  923,  918,    0,
      920,  917,  923,  918,  920,  918,  919,  923,  921,  918,

      920,    0,  921,    0,    0,  918,    0,    0,  921,    0,
      920,    0,    0,    0,    0,  918,  921,  920,  921,    0,
        0,  920,    0,    0,  922,  921,    0,  920,  922,  921,
        0,    0,    0,    0,  922,  921,    0,  920,    0,  922,
      924,  925,  924,  921,  922,  921,  925,  924,  925,    0,
        0,  922,  924,  925,    0,  922,    0,    0,  925,    0,
        0,  922,    0,    0,    0,    0,  922,  924,  925,  924,
      926,  922,  926,  925,  924,  925,  927,  926,  927,  924,
      925,    0,  926,  927,  928,  925,  928,    0,  927,    0,
        0,  928,  928,    0,    0,    0,  928,  926,    0,  926,

      929,    0,  929,  927,  926,  927,    0,  929,    0,  926,
      927,  928,  929,  928,  930,  927,  930,    0,  928,  928,
        0,  930,    0,  928,    0,    0,  930,  929,  931,  929,
      931,    0,    0,    0,  929,  931,  931,    0,    0,  929,
      931,  930,  933,  930,  933,    0,  932,    0,  930,  933,
      932,    0,  933,  930,  933,  931,  932,  931,  934,    0,
      934,    0,  931,  931,    0,  934,  932,  931,    0,  933,
      934,  933,    0,  932,    0,    0,  933,  932,    0,  933,
        0,  933,  935,  932,  935,  934,    0,  934,  936,  935,
      936,    0,  934,  932,  935,  936,    0,  934,  937,    0,

      936,    0,    0,    0,    0,    0,  937,    0,  937,  935,
        0,  935,  938,  937,  938,  936,  935,  936,  937,  938,
        0,  935,  936,    0,  938,  937,    0,  936,    0,    0,
      939,    0,  939,  937,  940,  937,    0,  939,  940,  938,
      937,  938,  939,    0,  940,  937,  938,    0,  941,    0,
      941,  938,    0,    0,  940,  941,    0,  939,    0,  939,
      941,  940,    0,    0,  939,  940,    0,  942,    0,  939,
        0,  940,    0,    0,    0,  941,    0,  941,  942,    0,
      942,  940,  941,    0,  943,  942,  943,  941,    0,    0,
      942,  943,  943,  944,  942,    0,  943,  944,    0,  944,

        0,    0,    0,    0,  944,  942,    0,  942,    0,  944,
        0,  943,  942,  943,    0,    0,    0,  942,  943,  943,
      944,    0,    0,  943,  944,  945,  944,  945,  946,    0,
      946,  944,  945,  945,    0,  946,  944,  945,  948,  947,
      946,  947,  948,    0,    0,    0,  947,    0,  948,    0,
        0,  947,  945,    0,  945,  946,    0,  946,  948,  945,
      945,    0,  946,    0,  945,  948,  947,  946,  947,  948,
        0,    0,  949,  947,    0,  948,  949,    0,  947,  950,
      950,    0,  949,    0,  950,  948,    0,  949,    0,    0,
      950,    0,  949,    0,    0,    0,    0,    0,    0,  949,

      950,    0,    0,  949,    0,    0,  950,  950,  951,  949,
        0,  950,  951,    0,  949,    0,  952,  950,  951,  949,
      952,  951,    0,    0,    0,    0,  952,  950,  951,  952,
      953,    0,    0,    0,  953,  951,  952,    0,    0,  951,
      953,    0,  954,  952,    0,  951,  954,  952,  951,    0,
      953,    0,  954,  952,  955,  951,  952,  953,  955,    0,
        0,  953,  954,  952,  955,    0,  956,  953,    0,  954,
      956,    0,    0,  954,  955,    0,  956,  953,    0,  954,
      957,  955,    0,    0,  957,  955,  956,    0,  957,  954,
        0,  955,  958,  956,  957,    0,  958,  956,  961,    0,

      958,  955,  961,  956,  957,    0,  958,  957,  961,  959,
      959,  957,    0,  956,  959,  957,  958,    0,  961,  958,
      959,  957,  962,  958,  960,  961,  962,  958,  960,  961,
      959,  957,  962,  958,  960,  961,  959,  959,    0,  960,
        0,  959,  962,  958,  960,  961,    0,  959,    0,  962,
      964,  960,    0,  962,  964,  960,    0,  959,  964,  962,
        0,  960,  963,  963,  964,    0,  960,  963,  965,  962,
      965,  960,    0,  963,  964,  965,    0,  964,    0,    0,
      965,  964,    0,  963,    0,  964,  966,    0,  966,  963,
      963,  964,  966,  966,  963,  965,    0,  965,  966,    0,

      963,  964,  965,    0,  967,    0,  967,  965,    0,    0,
      963,  967,  967,  966,    0,  966,  967,  968,    0,  966,
      966,    0,    0,  969,    0,  966,    0,    0,  968,    0,
      968,  967,    0,  967,  969,  968,  969,    0,  967,  967,
      968,  969,    0,  967,  968,    0,  969,  970,    0,  970,
      969,    0,    0,    0,  970,  968,    0,  968,  971,  970,
      971,  969,  968,  969,    0,  971,    0,  968,  969,    0,
      971,    0,    0,  969,  970,    0,  970,    0,  972,  973,
      973,  970,  972,    0,  973,  971,  970,  971,  972,    0,
      973,    0,  971,    0,    0,    0,    0,  971,  972,    0,

      973,    0,    0,    0,  974,  972,  973,  973,  974,  972,
        0,  973,  975,    0,  974,  972,  975,  973,    0,    0,
        0,  976,  975,  976,  974,  972,    0,  973,  976,  977,
        0,  974,  975,  976,    0,  974,    0,    0,    0,  975,
      977,  974,  977,  975,    0,    0,    0,  977,  976,  975,
      976,  974,  977,  978,    0,  976,  977,    0,    0,  975,
      976,  979,    0,  979,  978,    0,  978,  977,  979,  977,
      980,  978,  980,  979,  977,    0,  978,  980,    0,  977,
      978,    0,  980,    0,    0,    0,    0,    0,  979,    0,
      979,  978, 1054,  978,    0,  979, 1054,  980,  978,  980,

      979, 1055, 1054,  978,  980, 1055, 1056,    0, 1056,  980,
     1059, 1055, 1054, 1056, 1059, 1057,    0, 1057, 1056, 1054,
     1059, 1055, 1057, 1054, 1058,    0, 1058, 1057, 1055, 1054,
     1059, 1058, 1055, 1056,    0, 1056, 1058, 1059, 1055, 1054,
     1056, 1059, 1057,    0, 1057, 1056, 1060, 1059, 1055, 1057,
     1060, 1058,    0, 1058, 1057,    0, 1060, 1059, 1058,    0,
     1061, 1060, 1061, 1058,    0,    0, 1060, 1061, 1062, 1063,
     1062, 1063, 1061, 1060,    0, 1062, 1063, 1060,    0,    0,
     1062, 1063,    0, 1060,    0,    0,    0, 1061, 1060, 1061,
        0,    0,    0, 1060, 1061, 1062, 1063, 1062, 1063, 1061,

        0,    0, 1062, 1063, 1064,    0, 1064, 1062, 1063,    0,
     1065, 1064, 1065, 1066, 1066, 1066, 1064, 1065, 1065,    0,
     1066,    0, 1065,    0,    0, 1066,    0,    0,    0,    0,
        0, 1064,    0, 1064,    0,    0,    0, 1065, 1064, 1065,
     1066, 1066, 1066, 1064, 1065, 1065, 1067, 1066,    0, 1065,
        0, 1068, 1066, 1068,    0, 1067,    0, 1067, 1068,    0,
        0,    0, 1067, 1068, 1069,    0, 1069, 1067,    0,    0,
        0, 1069, 1069, 1067,    0,    0, 1069,    0, 1068,    0,
     1068,    0, 1067,    0, 1067, 1068,    0,    0,    0, 1067,
     1068, 1069,    0, 1069, 1067, 1070, 1071, 1070, 1069, 1069,

        0,    0, 1070, 1069,    0,    0, 1071, 1070, 1071, 1072,
        0, 1072,    0, 1071,    0,    0, 1072,    0, 1071,    0,
        0, 1072, 1070, 1071, 1070,    0,    0,    0, 1073, 1070,
        0,    0,    0, 1071, 1070, 1071, 1072, 1073, 1072, 1073,
     1071,    0,    0, 1072, 1073, 1071,    0,    0, 1072, 1073,
     1074, 1075, 1074, 1075,    0, 1073,    0, 1074, 1075,    0,
        0,    0, 1074, 1075, 1073,    0, 1073,    0,    0,    0,
        0, 1073,    0,    0,    0,    0, 1073, 1074, 1075, 1074,
     1075, 1076, 1077, 1076, 1074, 1075, 1077, 1078, 1076, 1074,
     1075, 1078, 1077, 1076, 1079,    0, 1079, 1078,    0,    0,

        0, 1079, 1077,    0,    0,    0, 1079, 1078, 1076, 1077,
     1076,    0,    0, 1077, 1078, 1076,    0,    0, 1078, 1077,
     1076, 1079,    0, 1079, 1078,    0,    0,    0, 1079, 1077,
     1080, 1081, 1080, 1079, 1078, 1081, 1082, 1080, 1080,    0,
     1082, 1081, 1080,    0,    0,    0, 1082,    0,    0,    0,
     1083, 1081,    0,    0, 1083,    0, 1082, 1080, 1081, 1080,
     1083,    0, 1081, 1082, 1080, 1080,    0, 1082, 1081, 1080,
     1083,    0, 1084, 1082,    0, 1085, 1084, 1083, 1081, 1085,
     1086, 1083, 1084, 1082, 1086, 1085,    0, 1083,    0,    0,
     1086,    0, 1084,    0, 1087, 1085,    0, 1083, 1087, 1084,

     1086,    0, 1085, 1084, 1087,    0, 1085, 1086,    0, 1084,
        0, 1086, 1085,    0, 1087,    0, 1088, 1086,    0, 1084,
     1088, 1087, 1085,    0, 1089, 1087, 1088, 1086, 1089, 1088,
        0, 1087,    0,    0, 1089,    0, 1088,    0, 1090,    0,
        0, 1087, 1090, 1088, 1089,    0, 1090, 1088,    0,    0,
     1091, 1089, 1090, 1088, 1091, 1089, 1088,    0,    0,    0,
     1091, 1089, 1090, 1088, 1092, 1090,    0,    0, 1092, 1090,
     1091, 1089,    0, 1090, 1092,    0, 1093, 1091, 1093, 1090,
        0, 1091,    0, 1093, 1092,    0,    0, 1091, 1093, 1090,
        0, 1092, 1094,    0, 1094, 1092,    0, 1091,    0, 1094,

     1095, 1092, 1095, 1093, 1094, 1093, 1096, 1095, 1096,    0,
     1093, 1092, 1095, 1096,    0, 1093,    0,    0, 1096, 1094,
        0, 1094,    0,    0,    0, 1097, 1094, 1095,    0, 1095,
     1098, 1094, 1098, 1096, 1095, 1096, 1097, 1098, 1097, 1095,
     1096,    0, 1098, 1097,    0, 1096,    0,    0, 1097,    0,
        0,    0, 1097,    0, 1099,    0, 1099, 1098,    0, 1098,
        0, 1099, 1099, 1097, 1098, 1097, 1099, 1100,    0, 1098,
     1097, 1100, 1101,    0,    0, 1097, 1101, 1100,    0,    0,
     1102, 1099, 1101, 1099, 1102,    0,    0, 1100, 1099, 1099,
     1102,    0, 1101, 1099, 1100,    0,    0,    0, 1100, 1101,

     1102,    0, 1103, 1101, 1100,    0, 1103, 1102,    0, 1101,
        0, 1102, 1103,    0, 1100,    0, 1104, 1102,    0, 1101,
     1104,    0, 1103,    0,    0,    0, 1104, 1102, 1105, 1103,
     1105,    0,    0, 1103, 1105,    0, 1104,    0,    0, 1103,
     1105,    0, 1106, 1104, 1106,    0,    0, 1104, 1106, 1103,
     1105,    0,    0, 1104, 1106, 1105,    0, 1105, 1107,    0,
     1107, 1105,    0, 1104, 1106, 1107, 1108, 1105, 1108, 1106,
     1107, 1106, 1109, 1108, 1109, 1106,    0, 1105, 1108, 1109,
        0, 1106,    0,    0, 1109, 1107,    0, 1107,    0,    0,
        0, 1106, 1107, 1108,    0, 1108, 1110, 1107, 1110, 1109,

     1108, 1109, 1111, 1110, 1111, 1108, 1109,    0, 1110, 1111,
     1112, 1109, 1112,    0, 1111,    0,    0, 1112,    0,    0,
        0,    0, 1112, 1110,    0, 1110, 1113,    0, 1113, 1111,
     1110, 1111,    0, 1113, 1114, 1110, 1111, 1112, 1113, 1112,
        0, 1111,    0,    0, 1112, 1114, 1173, 1114,    0, 1112,
     1173,    0, 1114, 1113,    0, 1113, 1173, 1114, 1115, 1116,
     1113, 1114,    0,    0,    0, 1113, 1173,    0, 1116, 1115,
     1116, 1115, 1114, 1173, 1114, 1116, 1115, 1173,    0, 1114,
     1116, 1115,    0, 1173, 1114, 1115, 1116,    0,    0,    0,
        0,    0, 1117, 1173, 1117, 1116, 1115, 1116, 1115, 1117,

     1117,    0, 1116, 1115, 1117, 1118,    0, 1116, 1115,    0,
     1119,    0, 1119, 1118,    0, 1118, 1120, 1119, 1120, 1117,
     1118, 1117, 1119, 1120,    0, 1118, 1117, 1117, 1120,    0,
        0, 1117, 1118,    0, 1121,    0, 1121, 1119,    0, 1119,
     1118, 1121, 1118, 1120, 1119, 1120, 1121, 1118,    0, 1119,
     1120,    0, 1118,    0,    0, 1120,    0, 1122,    0, 1122,
     1123, 1121, 1123, 1121, 1122,    0, 1123, 1123, 1121, 1122,
        0,    0, 1123, 1121,    0,    0, 1124,    0, 1124,    0,
        0,    0,    0, 1124, 1122,    0, 1122, 1123, 1124, 1123,
     1169, 1122, 1169, 1123, 1123,    0, 1122, 1169,    0, 1123,

        0,    0, 1169, 1124, 1170, 1124, 1170,    0,    0,    0,
     1124, 1170,    0,    0,    0, 1124, 1170, 1169, 1171, 1169,
     1171,    0,    0,    0, 1169, 1171,    0,    0,    0, 1169,
     1171, 1170, 1172, 1170, 1172,    0, 1174,    0, 1170, 1172,
     1174,    0,    0, 1170, 1172, 1171, 1174, 1171,    0,    0,
     1175,    0, 1171,    0, 1175,    0, 1174, 1171,    0, 1172,
     1175, 1172,    0, 1174, 1176, 1176, 1172, 1174,    0,    0,
     1175, 1172,    0, 1174, 1177,    0,    0, 1175, 1177,    0,
        0, 1175,    0, 1174, 1177,    0,    0, 1175, 1178, 1176,
        0,    0, 1178, 1176, 1177,    0, 1178, 1175,    0, 1176,

        0, 1177, 1178,    0, 1179, 1177,    0,    0, 1179, 1176,
        0, 1177, 1178,    0, 1179, 1178, 1176,    0, 1180, 1178,
     1176, 1177, 1180, 1178, 1179,    0, 1176,    0, 1180, 1178,
     1181, 1179,    0, 1182, 1181, 1179, 1176, 1182, 1180, 1178,
     1181, 1179, 1183, 1182,    0, 1180, 1183,    0, 1182, 1180,
     1181, 1179, 1183, 1182,    0, 1180,    0, 1181,    0,    0,
     1182, 1181, 1183,    0, 1182, 1180,    0, 1181, 1184, 1183,
     1182,    0, 1184, 1183,    0, 1182,    0, 1181, 1184, 1183,
     1182, 1185,    0,    0,    0, 1185,    0,    0, 1184, 1183,
     1186, 1185, 1186,    0,    0, 1184,    0, 1186,    0, 1184,

        0, 1185, 1186,    0, 1187, 1184, 1187,    0, 1185,    0,
        0, 1187, 1185,    0, 1188, 1184, 1187, 1186, 1185, 1186,
     1189,    0, 1189, 1188, 1186, 1188,    0, 1189, 1185, 1186,
     1188, 1187, 1189, 1187,    0, 1188,    0,    0, 1187,    0,
        0, 1188,    0, 1187, 1190,    0, 1190, 1189,    0, 1189,
     1188, 1190, 1188, 1191, 1189, 1191, 1190, 1188,    0, 1189,
     1191,    0, 1188,    0,    0, 1191,    0,    0,    0,    0,
        0, 1190,    0, 1190,    0,    0,    0,    0, 1190, 1192,
     1191, 1192, 1191, 1190, 1193, 1192, 1192, 1191, 1193, 1194,
        0, 1192, 1191, 1194, 1193, 1195,    0, 1195, 1196, 1194,

     1196,    0, 1195,    0, 1193, 1196, 1192, 1195, 1192, 1194,
     1196, 1193, 1192, 1192,    0, 1193, 1194,    0, 1192,    0,
     1194, 1193, 1195,    0, 1195, 1196, 1194, 1196, 1197, 1195,
     1197, 1193, 1196,    0, 1195, 1197, 1194, 1196,    0, 1198,
     1197, 1198,    0,    0,    0,    0, 1198, 1198,    0,    0,
        0, 1198, 1200,    0, 1200, 1197,    0, 1197,    0, 1200,
        0,    0, 1197,    0, 1200,    0, 1198, 1197, 1198, 1199,
     1199,    0, 1199, 1198, 1198,    0,    0, 1199, 1198, 1200,
        0, 1200, 1199, 1201,    0, 1201, 1200,    0,    0,    0,
     1201, 1200,    0,    0,    0, 1201, 1199, 1199,    0, 1199,

        0,    0,    0,    0, 1199,    0, 1202,    0, 1202, 1199,
     1201,    0, 1201, 1202, 1202,    0,    0, 1201, 1202, 1203,
        0, 1203, 1201,    0,    0,    0, 1203, 1205, 1205, 1205,
     1204, 1203, 1204, 1202, 1205, 1202, 1204, 1204,    0, 1205,
     1202, 1202, 1204,    0,    0, 1202, 1203,    0, 1203,    0,
        0,    0,    0, 1203, 1205, 1205, 1205, 1204, 1203, 1204,
     1206, 1205, 1206, 1204, 1204,    0, 1205, 1206, 1206, 1204,
     1207, 1208, 1206, 1209, 1207, 1208,    0, 1209,    0,    0,
     1207, 1208,    0, 1209,    0,    0,    0, 1206,    0, 1206,
     1207, 1208,    0, 1209, 1206, 1206,    0, 1207, 1208, 1206,

     1209, 1207, 1208,    0, 1209, 1210,    0, 1207, 1208, 1210,
     1209,    0,    0,    0,    0, 1210,    0, 1207, 1208, 1236,
     1209,    0,    0, 1236, 1234, 1210,    0,    0, 1234, 1236,
        0,    0, 1210,    0, 1234,    0, 1210, 1234,    0, 1236,
        0,    0, 1210, 1237, 1234,    0, 1236, 1237,    0,    0,
     1236, 1234, 1210, 1237,    0, 1234, 1236,    0, 1237,    0,
        0, 1234,    0, 1237, 1234,    0, 1236,    0, 1238, 1239,
     1237, 1234, 1238, 1239, 1237,    0,    0,    0, 1238, 1239,
     1237,    0, 1240, 1241, 1240, 1237, 1240, 1241, 1238, 1239,
     1237,    0, 1240, 1241,    0, 1238, 1239, 1242, 1241, 1238,

     1239, 1242, 1240, 1241,    0, 1238, 1239, 1242,    0, 1240,
     1241, 1240,    0, 1240, 1241, 1238, 1239, 1242,    0, 1240,
     1241,    0, 1243, 1244, 1242, 1241, 1243, 1244, 1242, 1240,
     1241,    0, 1243, 1244, 1242,    0, 1244,    0,    0, 1245,
     1246, 1245, 1243, 1244, 1242, 1246, 1245, 1246,    0, 1243,
     1244, 1245, 1246, 1243, 1244,    0,    0, 1246,    0, 1243,
     1244,    0,    0, 1244,    0,    0, 1245, 1246, 1245, 1243,
     1244,    0, 1246, 1245, 1246, 1247,    0, 1247, 1245, 1246,
        0, 1248, 1247, 1248, 1246,    0,    0, 1247, 1248, 1249,
        0, 1249,    0, 1248,    0,    0, 1249,    0,    0,    0,

        0, 1249, 1247,    0, 1247,    0,    0, 1250, 1248, 1247,
     1248, 1250,    0,    0, 1247, 1248, 1249, 1250, 1249,    0,
     1248, 1251,    0, 1249,    0, 1251, 1252, 1250, 1249, 1253,
     1252, 1251, 1251, 1253, 1250,    0, 1252,    0, 1250, 1253,
        0, 1251,    0,    0, 1250,    0, 1252,    0, 1251, 1253,
     1254, 1254, 1251, 1252, 1250, 1254, 1253, 1252, 1251, 1251,
     1253, 1254,    0, 1252, 1269, 1269, 1253,    0, 1251,    0,
     1271, 1254,    0, 1252, 1271, 1272, 1253, 1254, 1254, 1272,
     1271,    0, 1254,    0, 1274, 1272, 1274,    0, 1254, 1269,
     1271, 1274,    0, 1269,    0, 1272, 1274, 1271, 1254, 1269,

        0, 1271, 1272, 1275, 1273, 1275, 1272, 1271, 1273, 1269,
     1275, 1274, 1272, 1274, 1273, 1275, 1269, 1271, 1274,    0,
     1269,    0, 1272, 1274, 1273,    0, 1269, 1276,    0, 1276,
     1275, 1273, 1275,    0, 1276, 1273, 1269, 1275, 1277, 1276,
     1277, 1273, 1275,    0,    0, 1277, 1278,    0, 1278,    0,
     1277, 1273,    0, 1278, 1276,    0, 1276,    0, 1278, 1279,
        0, 1276,    0, 1279, 1280, 1277, 1276, 1277, 1280, 1279,
        0,    0, 1277, 1278, 1280, 1278,    0, 1277, 1290, 1279,
     1278,    0, 1290, 1291, 1280, 1278, 1279, 1291, 1290,    0,
     1279, 1280, 1292, 1291, 1292, 1280, 1279,    0, 1290, 1292,

     1300, 1280, 1300, 1291, 1292, 1290, 1279, 1300,    0, 1290,
     1291, 1280, 1300,    0, 1291, 1290,    0,    0,    0, 1292,
     1291, 1292,    0,    0,    0, 1290, 1292, 1300,    0, 1300,
     1291, 1292,    0,    0, 1300,    0,    0,    0,    0, 1300,
     1332, 1332, 1332, 1332, 1332, 1333,    0,    0, 1333, 1333,
     1335, 1335, 1335,    0, 1335, 1336,    0, 1336, 1336, 1336,
     1337,    0, 1337, 1337, 1337, 1338,    0, 1338, 1338, 1338,
     1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,
     1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,
     1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,

     1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,
     1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,
     1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,
     1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331, 1331,
     1331, 1331, 1331, 1331, 1331, 1331, 1331
    } ;

/* The intent behind this definition is that it'll catch
 * any uses of REJECT which flex missed.
 */
#define REJECT reject_used_but_not_detected
#define yymore() yymore_used_but_not_detected
#define YY_MORE_ADJ 0
#define YY_RESTORE_YY_MORE_OFFSET
#line 1 "flex_lexer.l"
/**
 * lexer
 *
 *
 */
/***************************
 ** Section 1: Definitions
 ***************************/

#line 12 "flex_lexer.l"

#include "../sql/Expr.h"
#include "bison_parser.h"
#include <climits>
#include <stdio.h>
#include <sstream>

#define TOKEN(name) { return SQL_##name; }

static thread_local std::stringstream strbuf;

#line 3064 "flex_lexer.cpp"

/***************************
 ** Section 2: Rules
 ***************************/
/* Define the output files */
/* Make reentrant */
/* performance tweeks */
/* other flags */
/* %option nodefault */

/***************************
 ** Section 3: Rules
 ***************************/
#line 3078 "flex_lexer.cpp"

#define INITIAL 0
#define singlequotedstring 1
#define COMMENT 2

#ifndef YY_NO_UNISTD_H
/* Special case for "unistd.h", since it is non-ANSI. We include it way
 * down here because we want the user's section 1 to have been scanned first.
 * The user has a chance to override it with an option.
 */
#if defined(_WIN32) || defined(_WIN64)
    #include <io.h>
#else
    #include <unistd.h>
#endif
#endif

#ifndef YY_EXTRA_TYPE
#define YY_EXTRA_TYPE void *
#endif

/* Holds the entire state of the reentrant scanner. */
struct yyguts_t
    {

    /* User-defined. Not touched by flex. */
    YY_EXTRA_TYPE yyextra_r;

    /* The rest are the same as the globals declared in the non-reentrant scanner. */
    FILE *yyin_r, *yyout_r;
    size_t yy_buffer_stack_top; /**< index of top of stack. */
    size_t yy_buffer_stack_max; /**< capacity of stack. */
    YY_BUFFER_STATE * yy_buffer_stack; /**< Stack as an array. */
    char yy_hold_char;
    int yy_n_chars;
    int yyleng_r;
    char *yy_c_buf_p;
    int yy_init;
    int yy_start;
    int yy_did_buffer_switch_on_eof;
    int yy_start_stack_ptr;
    int yy_start_stack_depth;
    int *yy_start_stack;
    yy_state_type yy_last_accepting_state;
    char* yy_last_accepting_cpos;

    int yylineno_r;
    int yy_flex_debug_r;

    char *yytext_r;
    int yy_more_flag;
    int yy_more_len;

    YYSTYPE * yylval_r;

    YYLTYPE * yylloc_r;

    }; /* end struct yyguts_t */

static int yy_init_globals ( yyscan_t yyscanner );

    /* This must go here because YYSTYPE and YYLTYPE are included
     * from bison output in section 1.*/
    #    define yylval yyg->yylval_r
    
    #    define yylloc yyg->yylloc_r
    
int yylex_init (yyscan_t* scanner);

int yylex_init_extra ( YY_EXTRA_TYPE user_defined, yyscan_t* scanner);

/* Accessor methods to globals.
   These are made visible to non-reentrant scanners for convenience. */

int yylex_destroy ( yyscan_t yyscanner );

int yyget_debug ( yyscan_t yyscanner );

void yyset_debug ( int debug_flag , yyscan_t yyscanner );

YY_EXTRA_TYPE yyget_extra ( yyscan_t yyscanner );

void yyset_extra ( YY_EXTRA_TYPE user_defined , yyscan_t yyscanner );

FILE *yyget_in ( yyscan_t yyscanner );

void yyset_in  ( FILE * _in_str , yyscan_t yyscanner );

FILE *yyget_out ( yyscan_t yyscanner );

void yyset_out  ( FILE * _out_str , yyscan_t yyscanner );

			int yyget_leng ( yyscan_t yyscanner );

char *yyget_text ( yyscan_t yyscanner );

int yyget_lineno ( yyscan_t yyscanner );

void yyset_lineno ( int _line_number , yyscan_t yyscanner );

int yyget_column  ( yyscan_t yyscanner );

void yyset_column ( int _column_no , yyscan_t yyscanner );

YYSTYPE * yyget_lval ( yyscan_t yyscanner );

void yyset_lval ( YYSTYPE * yylval_param , yyscan_t yyscanner );

       YYLTYPE *yyget_lloc ( yyscan_t yyscanner );
    
        void yyset_lloc ( YYLTYPE * yylloc_param , yyscan_t yyscanner );
    
/* Macros after this point can all be overridden by user definitions in
 * section 1.
 */

#ifndef YY_SKIP_YYWRAP
#ifdef __cplusplus
extern "C" int yywrap ( yyscan_t yyscanner );
#else
extern int yywrap ( yyscan_t yyscanner );
#endif
#endif

#ifndef YY_NO_UNPUT
    
#endif

#ifndef yytext_ptr
static void yy_flex_strncpy ( char *, const char *, int , yyscan_t yyscanner);
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen ( const char * , yyscan_t yyscanner);
#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
static int yyinput ( yyscan_t yyscanner );
#else
static int input ( yyscan_t yyscanner );
#endif

#endif

/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Copy whatever the last rule matched to the standard output. */
#ifndef ECHO
/* This used to be an fputs(), but since the string might contain NUL's,
 * we now use fwrite().
 */
#define ECHO do { if (fwrite( yytext, (size_t) yyleng, 1, yyout )) {} } while (0)
#endif

/* Gets input and stuffs it into "buf".  number of characters read, or YY_NULL,
 * is returned in "result".
 */
#ifndef YY_INPUT
#define YY_INPUT(buf,result,max_size) \
	if ( YY_CURRENT_BUFFER_LVALUE->yy_is_interactive ) \
		{ \
		int c = '*'; \
		int n; \
		for ( n = 0; n < max_size && \
			     (c = getc( yyin )) != EOF && c != '\n'; ++n ) \
			buf[n] = (char) c; \
		if ( c == '\n' ) \
			buf[n++] = (char) c; \
		if ( c == EOF && ferror( yyin ) ) \
			YY_FATAL_ERROR( "input in flex scanner failed" ); \
		result = n; \
		} \
	else \
		{ \
		errno=0; \
		while ( (result = (int) fread(buf, 1, (yy_size_t) max_size, yyin)) == 0 && ferror(yyin)) \
			{ \
			if( errno != EINTR) \
				{ \
				YY_FATAL_ERROR( "input in flex scanner failed" ); \
				break; \
				} \
			errno=0; \
			clearerr(yyin); \
			} \
		}\
\

#endif

/* No semi-colon after return; correct usage is to write "yyterminate();" -
 * we don't want an extra ';' after the "return" because that will cause
 * some compilers to complain about unreachable statements.
 */
#ifndef yyterminate
#define yyterminate() return YY_NULL
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Report a fatal error. */
#ifndef YY_FATAL_ERROR
#define YY_FATAL_ERROR(msg) yy_fatal_error( msg , yyscanner)
#endif

/* end tables serialization structures and prototypes */

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1

extern int yylex \
               (YYSTYPE * yylval_param, YYLTYPE * yylloc_param , yyscan_t yyscanner);

#define YY_DECL int yylex \
               (YYSTYPE * yylval_param, YYLTYPE * yylloc_param , yyscan_t yyscanner)
#endif /* !YY_DECL */

/* Code executed at the beginning of each rule, after yytext and yyleng
 * have been set up.
 */
#ifndef YY_USER_ACTION
#define YY_USER_ACTION
#endif

/* Code executed at the end of each rule. */
#ifndef YY_BREAK
#define YY_BREAK /*LINTED*/break;
#endif

#define YY_RULE_SETUP \
	YY_USER_ACTION

/** The main scanner function which does all the work.
 */
YY_DECL
{
	yy_state_type yy_current_state;
	char *yy_cp, *yy_bp;
	int yy_act;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

    yylval = yylval_param;

    yylloc = yylloc_param;

	if ( !yyg->yy_init )
		{
		yyg->yy_init = 1;

#ifdef YY_USER_INIT
		YY_USER_INIT;
#endif

		if ( ! yyg->yy_start )
			yyg->yy_start = 1;	/* first start state */

		if ( ! yyin )
			yyin = stdin;

		if ( ! yyout )
			yyout = stdout;

		if ( ! YY_CURRENT_BUFFER ) {
			yyensure_buffer_stack (yyscanner);
			YY_CURRENT_BUFFER_LVALUE =
				yy_create_buffer( yyin, YY_BUF_SIZE , yyscanner);
		}

		yy_load_buffer_state( yyscanner );
		}

	{
#line 57 "flex_lexer.l"


#line 3365 "flex_lexer.cpp"

	while ( /*CONSTCOND*/1 )		/* loops until end-of-file is reached */
		{
		yy_cp = yyg->yy_c_buf_p;

		/* Support of yytext. */
		*yy_cp = yyg->yy_hold_char;

		/* yy_bp points to the position in yy_ch_buf of the start of
		 * the current run.
		 */
		yy_bp = yy_cp;

		yy_current_state = yyg->yy_start;
yy_match:
		do
			{
			YY_CHAR yy_c = yy_ec[YY_SC_TO_UI(*yy_cp)] ;
			if ( yy_accept[yy_current_state] )
				{
				yyg->yy_last_accepting_state = yy_current_state;
				yyg->yy_last_accepting_cpos = yy_cp;
				}
			while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
				{
				yy_current_state = (int) yy_def[yy_current_state];
				if ( yy_current_state >= 1332 )
					yy_c = yy_meta[yy_c];
				}
			yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
			++yy_cp;
			}
		while ( yy_current_state != 1331 );
		yy_cp = yyg->yy_last_accepting_cpos;
		yy_current_state = yyg->yy_last_accepting_state;

yy_find_action:
		yy_act = yy_accept[yy_current_state];

		YY_DO_BEFORE_ACTION;

do_action:	/* This label is used only to access EOF actions. */

		switch ( yy_act )
	{ /* beginning of action switch */
			case 0: /* must back up */
			/* undo the effects of YY_DO_BEFORE_ACTION */
			*yy_cp = yyg->yy_hold_char;
			yy_cp = yyg->yy_last_accepting_cpos;
			yy_current_state = yyg->yy_last_accepting_state;
			goto yy_find_action;

case 1:
YY_RULE_SETUP
#line 59 "flex_lexer.l"
BEGIN(COMMENT);
	YY_BREAK
case 2:
YY_RULE_SETUP
#line 60 "flex_lexer.l"
/* skipping comment content until a end of line is read */;
	YY_BREAK
case 3:
/* rule 3 can match eol */
YY_RULE_SETUP
#line 61 "flex_lexer.l"
BEGIN(INITIAL);
	YY_BREAK
case 4:
/* rule 4 can match eol */
YY_RULE_SETUP
#line 63 "flex_lexer.l"
/* skip whitespace */;
	YY_BREAK
case 5:
YY_RULE_SETUP
#line 65 "flex_lexer.l"
TOKEN(ADD)
	YY_BREAK
case 6:
YY_RULE_SETUP
#line 66 "flex_lexer.l"
TOKEN(AFTER)
	YY_BREAK
case 7:
YY_RULE_SETUP
#line 67 "flex_lexer.l"
TOKEN(ALL)
	YY_BREAK
case 8:
YY_RULE_SETUP
#line 68 "flex_lexer.l"
TOKEN(ALTER)
	YY_BREAK
case 9:
YY_RULE_SETUP
#line 69 "flex_lexer.l"
TOKEN(ANALYZE)
	YY_BREAK
case 10:
YY_RULE_SETUP
#line 70 "flex_lexer.l"
TOKEN(AND)
	YY_BREAK
case 11:
YY_RULE_SETUP
#line 71 "flex_lexer.l"
TOKEN(ARRAY)
	YY_BREAK
case 12:
YY_RULE_SETUP
#line 72 "flex_lexer.l"
TOKEN(AS)
	YY_BREAK
case 13:
YY_RULE_SETUP
#line 73 "flex_lexer.l"
TOKEN(ASC)
	YY_BREAK
case 14:
YY_RULE_SETUP
#line 74 "flex_lexer.l"
TOKEN(BEFORE)
	YY_BREAK
case 15:
YY_RULE_SETUP
#line 75 "flex_lexer.l"
TOKEN(BEGIN)
	YY_BREAK
case 16:
YY_RULE_SETUP
#line 76 "flex_lexer.l"
TOKEN(BETWEEN)
	YY_BREAK
case 17:
YY_RULE_SETUP
#line 77 "flex_lexer.l"
TOKEN(BIGINT)
	YY_BREAK
case 18:
YY_RULE_SETUP
#line 78 "flex_lexer.l"
TOKEN(BOOLEAN)
	YY_BREAK
case 19:
YY_RULE_SETUP
#line 79 "flex_lexer.l"
TOKEN(BY)
	YY_BREAK
case 20:
YY_RULE_SETUP
#line 80 "flex_lexer.l"
TOKEN(CALL)
	YY_BREAK
case 21:
YY_RULE_SETUP
#line 81 "flex_lexer.l"
TOKEN(CASCADE)
	YY_BREAK
case 22:
YY_RULE_SETUP
#line 82 "flex_lexer.l"
TOKEN(CASE)
	YY_BREAK
case 23:
YY_RULE_SETUP
#line 83 "flex_lexer.l"
TOKEN(CAST)
	YY_BREAK
case 24:
YY_RULE_SETUP
#line 84 "flex_lexer.l"
TOKEN(CHAR)
	YY_BREAK
case 25:
YY_RULE_SETUP
#line 85 "flex_lexer.l"
TOKEN(COLUMN)
	YY_BREAK
case 26:
YY_RULE_SETUP
#line 86 "flex_lexer.l"
TOKEN(COLUMNS)
	YY_BREAK
case 27:
YY_RULE_SETUP
#line 87 "flex_lexer.l"
TOKEN(COMMIT)
	YY_BREAK
case 28:
YY_RULE_SETUP
#line 88 "flex_lexer.l"
TOKEN(CONTROL)
	YY_BREAK
case 29:
YY_RULE_SETUP
#line 89 "flex_lexer.l"
TOKEN(COPY)
	YY_BREAK
case 30:
YY_RULE_SETUP
#line 90 "flex_lexer.l"
TOKEN(CREATE)
	YY_BREAK
case 31:
YY_RULE_SETUP
#line 91 "flex_lexer.l"
TOKEN(CROSS)
	YY_BREAK
case 32:
YY_RULE_SETUP
#line 92 "flex_lexer.l"
TOKEN(DATE)
	YY_BREAK
case 33:
YY_RULE_SETUP
#line 93 "flex_lexer.l"
TOKEN(DATETIME)
	YY_BREAK
case 34:
YY_RULE_SETUP
#line 94 "flex_lexer.l"
TOKEN(DAY)
	YY_BREAK
case 35:
YY_RULE_SETUP
#line 95 "flex_lexer.l"
TOKEN(DAYS)
	YY_BREAK
case 36:
YY_RULE_SETUP
#line 96 "flex_lexer.l"
TOKEN(DEALLOCATE)
	YY_BREAK
case 37:
YY_RULE_SETUP
#line 97 "flex_lexer.l"
TOKEN(DECIMAL)
	YY_BREAK
case 38:
YY_RULE_SETUP
#line 98 "flex_lexer.l"
TOKEN(DEFAULT)
	YY_BREAK
case 39:
YY_RULE_SETUP
#line 99 "flex_lexer.l"
TOKEN(DELETE)
	YY_BREAK
case 40:
YY_RULE_SETUP
#line 100 "flex_lexer.l"
TOKEN(DELTA)
	YY_BREAK
case 41:
YY_RULE_SETUP
#line 101 "flex_lexer.l"
TOKEN(DESC)
	YY_BREAK
case 42:
YY_RULE_SETUP
#line 102 "flex_lexer.l"
TOKEN(DESCRIBE)
	YY_BREAK
case 43:
YY_RULE_SETUP
#line 103 "flex_lexer.l"
TOKEN(DIRECT)
	YY_BREAK
case 44:
YY_RULE_SETUP
#line 104 "flex_lexer.l"
TOKEN(DISTINCT)
	YY_BREAK
case 45:
YY_RULE_SETUP
#line 105 "flex_lexer.l"
TOKEN(DOUBLE)
	YY_BREAK
case 46:
YY_RULE_SETUP
#line 106 "flex_lexer.l"
TOKEN(DROP)
	YY_BREAK
case 47:
YY_RULE_SETUP
#line 107 "flex_lexer.l"
TOKEN(ELSE)
	YY_BREAK
case 48:
YY_RULE_SETUP
#line 108 "flex_lexer.l"
TOKEN(END)
	YY_BREAK
case 49:
YY_RULE_SETUP
#line 109 "flex_lexer.l"
TOKEN(ESCAPE)
	YY_BREAK
case 50:
YY_RULE_SETUP
#line 110 "flex_lexer.l"
TOKEN(EXCEPT)
	YY_BREAK
case 51:
YY_RULE_SETUP
#line 111 "flex_lexer.l"
TOKEN(EXECUTE)
	YY_BREAK
case 52:
YY_RULE_SETUP
#line 112 "flex_lexer.l"
TOKEN(EXISTS)
	YY_BREAK
case 53:
YY_RULE_SETUP
#line 113 "flex_lexer.l"
TOKEN(EXPLAIN)
	YY_BREAK
case 54:
YY_RULE_SETUP
#line 114 "flex_lexer.l"
TOKEN(EXTRACT)
	YY_BREAK
case 55:
YY_RULE_SETUP
#line 115 "flex_lexer.l"
TOKEN(FALSE)
	YY_BREAK
case 56:
YY_RULE_SETUP
#line 116 "flex_lexer.l"
TOKEN(FILE)
	YY_BREAK
case 57:
YY_RULE_SETUP
#line 117 "flex_lexer.l"
TOKEN(FLOAT)
	YY_BREAK
case 58:
YY_RULE_SETUP
#line 118 "flex_lexer.l"
TOKEN(FOLLOWING)
	YY_BREAK
case 59:
YY_RULE_SETUP
#line 119 "flex_lexer.l"
TOKEN(FOR)
	YY_BREAK
case 60:
YY_RULE_SETUP
#line 120 "flex_lexer.l"
TOKEN(FORMAT)
	YY_BREAK
case 61:
YY_RULE_SETUP
#line 121 "flex_lexer.l"
TOKEN(FROM)
	YY_BREAK
case 62:
YY_RULE_SETUP
#line 122 "flex_lexer.l"
TOKEN(FULL)
	YY_BREAK
case 63:
YY_RULE_SETUP
#line 123 "flex_lexer.l"
TOKEN(GLOBAL)
	YY_BREAK
case 64:
YY_RULE_SETUP
#line 124 "flex_lexer.l"
TOKEN(GROUP)
	YY_BREAK
case 65:
YY_RULE_SETUP
#line 125 "flex_lexer.l"
TOKEN(GROUPS)
	YY_BREAK
case 66:
YY_RULE_SETUP
#line 126 "flex_lexer.l"
TOKEN(HASH)
	YY_BREAK
case 67:
YY_RULE_SETUP
#line 127 "flex_lexer.l"
TOKEN(HAVING)
	YY_BREAK
case 68:
YY_RULE_SETUP
#line 128 "flex_lexer.l"
TOKEN(HINT)
	YY_BREAK
case 69:
YY_RULE_SETUP
#line 129 "flex_lexer.l"
TOKEN(HOUR)
	YY_BREAK
case 70:
YY_RULE_SETUP
#line 130 "flex_lexer.l"
TOKEN(HOURS)
	YY_BREAK
case 71:
YY_RULE_SETUP
#line 131 "flex_lexer.l"
TOKEN(IF)
	YY_BREAK
case 72:
YY_RULE_SETUP
#line 132 "flex_lexer.l"
TOKEN(ILIKE)
	YY_BREAK
case 73:
YY_RULE_SETUP
#line 133 "flex_lexer.l"
TOKEN(IMPORT)
	YY_BREAK
case 74:
YY_RULE_SETUP
#line 134 "flex_lexer.l"
TOKEN(IN)
	YY_BREAK
case 75:
YY_RULE_SETUP
#line 135 "flex_lexer.l"
TOKEN(INDEX)
	YY_BREAK
case 76:
YY_RULE_SETUP
#line 136 "flex_lexer.l"
TOKEN(INNER)
	YY_BREAK
case 77:
YY_RULE_SETUP
#line 137 "flex_lexer.l"
TOKEN(INSERT)
	YY_BREAK
case 78:
YY_RULE_SETUP
#line 138 "flex_lexer.l"
TOKEN(INT)
	YY_BREAK
case 79:
YY_RULE_SETUP
#line 139 "flex_lexer.l"
TOKEN(INTEGER)
	YY_BREAK
case 80:
YY_RULE_SETUP
#line 140 "flex_lexer.l"
TOKEN(INTERSECT)
	YY_BREAK
case 81:
YY_RULE_SETUP
#line 141 "flex_lexer.l"
TOKEN(INTERVAL)
	YY_BREAK
case 82:
YY_RULE_SETUP
#line 142 "flex_lexer.l"
TOKEN(INTO)
	YY_BREAK
case 83:
YY_RULE_SETUP
#line 143 "flex_lexer.l"
TOKEN(IS)
	YY_BREAK
case 84:
YY_RULE_SETUP
#line 144 "flex_lexer.l"
TOKEN(ISNULL)
	YY_BREAK
case 85:
YY_RULE_SETUP
#line 145 "flex_lexer.l"
TOKEN(JOIN)
	YY_BREAK
case 86:
YY_RULE_SETUP
#line 146 "flex_lexer.l"
TOKEN(KEY)
	YY_BREAK
case 87:
YY_RULE_SETUP
#line 147 "flex_lexer.l"
TOKEN(LEFT)
	YY_BREAK
case 88:
YY_RULE_SETUP
#line 148 "flex_lexer.l"
TOKEN(LIKE)
	YY_BREAK
case 89:
YY_RULE_SETUP
#line 149 "flex_lexer.l"
TOKEN(LIMIT)
	YY_BREAK
case 90:
YY_RULE_SETUP
#line 150 "flex_lexer.l"
TOKEN(LOAD)
	YY_BREAK
case 91:
YY_RULE_SETUP
#line 151 "flex_lexer.l"
TOKEN(LOCAL)
	YY_BREAK
case 92:
YY_RULE_SETUP
#line 152 "flex_lexer.l"
TOKEN(LOCKED)
	YY_BREAK
case 93:
YY_RULE_SETUP
#line 153 "flex_lexer.l"
TOKEN(LONG)
	YY_BREAK
case 94:
YY_RULE_SETUP
#line 154 "flex_lexer.l"
TOKEN(MERGE)
	YY_BREAK
case 95:
YY_RULE_SETUP
#line 155 "flex_lexer.l"
TOKEN(MINUS)
	YY_BREAK
case 96:
YY_RULE_SETUP
#line 156 "flex_lexer.l"
TOKEN(MINUTE)
	YY_BREAK
case 97:
YY_RULE_SETUP
#line 157 "flex_lexer.l"
TOKEN(MINUTES)
	YY_BREAK
case 98:
YY_RULE_SETUP
#line 158 "flex_lexer.l"
TOKEN(MONTH)
	YY_BREAK
case 99:
YY_RULE_SETUP
#line 159 "flex_lexer.l"
TOKEN(MONTHS)
	YY_BREAK
case 100:
YY_RULE_SETUP
#line 160 "flex_lexer.l"
TOKEN(NATURAL)
	YY_BREAK
case 101:
YY_RULE_SETUP
#line 161 "flex_lexer.l"
TOKEN(NO)
	YY_BREAK
case 102:
YY_RULE_SETUP
#line 162 "flex_lexer.l"
TOKEN(NOT)
	YY_BREAK
case 103:
YY_RULE_SETUP
#line 163 "flex_lexer.l"
TOKEN(NOWAIT)
	YY_BREAK
case 104:
YY_RULE_SETUP
#line 164 "flex_lexer.l"
TOKEN(NULL)
	YY_BREAK
case 105:
YY_RULE_SETUP
#line 165 "flex_lexer.l"
TOKEN(NVARCHAR)
	YY_BREAK
case 106:
YY_RULE_SETUP
#line 166 "flex_lexer.l"
TOKEN(OF)
	YY_BREAK
case 107:
YY_RULE_SETUP
#line 167 "flex_lexer.l"
TOKEN(OFF)
	YY_BREAK
case 108:
YY_RULE_SETUP
#line 168 "flex_lexer.l"
TOKEN(OFFSET)
	YY_BREAK
case 109:
YY_RULE_SETUP
#line 169 "flex_lexer.l"
TOKEN(ON)
	YY_BREAK
case 110:
YY_RULE_SETUP
#line 170 "flex_lexer.l"
TOKEN(OR)
	YY_BREAK
case 111:
YY_RULE_SETUP
#line 171 "flex_lexer.l"
TOKEN(ORDER)
	YY_BREAK
case 112:
YY_RULE_SETUP
#line 172 "flex_lexer.l"
TOKEN(OUTER)
	YY_BREAK
case 113:
YY_RULE_SETUP
#line 173 "flex_lexer.l"
TOKEN(OVER)
	YY_BREAK
case 114:
YY_RULE_SETUP
#line 174 "flex_lexer.l"
TOKEN(PARAMETERS)
	YY_BREAK
case 115:
YY_RULE_SETUP
#line 175 "flex_lexer.l"
TOKEN(PARTITION)
	YY_BREAK
case 116:
YY_RULE_SETUP
#line 176 "flex_lexer.l"
TOKEN(PLAN)
	YY_BREAK
case 117:
YY_RULE_SETUP
#line 177 "flex_lexer.l"
TOKEN(PRECEDING)
	YY_BREAK
case 118:
YY_RULE_SETUP
#line 178 "flex_lexer.l"
TOKEN(PREPARE)
	YY_BREAK
case 119:
YY_RULE_SETUP
#line 179 "flex_lexer.l"
TOKEN(PRIMARY)
	YY_BREAK
case 120:
YY_RULE_SETUP
#line 180 "flex_lexer.l"
TOKEN(RANGE)
	YY_BREAK
case 121:
YY_RULE_SETUP
#line 181 "flex_lexer.l"
TOKEN(REAL)
	YY_BREAK
case 122:
YY_RULE_SETUP
#line 182 "flex_lexer.l"
TOKEN(RENAME)
	YY_BREAK
case 123:
YY_RULE_SETUP
#line 183 "flex_lexer.l"
TOKEN(RESTRICT)
	YY_BREAK
case 124:
YY_RULE_SETUP
#line 184 "flex_lexer.l"
TOKEN(RIGHT)
	YY_BREAK
case 125:
YY_RULE_SETUP
#line 185 "flex_lexer.l"
TOKEN(ROLLBACK)
	YY_BREAK
case 126:
YY_RULE_SETUP
#line 186 "flex_lexer.l"
TOKEN(ROWS)
	YY_BREAK
case 127:
YY_RULE_SETUP
#line 187 "flex_lexer.l"
TOKEN(SCHEMA)
	YY_BREAK
case 128:
YY_RULE_SETUP
#line 188 "flex_lexer.l"
TOKEN(SCHEMAS)
	YY_BREAK
case 129:
YY_RULE_SETUP
#line 189 "flex_lexer.l"
TOKEN(SECOND)
	YY_BREAK
case 130:
YY_RULE_SETUP
#line 190 "flex_lexer.l"
TOKEN(SECONDS)
	YY_BREAK
case 131:
YY_RULE_SETUP
#line 191 "flex_lexer.l"
TOKEN(SELECT)
	YY_BREAK
case 132:
YY_RULE_SETUP
#line 192 "flex_lexer.l"
TOKEN(SET)
	YY_BREAK
case 133:
YY_RULE_SETUP
#line 193 "flex_lexer.l"
TOKEN(SHARE)
	YY_BREAK
case 134:
YY_RULE_SETUP
#line 194 "flex_lexer.l"
TOKEN(SHOW)
	YY_BREAK
case 135:
YY_RULE_SETUP
#line 195 "flex_lexer.l"
TOKEN(SKIP)
	YY_BREAK
case 136:
YY_RULE_SETUP
#line 196 "flex_lexer.l"
TOKEN(SMALLINT)
	YY_BREAK
case 137:
YY_RULE_SETUP
#line 197 "flex_lexer.l"
TOKEN(SORTED)
	YY_BREAK
case 138:
YY_RULE_SETUP
#line 198 "flex_lexer.l"
TOKEN(SPATIAL)
	YY_BREAK
case 139:
YY_RULE_SETUP
#line 199 "flex_lexer.l"
TOKEN(TABLE)
	YY_BREAK
case 140:
YY_RULE_SETUP
#line 200 "flex_lexer.l"
TOKEN(TABLES)
	YY_BREAK
case 141:
YY_RULE_SETUP
#line 201 "flex_lexer.l"
TOKEN(TEMPORARY)
	YY_BREAK
case 142:
YY_RULE_SETUP
#line 202 "flex_lexer.l"
TOKEN(TEXT)
	YY_BREAK
case 143:
YY_RULE_SETUP
#line 203 "flex_lexer.l"
TOKEN(THEN)
	YY_BREAK
case 144:
YY_RULE_SETUP
#line 204 "flex_lexer.l"
TOKEN(TIME)
	YY_BREAK
case 145:
YY_RULE_SETUP
#line 205 "flex_lexer.l"
TOKEN(TIMESTAMP)
	YY_BREAK
case 146:
YY_RULE_SETUP
#line 206 "flex_lexer.l"
TOKEN(TO)
	YY_BREAK
case 147:
YY_RULE_SETUP
#line 207 "flex_lexer.l"
TOKEN(TOP)
	YY_BREAK
case 148:
YY_RULE_SETUP
#line 208 "flex_lexer.l"
TOKEN(TRANSACTION)
	YY_BREAK
case 149:
YY_RULE_SETUP
#line 209 "flex_lexer.l"
TOKEN(TRUE)
	YY_BREAK
case 150:
YY_RULE_SETUP
#line 210 "flex_lexer.l"
TOKEN(TRUNCATE)
	YY_BREAK
case 151:
YY_RULE_SETUP
#line 211 "flex_lexer.l"
TOKEN(UNBOUNDED)
	YY_BREAK
case 152:
YY_RULE_SETUP
#line 212 "flex_lexer.l"
TOKEN(UNION)
	YY_BREAK
case 153:
YY_RULE_SETUP
#line 213 "flex_lexer.l"
TOKEN(UNIQUE)
	YY_BREAK
case 154:
YY_RULE_SETUP
#line 214 "flex_lexer.l"
TOKEN(UNLOAD)
	YY_BREAK
case 155:
YY_RULE_SETUP
#line 215 "flex_lexer.l"
TOKEN(UPDATE)
	YY_BREAK
case 156:
YY_RULE_SETUP
#line 216 "flex_lexer.l"
TOKEN(USING)
	YY_BREAK
case 157:
YY_RULE_SETUP
#line 217 "flex_lexer.l"
TOKEN(VALUES)
	YY_BREAK
case 158:
YY_RULE_SETUP
#line 218 "flex_lexer.l"
TOKEN(VARCHAR)
	YY_BREAK
case 159:
YY_RULE_SETUP
#line 219 "flex_lexer.l"
TOKEN(VIEW)
	YY_BREAK
case 160:
YY_RULE_SETUP
#line 220 "flex_lexer.l"
TOKEN(VIRTUAL)
	YY_BREAK
case 161:
YY_RULE_SETUP
#line 221 "flex_lexer.l"
TOKEN(WHEN)
	YY_BREAK
case 162:
YY_RULE_SETUP
#line 222 "flex_lexer.l"
TOKEN(WHERE)
	YY_BREAK
case 163:
YY_RULE_SETUP
#line 223 "flex_lexer.l"
TOKEN(WITH)
	YY_BREAK
case 164:
YY_RULE_SETUP
#line 224 "flex_lexer.l"
TOKEN(YEAR)
	YY_BREAK
case 165:
YY_RULE_SETUP
#line 225 "flex_lexer.l"
TOKEN(YEARS)
	YY_BREAK
case 166:
/* rule 166 can match eol */
YY_RULE_SETUP
#line 227 "flex_lexer.l"
TOKEN(CURRENT_ROW)
	YY_BREAK
case 167:
/* rule 167 can match eol */
YY_RULE_SETUP
#line 228 "flex_lexer.l"
TOKEN(CHARACTER_VARYING)
	YY_BREAK
/* Allow =/== see https://sqlite.org/lang_expr.html#collateop */
case 168:
YY_RULE_SETUP
#line 231 "flex_lexer.l"
TOKEN(EQUALS)
	YY_BREAK
case 169:
YY_RULE_SETUP
#line 232 "flex_lexer.l"
TOKEN(NOTEQUALS)
	YY_BREAK
case 170:
YY_RULE_SETUP
#line 233 "flex_lexer.l"
TOKEN(NOTEQUALS)
	YY_BREAK
case 171:
YY_RULE_SETUP
#line 234 "flex_lexer.l"
TOKEN(LESSEQ)
	YY_BREAK
case 172:
YY_RULE_SETUP
#line 235 "flex_lexer.l"
TOKEN(GREATEREQ)
	YY_BREAK
case 173:
YY_RULE_SETUP
#line 236 "flex_lexer.l"
TOKEN(CONCAT)
	YY_BREAK
case 174:
YY_RULE_SETUP
#line 238 "flex_lexer.l"
{ return yytext[0]; }
	YY_BREAK
case 175:
#line 241 "flex_lexer.l"
case 176:
YY_RULE_SETUP
#line 241 "flex_lexer.l"
{
  yylval->fval = atof(yytext);
  return SQL_FLOATVAL;
}
	YY_BREAK
/*
   * Regularly, negative literals are treated as <unary minus> <positive literal>. This does not work for LLONG_MIN, as it has no
   * positive equivalent. We thus match for LLONG_MIN specifically. This is not an issue for floats, where
   *   numeric_limits<double>::lowest() == -numeric_limits<double>::max();
   */
case 177:
YY_RULE_SETUP
#line 251 "flex_lexer.l"
{
  yylval->ival = LLONG_MIN;
  return SQL_INTVAL;
}
	YY_BREAK
case 178:
YY_RULE_SETUP
#line 256 "flex_lexer.l"
{
  errno = 0;
  yylval->ival = strtoll(yytext, nullptr, 0);
  if (errno) {
    return fprintf(stderr, "[SQL-Lexer-Error] Integer cannot be parsed - is it out of range?");
    return 0;
  }
  return SQL_INTVAL;
}
	YY_BREAK
case 179:
YY_RULE_SETUP
#line 266 "flex_lexer.l"
{
  // Crop the leading and trailing quote char
  yylval->sval = hsql::substr(yytext, 1, strlen(yytext)-1);
  return SQL_IDENTIFIER;
}
	YY_BREAK
case 180:
YY_RULE_SETUP
#line 272 "flex_lexer.l"
{
  yylval->sval = strdup(yytext);
  return SQL_IDENTIFIER;
}
	YY_BREAK
case 181:
YY_RULE_SETUP
#line 277 "flex_lexer.l"
{ BEGIN singlequotedstring; strbuf.clear(); strbuf.str(""); }  // Clear strbuf manually, see #170
	YY_BREAK
case 182:
YY_RULE_SETUP
#line 278 "flex_lexer.l"
{ strbuf << '\''; }
	YY_BREAK
case 183:
/* rule 183 can match eol */
YY_RULE_SETUP
#line 279 "flex_lexer.l"
{ strbuf << yytext; }
	YY_BREAK
case 184:
YY_RULE_SETUP
#line 280 "flex_lexer.l"
{ BEGIN 0; yylval->sval = strdup(strbuf.str().c_str()); return SQL_STRING; }
	YY_BREAK
case YY_STATE_EOF(singlequotedstring):
#line 281 "flex_lexer.l"
{ fprintf(stderr, "[SQL-Lexer-Error] Unterminated string\n"); return 0; }
	YY_BREAK
case 185:
YY_RULE_SETUP
#line 283 "flex_lexer.l"
{ fprintf(stderr, "[SQL-Lexer-Error] Unknown Character: %c\n", yytext[0]); return 0; }
	YY_BREAK
case 186:
YY_RULE_SETUP
#line 285 "flex_lexer.l"
ECHO;
	YY_BREAK
#line 4381 "flex_lexer.cpp"
case YY_STATE_EOF(INITIAL):
case YY_STATE_EOF(COMMENT):
	yyterminate();

	case YY_END_OF_BUFFER:
		{
		/* Amount of text matched not including the EOB char. */
		int yy_amount_of_matched_text = (int) (yy_cp - yyg->yytext_ptr) - 1;

		/* Undo the effects of YY_DO_BEFORE_ACTION. */
		*yy_cp = yyg->yy_hold_char;
		YY_RESTORE_YY_MORE_OFFSET

		if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_NEW )
			{
			/* We're scanning a new file or input source.  It's
			 * possible that this happened because the user
			 * just pointed yyin at a new source and called
			 * yylex().  If so, then we have to assure
			 * consistency between YY_CURRENT_BUFFER and our
			 * globals.  Here is the right place to do so, because
			 * this is the first action (other than possibly a
			 * back-up) that will match for the new input source.
			 */
			yyg->yy_n_chars = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
			YY_CURRENT_BUFFER_LVALUE->yy_input_file = yyin;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status = YY_BUFFER_NORMAL;
			}

		/* Note that here we test for yy_c_buf_p "<=" to the position
		 * of the first EOB in the buffer, since yy_c_buf_p will
		 * already have been incremented past the NUL character
		 * (since all states make transitions on EOB to the
		 * end-of-buffer state).  Contrast this with the test
		 * in input().
		 */
		if ( yyg->yy_c_buf_p <= &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars] )
			{ /* This was really a NUL. */
			yy_state_type yy_next_state;

			yyg->yy_c_buf_p = yyg->yytext_ptr + yy_amount_of_matched_text;

			yy_current_state = yy_get_previous_state( yyscanner );

			/* Okay, we're now positioned to make the NUL
			 * transition.  We couldn't have
			 * yy_get_previous_state() go ahead and do it
			 * for us because it doesn't know how to deal
			 * with the possibility of jamming (and we don't
			 * want to build jamming into it because then it
			 * will run more slowly).
			 */

			yy_next_state = yy_try_NUL_trans( yy_current_state , yyscanner);

			yy_bp = yyg->yytext_ptr + YY_MORE_ADJ;

			if ( yy_next_state )
				{
				/* Consume the NUL. */
				yy_cp = ++yyg->yy_c_buf_p;
				yy_current_state = yy_next_state;
				goto yy_match;
				}

			else
				{
				yy_cp = yyg->yy_last_accepting_cpos;
				yy_current_state = yyg->yy_last_accepting_state;
				goto yy_find_action;
				}
			}

		else switch ( yy_get_next_buffer( yyscanner ) )
			{
			case EOB_ACT_END_OF_FILE:
				{
				yyg->yy_did_buffer_switch_on_eof = 0;

				if ( yywrap( yyscanner ) )
					{
					/* Note: because we've taken care in
					 * yy_get_next_buffer() to have set up
					 * yytext, we can now set up
					 * yy_c_buf_p so that if some total
					 * hoser (like flex itself) wants to
					 * call the scanner after we return the
					 * YY_NULL, it'll still work - another
					 * YY_NULL will get returned.
					 */
					yyg->yy_c_buf_p = yyg->yytext_ptr + YY_MORE_ADJ;

					yy_act = YY_STATE_EOF(YY_START);
					goto do_action;
					}

				else
					{
					if ( ! yyg->yy_did_buffer_switch_on_eof )
						YY_NEW_FILE;
					}
				break;
				}

			case EOB_ACT_CONTINUE_SCAN:
				yyg->yy_c_buf_p =
					yyg->yytext_ptr + yy_amount_of_matched_text;

				yy_current_state = yy_get_previous_state( yyscanner );

				yy_cp = yyg->yy_c_buf_p;
				yy_bp = yyg->yytext_ptr + YY_MORE_ADJ;
				goto yy_match;

			case EOB_ACT_LAST_MATCH:
				yyg->yy_c_buf_p =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars];

				yy_current_state = yy_get_previous_state( yyscanner );

				yy_cp = yyg->yy_c_buf_p;
				yy_bp = yyg->yytext_ptr + YY_MORE_ADJ;
				goto yy_find_action;
			}
		break;
		}

	default:
		YY_FATAL_ERROR(
			"fatal flex scanner internal error--no action found" );
	} /* end of action switch */
		} /* end of scanning one token */
	} /* end of user's declarations */
} /* end of yylex */

/* yy_get_next_buffer - try to read in a new buffer
 *
 * Returns a code representing an action:
 *	EOB_ACT_LAST_MATCH -
 *	EOB_ACT_CONTINUE_SCAN - continue scanning from current position
 *	EOB_ACT_END_OF_FILE - end of file
 */
static int yy_get_next_buffer (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	char *dest = YY_CURRENT_BUFFER_LVALUE->yy_ch_buf;
	char *source = yyg->yytext_ptr;
	int number_to_move, i;
	int ret_val;

	if ( yyg->yy_c_buf_p > &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars + 1] )
		YY_FATAL_ERROR(
		"fatal flex scanner internal error--end of buffer missed" );

	if ( YY_CURRENT_BUFFER_LVALUE->yy_fill_buffer == 0 )
		{ /* Don't try to fill the buffer, so this is an EOF. */
		if ( yyg->yy_c_buf_p - yyg->yytext_ptr - YY_MORE_ADJ == 1 )
			{
			/* We matched a single character, the EOB, so
			 * treat this as a final EOF.
			 */
			return EOB_ACT_END_OF_FILE;
			}

		else
			{
			/* We matched some text prior to the EOB, first
			 * process it.
			 */
			return EOB_ACT_LAST_MATCH;
			}
		}

	/* Try to read more data. */

	/* First move last chars to start of buffer. */
	number_to_move = (int) (yyg->yy_c_buf_p - yyg->yytext_ptr - 1);

	for ( i = 0; i < number_to_move; ++i )
		*(dest++) = *(source++);

	if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_EOF_PENDING )
		/* don't do the read, it's not guaranteed to return an EOF,
		 * just force an EOF
		 */
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars = 0;

	else
		{
			int num_to_read =
			YY_CURRENT_BUFFER_LVALUE->yy_buf_size - number_to_move - 1;

		while ( num_to_read <= 0 )
			{ /* Not enough room in the buffer - grow it. */

			/* just a shorter name for the current buffer */
			YY_BUFFER_STATE b = YY_CURRENT_BUFFER_LVALUE;

			int yy_c_buf_p_offset =
				(int) (yyg->yy_c_buf_p - b->yy_ch_buf);

			if ( b->yy_is_our_buffer )
				{
				int new_size = b->yy_buf_size * 2;

				if ( new_size <= 0 )
					b->yy_buf_size += b->yy_buf_size / 8;
				else
					b->yy_buf_size *= 2;

				b->yy_ch_buf = (char *)
					/* Include room in for 2 EOB chars. */
					yyrealloc( (void *) b->yy_ch_buf,
							 (yy_size_t) (b->yy_buf_size + 2) , yyscanner );
				}
			else
				/* Can't grow it, we don't own it. */
				b->yy_ch_buf = NULL;

			if ( ! b->yy_ch_buf )
				YY_FATAL_ERROR(
				"fatal error - scanner input buffer overflow" );

			yyg->yy_c_buf_p = &b->yy_ch_buf[yy_c_buf_p_offset];

			num_to_read = YY_CURRENT_BUFFER_LVALUE->yy_buf_size -
						number_to_move - 1;

			}

		if ( num_to_read > YY_READ_BUF_SIZE )
			num_to_read = YY_READ_BUF_SIZE;

		/* Read in more data. */
		YY_INPUT( (&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move]),
			yyg->yy_n_chars, num_to_read );

		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars;
		}

	if ( yyg->yy_n_chars == 0 )
		{
		if ( number_to_move == YY_MORE_ADJ )
			{
			ret_val = EOB_ACT_END_OF_FILE;
			yyrestart( yyin  , yyscanner);
			}

		else
			{
			ret_val = EOB_ACT_LAST_MATCH;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status =
				YY_BUFFER_EOF_PENDING;
			}
		}

	else
		ret_val = EOB_ACT_CONTINUE_SCAN;

	if ((yyg->yy_n_chars + number_to_move) > YY_CURRENT_BUFFER_LVALUE->yy_buf_size) {
		/* Extend the array by 50%, plus the number we really need. */
		int new_size = yyg->yy_n_chars + number_to_move + (yyg->yy_n_chars >> 1);
		YY_CURRENT_BUFFER_LVALUE->yy_ch_buf = (char *) yyrealloc(
			(void *) YY_CURRENT_BUFFER_LVALUE->yy_ch_buf, (yy_size_t) new_size , yyscanner );
		if ( ! YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			YY_FATAL_ERROR( "out of dynamic memory in yy_get_next_buffer()" );
		/* "- 2" to take care of EOB's */
		YY_CURRENT_BUFFER_LVALUE->yy_buf_size = (int) (new_size - 2);
	}

	yyg->yy_n_chars += number_to_move;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars] = YY_END_OF_BUFFER_CHAR;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars + 1] = YY_END_OF_BUFFER_CHAR;

	yyg->yytext_ptr = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[0];

	return ret_val;
}

/* yy_get_previous_state - get the state just before the EOB char was reached */

    static yy_state_type yy_get_previous_state (yyscan_t yyscanner)
{
	yy_state_type yy_current_state;
	char *yy_cp;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	yy_current_state = yyg->yy_start;

	for ( yy_cp = yyg->yytext_ptr + YY_MORE_ADJ; yy_cp < yyg->yy_c_buf_p; ++yy_cp )
		{
		YY_CHAR yy_c = (*yy_cp ? yy_ec[YY_SC_TO_UI(*yy_cp)] : 1);
		if ( yy_accept[yy_current_state] )
			{
			yyg->yy_last_accepting_state = yy_current_state;
			yyg->yy_last_accepting_cpos = yy_cp;
			}
		while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
			{
			yy_current_state = (int) yy_def[yy_current_state];
			if ( yy_current_state >= 1332 )
				yy_c = yy_meta[yy_c];
			}
		yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
		}

	return yy_current_state;
}

/* yy_try_NUL_trans - try to make a transition on the NUL character
 *
 * synopsis
 *	next_state = yy_try_NUL_trans( current_state );
 */
    static yy_state_type yy_try_NUL_trans  (yy_state_type yy_current_state , yyscan_t yyscanner)
{
	int yy_is_jam;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner; /* This var may be unused depending upon options. */
	char *yy_cp = yyg->yy_c_buf_p;

	YY_CHAR yy_c = 1;
	if ( yy_accept[yy_current_state] )
		{
		yyg->yy_last_accepting_state = yy_current_state;
		yyg->yy_last_accepting_cpos = yy_cp;
		}
	while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
		{
		yy_current_state = (int) yy_def[yy_current_state];
		if ( yy_current_state >= 1332 )
			yy_c = yy_meta[yy_c];
		}
	yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
	yy_is_jam = (yy_current_state == 1331);

	(void)yyg;
	return yy_is_jam ? 0 : yy_current_state;
}

#ifndef YY_NO_UNPUT

#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
    static int yyinput (yyscan_t yyscanner)
#else
    static int input  (yyscan_t yyscanner)
#endif

{
	int c;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	*yyg->yy_c_buf_p = yyg->yy_hold_char;

	if ( *yyg->yy_c_buf_p == YY_END_OF_BUFFER_CHAR )
		{
		/* yy_c_buf_p now points to the character we want to return.
		 * If this occurs *before* the EOB characters, then it's a
		 * valid NUL; if not, then we've hit the end of the buffer.
		 */
		if ( yyg->yy_c_buf_p < &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars] )
			/* This was really a NUL. */
			*yyg->yy_c_buf_p = '\0';

		else
			{ /* need more input */
			int offset = (int) (yyg->yy_c_buf_p - yyg->yytext_ptr);
			++yyg->yy_c_buf_p;

			switch ( yy_get_next_buffer( yyscanner ) )
				{
				case EOB_ACT_LAST_MATCH:
					/* This happens because yy_g_n_b()
					 * sees that we've accumulated a
					 * token and flags that we need to
					 * try matching the token before
					 * proceeding.  But for input(),
					 * there's no matching to consider.
					 * So convert the EOB_ACT_LAST_MATCH
					 * to EOB_ACT_END_OF_FILE.
					 */

					/* Reset buffer status. */
					yyrestart( yyin , yyscanner);

					/*FALLTHROUGH*/

				case EOB_ACT_END_OF_FILE:
					{
					if ( yywrap( yyscanner ) )
						return 0;

					if ( ! yyg->yy_did_buffer_switch_on_eof )
						YY_NEW_FILE;
#ifdef __cplusplus
					return yyinput(yyscanner);
#else
					return input(yyscanner);
#endif
					}

				case EOB_ACT_CONTINUE_SCAN:
					yyg->yy_c_buf_p = yyg->yytext_ptr + offset;
					break;
				}
			}
		}

	c = *(unsigned char *) yyg->yy_c_buf_p;	/* cast for 8-bit char's */
	*yyg->yy_c_buf_p = '\0';	/* preserve yytext */
	yyg->yy_hold_char = *++yyg->yy_c_buf_p;

	return c;
}
#endif	/* ifndef YY_NO_INPUT */

/** Immediately switch to a different input stream.
 * @param input_file A readable stream.
 * @param yyscanner The scanner object.
 * @note This function does not reset the start condition to @c INITIAL .
 */
    void yyrestart  (FILE * input_file , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	if ( ! YY_CURRENT_BUFFER ){
        yyensure_buffer_stack (yyscanner);
		YY_CURRENT_BUFFER_LVALUE =
            yy_create_buffer( yyin, YY_BUF_SIZE , yyscanner);
	}

	yy_init_buffer( YY_CURRENT_BUFFER, input_file , yyscanner);
	yy_load_buffer_state( yyscanner );
}

/** Switch to a different input buffer.
 * @param new_buffer The new input buffer.
 * @param yyscanner The scanner object.
 */
    void yy_switch_to_buffer  (YY_BUFFER_STATE  new_buffer , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	/* TODO. We should be able to replace this entire function body
	 * with
	 *		yypop_buffer_state();
	 *		yypush_buffer_state(new_buffer);
     */
	yyensure_buffer_stack (yyscanner);
	if ( YY_CURRENT_BUFFER == new_buffer )
		return;

	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*yyg->yy_c_buf_p = yyg->yy_hold_char;
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = yyg->yy_c_buf_p;
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars;
		}

	YY_CURRENT_BUFFER_LVALUE = new_buffer;
	yy_load_buffer_state( yyscanner );

	/* We don't actually know whether we did this switch during
	 * EOF (yywrap()) processing, but the only time this flag
	 * is looked at is after yywrap() is called, so it's safe
	 * to go ahead and always set it.
	 */
	yyg->yy_did_buffer_switch_on_eof = 1;
}

static void yy_load_buffer_state  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	yyg->yy_n_chars = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
	yyg->yytext_ptr = yyg->yy_c_buf_p = YY_CURRENT_BUFFER_LVALUE->yy_buf_pos;
	yyin = YY_CURRENT_BUFFER_LVALUE->yy_input_file;
	yyg->yy_hold_char = *yyg->yy_c_buf_p;
}

/** Allocate and initialize an input buffer state.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * @param yyscanner The scanner object.
 * @return the allocated buffer state.
 */
    YY_BUFFER_STATE yy_create_buffer  (FILE * file, int  size , yyscan_t yyscanner)
{
	YY_BUFFER_STATE b;
    
	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state ) , yyscanner );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_buf_size = size;

	/* yy_ch_buf has to be 2 characters longer than the size given because
	 * we need to put in 2 end-of-buffer characters.
	 */
	b->yy_ch_buf = (char *) yyalloc( (yy_size_t) (b->yy_buf_size + 2) , yyscanner );
	if ( ! b->yy_ch_buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_is_our_buffer = 1;

	yy_init_buffer( b, file , yyscanner);

	return b;
}

/** Destroy the buffer.
 * @param b a buffer created with yy_create_buffer()
 * @param yyscanner The scanner object.
 */
    void yy_delete_buffer (YY_BUFFER_STATE  b , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	if ( ! b )
		return;

	if ( b == YY_CURRENT_BUFFER ) /* Not sure if we should pop here. */
		YY_CURRENT_BUFFER_LVALUE = (YY_BUFFER_STATE) 0;

	if ( b->yy_is_our_buffer )
		yyfree( (void *) b->yy_ch_buf , yyscanner );

	yyfree( (void *) b , yyscanner );
}

/* Initializes or reinitializes a buffer.
 * This function is sometimes called more than once on the same buffer,
 * such as during a yyrestart() or at EOF.
 */
    static void yy_init_buffer  (YY_BUFFER_STATE  b, FILE * file , yyscan_t yyscanner)

{
	int oerrno = errno;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	yy_flush_buffer( b , yyscanner);

	b->yy_input_file = file;
	b->yy_fill_buffer = 1;

    /* If b is the current buffer, then yy_init_buffer was _probably_
     * called from yyrestart() or through yy_get_next_buffer.
     * In that case, we don't want to reset the lineno or column.
     */
    if (b != YY_CURRENT_BUFFER){
        b->yy_bs_lineno = 1;
        b->yy_bs_column = 0;
    }

        b->yy_is_interactive = 0;
    
	errno = oerrno;
}

/** Discard all buffered characters. On the next scan, YY_INPUT will be called.
 * @param b the buffer state to be flushed, usually @c YY_CURRENT_BUFFER.
 * @param yyscanner The scanner object.
 */
    void yy_flush_buffer (YY_BUFFER_STATE  b , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	if ( ! b )
		return;

	b->yy_n_chars = 0;

	/* We always need two end-of-buffer characters.  The first causes
	 * a transition to the end-of-buffer state.  The second causes
	 * a jam in that state.
	 */
	b->yy_ch_buf[0] = YY_END_OF_BUFFER_CHAR;
	b->yy_ch_buf[1] = YY_END_OF_BUFFER_CHAR;

	b->yy_buf_pos = &b->yy_ch_buf[0];

	b->yy_at_bol = 1;
	b->yy_buffer_status = YY_BUFFER_NEW;

	if ( b == YY_CURRENT_BUFFER )
		yy_load_buffer_state( yyscanner );
}

/** Pushes the new state onto the stack. The new state becomes
 *  the current state. This function will allocate the stack
 *  if necessary.
 *  @param new_buffer The new state.
 *  @param yyscanner The scanner object.
 */
void yypush_buffer_state (YY_BUFFER_STATE new_buffer , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	if (new_buffer == NULL)
		return;

	yyensure_buffer_stack(yyscanner);

	/* This block is copied from yy_switch_to_buffer. */
	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*yyg->yy_c_buf_p = yyg->yy_hold_char;
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = yyg->yy_c_buf_p;
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars;
		}

	/* Only push if top exists. Otherwise, replace top. */
	if (YY_CURRENT_BUFFER)
		yyg->yy_buffer_stack_top++;
	YY_CURRENT_BUFFER_LVALUE = new_buffer;

	/* copied from yy_switch_to_buffer. */
	yy_load_buffer_state( yyscanner );
	yyg->yy_did_buffer_switch_on_eof = 1;
}

/** Removes and deletes the top of the stack, if present.
 *  The next element becomes the new top.
 *  @param yyscanner The scanner object.
 */
void yypop_buffer_state (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	if (!YY_CURRENT_BUFFER)
		return;

	yy_delete_buffer(YY_CURRENT_BUFFER , yyscanner);
	YY_CURRENT_BUFFER_LVALUE = NULL;
	if (yyg->yy_buffer_stack_top > 0)
		--yyg->yy_buffer_stack_top;

	if (YY_CURRENT_BUFFER) {
		yy_load_buffer_state( yyscanner );
		yyg->yy_did_buffer_switch_on_eof = 1;
	}
}

/* Allocates the stack if it does not exist.
 *  Guarantees space for at least one push.
 */
static void yyensure_buffer_stack (yyscan_t yyscanner)
{
	yy_size_t num_to_alloc;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	if (!yyg->yy_buffer_stack) {

		/* First allocation is just for 2 elements, since we don't know if this
		 * scanner will even need a stack. We use 2 instead of 1 to avoid an
		 * immediate realloc on the next call.
         */
      num_to_alloc = 1; /* After all that talk, this was set to 1 anyways... */
		yyg->yy_buffer_stack = (struct yy_buffer_state**)yyalloc
								(num_to_alloc * sizeof(struct yy_buffer_state*)
								, yyscanner);
		if ( ! yyg->yy_buffer_stack )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		memset(yyg->yy_buffer_stack, 0, num_to_alloc * sizeof(struct yy_buffer_state*));

		yyg->yy_buffer_stack_max = num_to_alloc;
		yyg->yy_buffer_stack_top = 0;
		return;
	}

	if (yyg->yy_buffer_stack_top >= (yyg->yy_buffer_stack_max) - 1){

		/* Increase the buffer to prepare for a possible push. */
		yy_size_t grow_size = 8 /* arbitrary grow size */;

		num_to_alloc = yyg->yy_buffer_stack_max + grow_size;
		yyg->yy_buffer_stack = (struct yy_buffer_state**)yyrealloc
								(yyg->yy_buffer_stack,
								num_to_alloc * sizeof(struct yy_buffer_state*)
								, yyscanner);
		if ( ! yyg->yy_buffer_stack )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		/* zero only the new slots.*/
		memset(yyg->yy_buffer_stack + yyg->yy_buffer_stack_max, 0, grow_size * sizeof(struct yy_buffer_state*));
		yyg->yy_buffer_stack_max = num_to_alloc;
	}
}

/** Setup the input buffer state to scan directly from a user-specified character buffer.
 * @param base the character buffer
 * @param size the size in bytes of the character buffer
 * @param yyscanner The scanner object.
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_buffer  (char * base, yy_size_t  size , yyscan_t yyscanner)
{
	YY_BUFFER_STATE b;
    
	if ( size < 2 ||
	     base[size-2] != YY_END_OF_BUFFER_CHAR ||
	     base[size-1] != YY_END_OF_BUFFER_CHAR )
		/* They forgot to leave room for the EOB's. */
		return NULL;

	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state ) , yyscanner );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_buffer()" );

	b->yy_buf_size = (int) (size - 2);	/* "- 2" to take care of EOB's */
	b->yy_buf_pos = b->yy_ch_buf = base;
	b->yy_is_our_buffer = 0;
	b->yy_input_file = NULL;
	b->yy_n_chars = b->yy_buf_size;
	b->yy_is_interactive = 0;
	b->yy_at_bol = 1;
	b->yy_fill_buffer = 0;
	b->yy_buffer_status = YY_BUFFER_NEW;

	yy_switch_to_buffer( b , yyscanner );

	return b;
}

/** Setup the input buffer state to scan a string. The next call to yylex() will
 * scan from a @e copy of @a str.
 * @param yystr a NUL-terminated string to scan
 * @param yyscanner The scanner object.
 * @return the newly allocated buffer state object.
 * @note If you want to scan bytes that may contain NUL values, then use
 *       yy_scan_bytes() instead.
 */
YY_BUFFER_STATE yy_scan_string (const char * yystr , yyscan_t yyscanner)
{
    
	return yy_scan_bytes( yystr, (int) strlen(yystr) , yyscanner);
}

/** Setup the input buffer state to scan the given bytes. The next call to yylex() will
 * scan from a @e copy of @a bytes.
 * @param yybytes the byte buffer to scan
 * @param _yybytes_len the number of bytes in the buffer pointed to by @a bytes.
 * @param yyscanner The scanner object.
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_bytes  (const char * yybytes, int  _yybytes_len , yyscan_t yyscanner)
{
	YY_BUFFER_STATE b;
	char *buf;
	yy_size_t n;
	int i;
    
	/* Get memory for full buffer, including space for trailing EOB's. */
	n = (yy_size_t) (_yybytes_len + 2);
	buf = (char *) yyalloc( n , yyscanner );
	if ( ! buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_bytes()" );

	for ( i = 0; i < _yybytes_len; ++i )
		buf[i] = yybytes[i];

	buf[_yybytes_len] = buf[_yybytes_len+1] = YY_END_OF_BUFFER_CHAR;

	b = yy_scan_buffer( buf, n , yyscanner);
	if ( ! b )
		YY_FATAL_ERROR( "bad buffer in yy_scan_bytes()" );

	/* It's okay to grow etc. this buffer, and we should throw it
	 * away when we're done.
	 */
	b->yy_is_our_buffer = 1;

	return b;
}

#ifndef YY_EXIT_FAILURE
#define YY_EXIT_FAILURE 2
#endif

static void yynoreturn yy_fatal_error (const char* msg , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;
	fprintf( stderr, "%s\n", msg );
	exit( YY_EXIT_FAILURE );
}

/* Redefine yyless() so it works in section 3 code. */

#undef yyless
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		yytext[yyleng] = yyg->yy_hold_char; \
		yyg->yy_c_buf_p = yytext + yyless_macro_arg; \
		yyg->yy_hold_char = *yyg->yy_c_buf_p; \
		*yyg->yy_c_buf_p = '\0'; \
		yyleng = yyless_macro_arg; \
		} \
	while ( 0 )

/* Accessor  methods (get/set functions) to struct members. */

/** Get the user-defined data for this scanner.
 * @param yyscanner The scanner object.
 */
YY_EXTRA_TYPE yyget_extra  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyextra;
}

/** Get the current line number.
 * @param yyscanner The scanner object.
 */
int yyget_lineno  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

        if (! YY_CURRENT_BUFFER)
            return 0;
    
    return yylineno;
}

/** Get the current column number.
 * @param yyscanner The scanner object.
 */
int yyget_column  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

        if (! YY_CURRENT_BUFFER)
            return 0;
    
    return yycolumn;
}

/** Get the input stream.
 * @param yyscanner The scanner object.
 */
FILE *yyget_in  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyin;
}

/** Get the output stream.
 * @param yyscanner The scanner object.
 */
FILE *yyget_out  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyout;
}

/** Get the length of the current token.
 * @param yyscanner The scanner object.
 */
int yyget_leng  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyleng;
}

/** Get the current token.
 * @param yyscanner The scanner object.
 */

char *yyget_text  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yytext;
}

/** Set the user-defined data. This data is never touched by the scanner.
 * @param user_defined The data to be associated with this scanner.
 * @param yyscanner The scanner object.
 */
void yyset_extra (YY_EXTRA_TYPE  user_defined , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yyextra = user_defined ;
}

/** Set the current line number.
 * @param _line_number line number
 * @param yyscanner The scanner object.
 */
void yyset_lineno (int  _line_number , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

        /* lineno is only valid if an input buffer exists. */
        if (! YY_CURRENT_BUFFER )
           YY_FATAL_ERROR( "yyset_lineno called with no buffer" );
    
    yylineno = _line_number;
}

/** Set the current column.
 * @param _column_no column number
 * @param yyscanner The scanner object.
 */
void yyset_column (int  _column_no , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

        /* column is only valid if an input buffer exists. */
        if (! YY_CURRENT_BUFFER )
           YY_FATAL_ERROR( "yyset_column called with no buffer" );
    
    yycolumn = _column_no;
}

/** Set the input stream. This does not discard the current
 * input buffer.
 * @param _in_str A readable stream.
 * @param yyscanner The scanner object.
 * @see yy_switch_to_buffer
 */
void yyset_in (FILE *  _in_str , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yyin = _in_str ;
}

void yyset_out (FILE *  _out_str , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yyout = _out_str ;
}

int yyget_debug  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yy_flex_debug;
}

void yyset_debug (int  _bdebug , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yy_flex_debug = _bdebug ;
}

/* Accessor methods for yylval and yylloc */

YYSTYPE * yyget_lval  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yylval;
}

void yyset_lval (YYSTYPE *  yylval_param , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yylval = yylval_param;
}

YYLTYPE *yyget_lloc  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yylloc;
}
    
void yyset_lloc (YYLTYPE *  yylloc_param , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yylloc = yylloc_param;
}
    
/* User-visible API */

/* yylex_init is special because it creates the scanner itself, so it is
 * the ONLY reentrant function that doesn't take the scanner as the last argument.
 * That's why we explicitly handle the declaration, instead of using our macros.
 */
int yylex_init(yyscan_t* ptr_yy_globals)
{
    if (ptr_yy_globals == NULL){
        errno = EINVAL;
        return 1;
    }

    *ptr_yy_globals = (yyscan_t) yyalloc ( sizeof( struct yyguts_t ), NULL );

    if (*ptr_yy_globals == NULL){
        errno = ENOMEM;
        return 1;
    }

    /* By setting to 0xAA, we expose bugs in yy_init_globals. Leave at 0x00 for releases. */
    memset(*ptr_yy_globals,0x00,sizeof(struct yyguts_t));

    return yy_init_globals ( *ptr_yy_globals );
}

/* yylex_init_extra has the same functionality as yylex_init, but follows the
 * convention of taking the scanner as the last argument. Note however, that
 * this is a *pointer* to a scanner, as it will be allocated by this call (and
 * is the reason, too, why this function also must handle its own declaration).
 * The user defined value in the first argument will be available to yyalloc in
 * the yyextra field.
 */
int yylex_init_extra( YY_EXTRA_TYPE yy_user_defined, yyscan_t* ptr_yy_globals )
{
    struct yyguts_t dummy_yyguts;

    yyset_extra (yy_user_defined, &dummy_yyguts);

    if (ptr_yy_globals == NULL){
        errno = EINVAL;
        return 1;
    }

    *ptr_yy_globals = (yyscan_t) yyalloc ( sizeof( struct yyguts_t ), &dummy_yyguts );

    if (*ptr_yy_globals == NULL){
        errno = ENOMEM;
        return 1;
    }

    /* By setting to 0xAA, we expose bugs in
    yy_init_globals. Leave at 0x00 for releases. */
    memset(*ptr_yy_globals,0x00,sizeof(struct yyguts_t));

    yyset_extra (yy_user_defined, *ptr_yy_globals);

    return yy_init_globals ( *ptr_yy_globals );
}

static int yy_init_globals (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    /* Initialization is the same as for the non-reentrant scanner.
     * This function is called from yylex_destroy(), so don't allocate here.
     */

    yyg->yy_buffer_stack = NULL;
    yyg->yy_buffer_stack_top = 0;
    yyg->yy_buffer_stack_max = 0;
    yyg->yy_c_buf_p = NULL;
    yyg->yy_init = 0;
    yyg->yy_start = 0;

    yyg->yy_start_stack_ptr = 0;
    yyg->yy_start_stack_depth = 0;
    yyg->yy_start_stack =  NULL;

/* Defined in main.c */
#ifdef YY_STDINIT
    yyin = stdin;
    yyout = stdout;
#else
    yyin = NULL;
    yyout = NULL;
#endif

    /* For future reference: Set errno on error, since we are called by
     * yylex_init()
     */
    return 0;
}

/* yylex_destroy is for both reentrant and non-reentrant scanners. */
int yylex_destroy  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

    /* Pop the buffer stack, destroying each element. */
	while(YY_CURRENT_BUFFER){
		yy_delete_buffer( YY_CURRENT_BUFFER , yyscanner );
		YY_CURRENT_BUFFER_LVALUE = NULL;
		yypop_buffer_state(yyscanner);
	}

	/* Destroy the stack itself. */
	yyfree(yyg->yy_buffer_stack , yyscanner);
	yyg->yy_buffer_stack = NULL;

    /* Destroy the start condition stack. */
        yyfree( yyg->yy_start_stack , yyscanner );
        yyg->yy_start_stack = NULL;

    /* Reset the globals. This is important in a non-reentrant scanner so the next time
     * yylex() is called, initialization will occur. */
    yy_init_globals( yyscanner);

    /* Destroy the main struct (reentrant only). */
    yyfree ( yyscanner , yyscanner );
    yyscanner = NULL;
    return 0;
}

/*
 * Internal utility routines.
 */

#ifndef yytext_ptr
static void yy_flex_strncpy (char* s1, const char * s2, int n , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;

	int i;
	for ( i = 0; i < n; ++i )
		s1[i] = s2[i];
}
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (const char * s , yyscan_t yyscanner)
{
	int n;
	for ( n = 0; s[n]; ++n )
		;

	return n;
}
#endif

void *yyalloc (yy_size_t  size , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;
	return malloc(size);
}

void *yyrealloc  (void * ptr, yy_size_t  size , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;

	/* The cast to (char *) in the following accommodates both
	 * implementations that use char* generic pointers, and those
	 * that use void* generic pointers.  It works with the latter
	 * because both ANSI C and C++ allow castless assignment from
	 * any pointer type to void*, and deal with argument conversions
	 * as though doing an assignment.
	 */
	return realloc(ptr, size);
}

void yyfree (void * ptr , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;
	free( (char *) ptr );	/* see yyrealloc() for (char *) cast */
}

#define YYTABLES_NAME "yytables"

#line 285 "flex_lexer.l"

/***************************
 ** Section 3: User code
 ***************************/

int yyerror(const char *msg) {
    fprintf(stderr, "[SQL-Lexer-Error] %s\n",msg); return 0;
}

#if defined(_WIN32) || defined(_WIN64)
#pragma warning(default : 4996)
#pragma warning(default : 4267)
#endif
