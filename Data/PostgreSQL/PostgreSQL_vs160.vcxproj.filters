<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\BinaryExtractor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Binder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Connector.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Extractor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PostgreSQLException.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PostgreSQLStatementImpl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PostgreSQLTypes.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SessionHandle.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SessionImpl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\StatementExecutor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Utility.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Data\PostgreSQL\BinaryExtractor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\PostgreSQL\Binder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\PostgreSQL\Connector.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\PostgreSQL\Extractor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\PostgreSQL\PostgreSQL.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\PostgreSQL\PostgreSQLException.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\PostgreSQL\PostgreSQLStatementImpl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\PostgreSQL\PostgreSQLTypes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\PostgreSQL\SessionHandle.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\PostgreSQL\SessionImpl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\PostgreSQL\StatementExecutor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Data\PostgreSQL\Utility.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\..\DLLVersion.rc" />
  </ItemGroup>
</Project>