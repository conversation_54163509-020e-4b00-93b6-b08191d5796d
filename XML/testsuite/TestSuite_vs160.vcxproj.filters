<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="XML">
      <UniqueIdentifier>{42cf6f5a-de98-4d4b-979b-272e82198b1c}</UniqueIdentifier>
    </Filter>
    <Filter Include="XML\Header Files">
      <UniqueIdentifier>{bff33679-3eff-4c3c-b6a2-cbcb62074f37}</UniqueIdentifier>
    </Filter>
    <Filter Include="XML\Source Files">
      <UniqueIdentifier>{2d77b517-0338-4881-ae86-723f495a0cc9}</UniqueIdentifier>
    </Filter>
    <Filter Include="SAX">
      <UniqueIdentifier>{bfa3b9af-23f7-4843-b423-791e4889780a}</UniqueIdentifier>
    </Filter>
    <Filter Include="SAX\Header Files">
      <UniqueIdentifier>{ca9defc9-72e7-4ed5-9413-1051ec4d7ab3}</UniqueIdentifier>
    </Filter>
    <Filter Include="SAX\Source Files">
      <UniqueIdentifier>{4e12d839-30a9-46b2-bc9d-1bab54aba219}</UniqueIdentifier>
    </Filter>
    <Filter Include="DOM">
      <UniqueIdentifier>{b7ed9015-de1d-4298-98ca-58c445373742}</UniqueIdentifier>
    </Filter>
    <Filter Include="DOM\Header Files">
      <UniqueIdentifier>{69bac11c-3eb4-48c3-8abb-a81f29e8375e}</UniqueIdentifier>
    </Filter>
    <Filter Include="DOM\Source Files">
      <UniqueIdentifier>{3f1cb8d3-fc0f-4f2e-8ca7-362a26a263d7}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite">
      <UniqueIdentifier>{0cdd946e-c95d-47ea-81a6-c87c73ef44c5}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Header Files">
      <UniqueIdentifier>{1fc7e41d-3463-4184-9153-12c659e9643b}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Source Files">
      <UniqueIdentifier>{27ef3b40-6778-47c2-a282-b54b5c816293}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver">
      <UniqueIdentifier>{8298841c-45e3-421a-b7b2-45e73cf7957d}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver\Source Files">
      <UniqueIdentifier>{30475f77-ee31-457b-9898-ffd2b3ebd4ba}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\NamePoolTest.h">
      <Filter>XML\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NameTest.h">
      <Filter>XML\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\XMLStreamParserTest.h">
      <Filter>XML\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\XMLWriterTest.h">
      <Filter>XML\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\AttributesImplTest.h">
      <Filter>SAX\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NamespaceSupportTest.h">
      <Filter>SAX\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SAXParserTest.h">
      <Filter>SAX\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SAXTestSuite.h">
      <Filter>SAX\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ChildNodesTest.h">
      <Filter>DOM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DocumentTest.h">
      <Filter>DOM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DocumentTypeTest.h">
      <Filter>DOM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DOMTestSuite.h">
      <Filter>DOM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ElementTest.h">
      <Filter>DOM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\EventTest.h">
      <Filter>DOM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NodeAppenderTest.h">
      <Filter>DOM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NodeIteratorTest.h">
      <Filter>DOM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NodeTest.h">
      <Filter>DOM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ParserWriterTest.h">
      <Filter>DOM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TextTest.h">
      <Filter>DOM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TreeWalkerTest.h">
      <Filter>DOM\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\XMLTestSuite.h">
      <Filter>_Suite\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\NamePoolTest.cpp">
      <Filter>XML\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NameTest.cpp">
      <Filter>XML\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\XMLStreamParserTest.cpp">
      <Filter>XML\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\XMLWriterTest.cpp">
      <Filter>XML\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\AttributesImplTest.cpp">
      <Filter>SAX\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NamespaceSupportTest.cpp">
      <Filter>SAX\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SAXParserTest.cpp">
      <Filter>SAX\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SAXTestSuite.cpp">
      <Filter>SAX\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ChildNodesTest.cpp">
      <Filter>DOM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DocumentTest.cpp">
      <Filter>DOM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DocumentTypeTest.cpp">
      <Filter>DOM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DOMTestSuite.cpp">
      <Filter>DOM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ElementTest.cpp">
      <Filter>DOM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\EventTest.cpp">
      <Filter>DOM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NodeAppenderTest.cpp">
      <Filter>DOM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NodeIteratorTest.cpp">
      <Filter>DOM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NodeTest.cpp">
      <Filter>DOM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ParserWriterTest.cpp">
      <Filter>DOM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TextTest.cpp">
      <Filter>DOM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TreeWalkerTest.cpp">
      <Filter>DOM\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\XMLTestSuite.cpp">
      <Filter>_Suite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Driver.cpp">
      <Filter>_Driver\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>