/*
                            __  __            _
                         ___\ \/ /_ __   __ _| |_
                        / _ \\  /| '_ \ / _` | __|
                       |  __//  \| |_) | (_| | |_
                        \___/_/\_\ .__/ \__,_|\__|
                                 |_| XML parser

   Copyright (c) 1997-2000 Thai Open Source Software Center Ltd
   Copyright (c) 2000      <PERSON> <<EMAIL>>
   Copyright (c) 2000-2004 <PERSON>, Jr. <<EMAIL>>
   Copyright (c) 2001-2002 <PERSON> <<EMAIL>>
   Copyright (c) 2002-2006 <PERSON> <<EMAIL>>
   Copyright (c) 2016      <PERSON><PERSON><PERSON> <<EMAIL>>
   Copyright (c) 2016-2019 <PERSON> <<EMAIL>>
   Copyright (c) 2017      Rhodr<PERSON> <<EMAIL>>
   Copyright (c) 2018      <PERSON><PERSON> <<EMAIL>>
   Licensed under the MIT license:

   Permission is  hereby granted,  free of charge,  to any  person obtaining
   a  copy  of  this  software   and  associated  documentation  files  (the
   "Software"),  to  deal in  the  Software  without restriction,  including
   without  limitation the  rights  to use,  copy,  modify, merge,  publish,
   distribute, sublicense, and/or sell copies of the Software, and to permit
   persons  to whom  the Software  is  furnished to  do so,  subject to  the
   following conditions:

   The above copyright  notice and this permission notice  shall be included
   in all copies or substantial portions of the Software.

   THE  SOFTWARE  IS  PROVIDED  "AS  IS",  WITHOUT  WARRANTY  OF  ANY  KIND,
   EXPRESS  OR IMPLIED,  INCLUDING  BUT  NOT LIMITED  TO  THE WARRANTIES  OF
   MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
   NO EVENT SHALL THE AUTHORS OR  COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
   DAMAGES OR  OTHER LIABILITY, WHETHER  IN AN  ACTION OF CONTRACT,  TORT OR
   OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
   USE OR OTHER DEALINGS IN THE SOFTWARE.
*/

#ifndef Expat_External_INCLUDED
#define Expat_External_INCLUDED 1

/* External API definitions */

/* Expat tries very hard to make the API boundary very specifically
   defined.  There are two macros defined to control this boundary;
   each of these can be defined before including this header to
   achieve some different behavior, but doing so it not recommended or
   tested frequently.

   XMLCALL    - The calling convention to use for all calls across the
                "library boundary."  This will default to cdecl, and
                try really hard to tell the compiler that's what we
                want.

   XMLIMPORT  - Whatever magic is needed to note that a function is
                to be imported from a dynamically loaded library
                (.dll, .so, or .sl, depending on your platform).

   The XMLCALL macro was added in Expat 1.95.7.  The only one which is
   expected to be directly useful in client code is XMLCALL.

   Note that on at least some Unix versions, the Expat library must be
   compiled with the cdecl calling convention as the default since
   system headers may assume the cdecl convention.
*/
#ifndef XMLCALL
#  if defined(_MSC_VER)
#    define XMLCALL __cdecl
#  elif defined(__GNUC__) && defined(__i386) && ! defined(__INTEL_COMPILER)
#    define XMLCALL __attribute__((cdecl))
#  else
/* For any platform which uses this definition and supports more than
   one calling convention, we need to extend this definition to
   declare the convention used on that platform, if it's possible to
   do so.

   If this is the case for your platform, please file a bug report
   with information on how to identify your platform via the C
   pre-processor and how to specify the same calling convention as the
   platform's malloc() implementation.
*/
#    define XMLCALL
#  endif
#endif /* not defined XMLCALL */

#if ! defined(XML_STATIC) && ! defined(XMLIMPORT)
#  ifndef XML_BUILDING_EXPAT
/* using Expat from an application */

#    if defined(_MSC_EXTENSIONS) && ! defined(__BEOS__) && ! defined(__CYGWIN__)
#      define XMLIMPORT __declspec(dllimport)
#    endif

#  endif
#endif /* not defined XML_STATIC */

#ifndef XML_ENABLE_VISIBILITY
#  define XML_ENABLE_VISIBILITY 0
#endif

#if ! defined(XMLIMPORT) && XML_ENABLE_VISIBILITY
#  define XMLIMPORT __attribute__((visibility("default")))
#endif

/* If we didn't define it above, define it away: */
#ifndef XMLIMPORT
#  define XMLIMPORT
#endif

#if defined(__GNUC__)                                                          \
    && (__GNUC__ > 2 || (__GNUC__ == 2 && __GNUC_MINOR__ >= 96))
#  define XML_ATTR_MALLOC __attribute__((__malloc__))
#else
#  define XML_ATTR_MALLOC
#endif

#if defined(__GNUC__)                                                          \
    && ((__GNUC__ > 4) || (__GNUC__ == 4 && __GNUC_MINOR__ >= 3))
#  define XML_ATTR_ALLOC_SIZE(x) __attribute__((__alloc_size__(x)))
#else
#  define XML_ATTR_ALLOC_SIZE(x)
#endif

#define XMLPARSEAPI(type) XMLIMPORT type XMLCALL

#ifdef __cplusplus
extern "C" {
#endif

#ifdef XML_UNICODE_WCHAR_T
#  ifndef XML_UNICODE
#    define XML_UNICODE
#  endif
#  if defined(__SIZEOF_WCHAR_T__) && (__SIZEOF_WCHAR_T__ != 2)
#    error "sizeof(wchar_t) != 2; Need -fshort-wchar for both Expat and libc"
#  endif
#endif

#ifdef XML_UNICODE /* Information is UTF-16 encoded. */
#  ifdef XML_UNICODE_WCHAR_T
typedef wchar_t XML_Char;
typedef wchar_t XML_LChar;
#  else
typedef unsigned short XML_Char;
typedef char XML_LChar;
#  endif /* XML_UNICODE_WCHAR_T */
#else    /* Information is UTF-8 encoded. */
typedef char XML_Char;
typedef char XML_LChar;
#endif   /* XML_UNICODE */

#ifdef XML_LARGE_SIZE /* Use large integers for file/stream positions. */
typedef long long XML_Index;
typedef unsigned long long XML_Size;
#else
typedef long XML_Index;
typedef unsigned long XML_Size;
#endif /* XML_LARGE_SIZE */

#ifdef __cplusplus
}
#endif

#endif /* not Expat_External_INCLUDED */
