<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="XML"
	ProjectGUID="{9E211743-85FE-4977-82F3-4F04B40C912D}"
	RootNamespace="XML"
	Keyword="Win32Proj"
	TargetFrameworkVersion="0"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="debug_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_USRDLL;XML_STATIC;XML_NS;XML_DTD;HAVE_EXPAT_CONFIG_H;XML_EXPORTS"
				StringPooling="true"
				MinimalRebuild="false"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies=""
				OutputFile="..\bin\PocoXMLd.dll"
				LinkIncremental="2"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\lib"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="..\bin\PocoXMLd.pdb"
				SubSystem="1"
				ImportLibrary="..\lib\PocoXMLd.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_USRDLL;XML_STATIC;XML_NS;XML_DTD;HAVE_EXPAT_CONFIG_H;XML_EXPORTS"
				StringPooling="true"
				MinimalRebuild="false"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies=""
				OutputFile="..\bin\PocoXML.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				ImportLibrary="..\lib\PocoXML.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;XML_STATIC;XML_NS;XML_DTD;HAVE_EXPAT_CONFIG_H"
				StringPooling="true"
				MinimalRebuild="false"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\lib\PocoXMLMTd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoXMLMTd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;XML_STATIC;XML_NS;XML_DTD;HAVE_EXPAT_CONFIG_H"
				StringPooling="true"
				MinimalRebuild="false"
				RuntimeLibrary="0"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoXMLMT.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;XML_STATIC;XML_NS;XML_DTD;HAVE_EXPAT_CONFIG_H"
				StringPooling="true"
				MinimalRebuild="false"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\lib\PocoXMLMDd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoXMLMDd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;XML_STATIC;XML_NS;XML_DTD;HAVE_EXPAT_CONFIG_H"
				StringPooling="true"
				MinimalRebuild="false"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoXMLMD.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="XML"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\include\Poco\XML\Content.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\XML\Name.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Xml\NamePool.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Xml\NamespaceStrategy.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Xml\ParserEngine.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\XML\QName.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\XML\ValueTraits.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Xml\XML.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Xml\XMLException.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Xml\XMLStream.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\XML\XMLStreamParser.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\XML\XMLStreamParserException.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Xml\XMLString.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Xml\XMLWriter.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\Name.cpp"
					>
				</File>
				<File
					RelativePath=".\src\NamePool.cpp"
					>
				</File>
				<File
					RelativePath=".\src\NamespaceStrategy.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ParserEngine.cpp"
					>
				</File>
				<File
					RelativePath=".\src\QName.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ValueTraits.cpp"
					>
				</File>
				<File
					RelativePath=".\src\XMLException.cpp"
					>
				</File>
				<File
					RelativePath=".\src\XMLStreamParser.cpp"
					>
				</File>
				<File
					RelativePath=".\src\XMLStreamParserException.cpp"
					>
				</File>
				<File
					RelativePath=".\src\XMLString.cpp"
					>
					<FileConfiguration
						Name="debug_shared|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="release_shared|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="debug_static_mt|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="release_static_mt|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="debug_static_md|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="release_static_md|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath=".\src\XMLWriter.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="SAX"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\include\Poco\SAX\Attributes.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\SAX\AttributesImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\SAX\ContentHandler.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\SAX\DeclHandler.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\SAX\DefaultHandler.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\SAX\DTDHandler.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\SAX\EntityResolver.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Sax\EntityResolverImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\SAX\ErrorHandler.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\SAX\InputSource.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\SAX\LexicalHandler.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\SAX\Locator.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Sax\LocatorImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\SAX\NamespaceSupport.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\SAX\SAXException.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Sax\SAXParser.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Sax\WhitespaceFilter.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Sax\XMLFilter.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Sax\XMLFilterImpl.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\SAX\XMLReader.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\Attributes.cpp"
					>
				</File>
				<File
					RelativePath=".\src\AttributesImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ContentHandler.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DeclHandler.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DefaultHandler.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DTDHandler.cpp"
					>
				</File>
				<File
					RelativePath=".\src\EntityResolver.cpp"
					>
				</File>
				<File
					RelativePath=".\src\EntityResolverImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ErrorHandler.cpp"
					>
				</File>
				<File
					RelativePath=".\src\InputSource.cpp"
					>
				</File>
				<File
					RelativePath=".\src\LexicalHandler.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Locator.cpp"
					>
				</File>
				<File
					RelativePath=".\src\LocatorImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\NamespaceSupport.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SAXException.cpp"
					>
				</File>
				<File
					RelativePath=".\src\SAXParser.cpp"
					>
				</File>
				<File
					RelativePath=".\src\WhitespaceFilter.cpp"
					>
				</File>
				<File
					RelativePath=".\src\XMLFilter.cpp"
					>
				</File>
				<File
					RelativePath=".\src\XMLFilterImpl.cpp"
					>
				</File>
				<File
					RelativePath=".\src\XMLReader.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="DOM"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\include\Poco\Dom\AbstractContainerNode.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Dom\AbstractNode.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\Attr.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Dom\AttrMap.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Dom\AutoPtr.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\CDATASection.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\CharacterData.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Dom\ChildNodesList.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\Comment.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\Document.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\DocumentEvent.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\DocumentFragment.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\DocumentType.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Dom\DOMBuilder.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\DOMException.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\DOMImplementation.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\DOMObject.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Dom\DOMParser.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Dom\DOMSerializer.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\DOMWriter.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Dom\DTDMap.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\Element.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Dom\ElementsByTagNameList.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\Entity.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\EntityReference.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\Event.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Dom\EventDispatcher.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\EventException.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\EventListener.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\EventTarget.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\MutationEvent.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\NamedNodeMap.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\Node.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\NodeAppender.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Dom\NodeFilter.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Dom\NodeIterator.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\NodeList.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\Notation.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\ProcessingInstruction.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\DOM\Text.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Dom\TreeWalker.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\AbstractContainerNode.cpp"
					>
				</File>
				<File
					RelativePath=".\src\AbstractNode.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Attr.cpp"
					>
				</File>
				<File
					RelativePath=".\src\AttrMap.cpp"
					>
				</File>
				<File
					RelativePath=".\src\CDATASection.cpp"
					>
				</File>
				<File
					RelativePath=".\src\CharacterData.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ChildNodesList.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Comment.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Document.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DocumentEvent.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DocumentFragment.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DocumentType.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DOMBuilder.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DOMException.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DOMImplementation.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DOMObject.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DOMParser.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DOMSerializer.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DOMWriter.cpp"
					>
				</File>
				<File
					RelativePath=".\src\DTDMap.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Element.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ElementsByTagNameList.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Entity.cpp"
					>
				</File>
				<File
					RelativePath=".\src\EntityReference.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Event.cpp"
					>
				</File>
				<File
					RelativePath=".\src\EventDispatcher.cpp"
					>
				</File>
				<File
					RelativePath=".\src\EventException.cpp"
					>
				</File>
				<File
					RelativePath=".\src\EventListener.cpp"
					>
				</File>
				<File
					RelativePath=".\src\EventTarget.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MutationEvent.cpp"
					>
				</File>
				<File
					RelativePath=".\src\NamedNodeMap.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Node.cpp"
					>
				</File>
				<File
					RelativePath=".\src\NodeAppender.cpp"
					>
				</File>
				<File
					RelativePath=".\src\NodeFilter.cpp"
					>
				</File>
				<File
					RelativePath=".\src\NodeIterator.cpp"
					>
				</File>
				<File
					RelativePath=".\src\NodeList.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Notation.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ProcessingInstruction.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Text.cpp"
					>
				</File>
				<File
					RelativePath=".\src\TreeWalker.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Expat"
			>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\ascii.h"
					>
				</File>
				<File
					RelativePath=".\src\asciitab.h"
					>
				</File>
				<File
					RelativePath=".\src\expat.h"
					>
				</File>
				<File
					RelativePath=".\src\expat_config.h"
					>
				</File>
				<File
					RelativePath=".\src\expat_external.h"
					>
				</File>
				<File
					RelativePath=".\src\iasciitab.h"
					>
				</File>
				<File
					RelativePath=".\src\internal.h"
					>
				</File>
				<File
					RelativePath=".\src\latin1tab.h"
					>
				</File>
				<File
					RelativePath=".\src\nametab.h"
					>
				</File>
				<File
					RelativePath=".\src\utf8tab.h"
					>
				</File>
				<File
					RelativePath=".\src\xmlparse.cpp"
					>
				</File>
				<File
					RelativePath=".\src\xmlrole.c"
					>
				</File>
				<File
					RelativePath=".\src\xmlrole.h"
					>
				</File>
				<File
					RelativePath=".\src\xmltok.c"
					>
				</File>
				<File
					RelativePath=".\src\xmltok.h"
					>
				</File>
				<File
					RelativePath=".\src\xmltok_impl.c"
					>
					<FileConfiguration
						Name="debug_shared|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="release_shared|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="debug_static_mt|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="release_static_mt|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="debug_static_md|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="release_static_md|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath=".\src\xmltok_impl.h"
					>
				</File>
				<File
					RelativePath=".\src\xmltok_ns.c"
					>
					<FileConfiguration
						Name="debug_shared|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="release_shared|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="debug_static_mt|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="release_static_mt|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="debug_static_md|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="release_static_md|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
			</Filter>
		</Filter>
		<File
			RelativePath="..\DLLVersion.rc"
			>
			<FileConfiguration
				Name="debug_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="debug_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
