#
# WatchOS
#
# Build settings for Apple WatchOS 2.0 and newer
#

#
# General Settings
#
# WATCHOS does not allow dynamic linking to user libraries
#
LINKMODE ?= STATIC

#
# If the SDK is defined use it
# Otherwise find the latest version installed
#
# WATCHOS_SDK_VERSION = 2.0

# if WATCHOS_SDK_VERSION_MIN is defined use that
# Otherwise use the version found.

WATCHOS_SDK             ?= WatchOS
WATCHOS_SDK_ROOT        ?= $(shell xcode-select -print-path)/Platforms/$(WATCHOS_SDK).platform/Developer/SDKs
WATCHOS_SDK_ROOT_DIR     = $(WATCHOS_SDK_ROOT)/$(WATCHOS_SDK)
WATCHOS_SDK_BASE         = $(shell ls -d $(WATCHOS_SDK_ROOT_DIR)$(WATCHOS_SDK_VERSION)*.sdk | tail -1)
WATCHOS_SDK_VERSION_MIN ?= $(patsubst %.sdk,%,$(patsubst $(WATCHOS_SDK_ROOT_DIR)%,%,$(WATCHOS_SDK_BASE)))

POCO_TARGET_OSNAME     ?= $(WATCHOS_SDK)
POCO_TARGET_OSARCH     ?= armv7k
TOOL_PREFIX            ?= $(shell xcode-select -print-path)/Platforms/$(WATCHOS_SDK).platform/Developer/usr/bin
OSFLAGS                ?= -arch $(POCO_TARGET_OSARCH) -isysroot $(WATCHOS_SDK_BASE) -mwatchos-version-min=$(WATCHOS_SDK_VERSION_MIN) -fembed-bitcode

#
# Tools
#
# If GCC_VER is defined then use it.
# Otherwise select the latest version
#

CC		= $(shell xcrun -find clang)
CXX		= $(shell xcrun -find clang++)

LINK    = $(CXX) -bind_at_load
LIB     = libtool -static -o
RANLIB  = ranlib
SHLIB   = $(CXX) $(OSFLAGS) -dynamiclib -Wl,-install_name,$@ -o $@
DYLIB   = $(CXX) $(OSFLAGS) -dynamic -bundle -read_only_relocs suppress -Wl,-bind_at_load -o $@
SHLIBLN = $(POCO_BASE)/build/script/shlibln
STRIP   =
DEP     = $(POCO_BASE)/build/script/makedepend.gcc
SHELL   = sh
RM      = rm -rf
CP      = cp
MKDIR   = mkdir -p

#
# Extension for Shared Libraries
#
SHAREDLIBEXT     = .$(target_version).dylib
SHAREDLIBLINKEXT = .dylib

#
# Compiler and Linker Flags
#
CFLAGS          = $(OSFLAGS)
CFLAGS32        =
CFLAGS64        =
CXXFLAGS        = $(OSFLAGS) -std=c++17 -stdlib=libc++ -Wall -Wno-sign-compare
CXXFLAGS32      =
CXXFLAGS64      =
LINKFLAGS       = $(OSFLAGS) -stdlib=libc++
LINKFLAGS32     =
LINKFLAGS64     =
STATICOPT_CC    =
STATICOPT_CXX   =
STATICOPT_LINK  =
SHAREDOPT_CC    = -fPIC
SHAREDOPT_CXX   = -fPIC
SHAREDOPT_LINK  =
DEBUGOPT_CC     = -g -D_DEBUG=$(DEBUGLEVEL)
DEBUGOPT_CXX    = -g -D_DEBUG=$(DEBUGLEVEL)
DEBUGOPT_LINK   =
RELEASEOPT_CC   = -DNDEBUG -O2
RELEASEOPT_CXX  = -DNDEBUG -O
RELEASEOPT_LINK =

#
# System Specific Flags
#
SYSFLAGS = -DPOCO_HAVE_IPv6 -DPOCO_SOCKETADDRESS_DONT_PREFER_IPV4 -DPOCO_NO_FPENVIRONMENT -DPOCO_NO_STAT64 -DPOCO_NO_SHAREDLIBS -DPOCO_NO_NET_IFTYPES -DPOCO_NO_FORK_EXEC

#
# System Specific Libraries
#
SYSLIBS  = -ldl
