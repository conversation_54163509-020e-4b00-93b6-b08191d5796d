<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="SSLCore">
      <UniqueIdentifier>{0d24d8ec-9d08-4661-a5b7-8b8dcac36f31}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLCore\Header Files">
      <UniqueIdentifier>{c899ad3f-59dc-4a9c-a0de-3530aee706b3}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLCore\Source Files">
      <UniqueIdentifier>{da0eef86-235c-40aa-8468-d9f8eaa2df68}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient">
      <UniqueIdentifier>{2c65c68a-c3c4-4eb3-88e9-10098dd74732}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Header Files">
      <UniqueIdentifier>{cc692a9a-fed4-45d0-b2e7-d4f32d1e74a7}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Source Files">
      <UniqueIdentifier>{4a9b97c9-d5cf-4a7c-b36a-e98acc60f1b2}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLSockets">
      <UniqueIdentifier>{b1bc4f50-9b88-4e22-955d-1d46e48d70e8}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLSockets\Header Files">
      <UniqueIdentifier>{f23358d3-2793-4148-920a-bbd174182332}</UniqueIdentifier>
    </Filter>
    <Filter Include="SSLSockets\Source Files">
      <UniqueIdentifier>{4870f3dc-3293-4864-a62d-de07f3a59536}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail">
      <UniqueIdentifier>{775ea9ae-39c6-43ea-8413-65be9b380dee}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Header Files">
      <UniqueIdentifier>{8669f9c5-5a2b-424f-a66d-e32cb71db8e2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Source Files">
      <UniqueIdentifier>{63524b7c-2710-42cf-a572-cd8b767adc3a}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Net\AcceptCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\AutoSecBufferDesc.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\CertificateHandlerFactory.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\CertificateHandlerFactoryMgr.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\ConsoleCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Context.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\InvalidCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\KeyConsoleHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\KeyFileHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\NetSSL.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PrivateKeyFactory.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PrivateKeyFactoryMgr.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\PrivateKeyPassphraseHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\RejectCertificateHandler.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Session.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SSLException.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SSLManager.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\Utility.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\VerificationErrorArgs.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\X509Certificate.h">
      <Filter>SSLCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSClientSession.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSSessionInstantiator.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\HTTPSStreamFactory.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureServerSocket.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureServerSocketImpl.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureSocketImpl.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureStreamSocket.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureStreamSocketImpl.h">
      <Filter>SSLSockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Net\SecureSMTPClientSession.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\AcceptCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CertificateHandlerFactory.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\CertificateHandlerFactoryMgr.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ConsoleCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Context.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\InvalidCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KeyConsoleHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\KeyFileHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PrivateKeyFactory.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PrivateKeyFactoryMgr.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PrivateKeyPassphraseHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RejectCertificateHandler.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Session.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SSLException.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SSLManager.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Utility.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\VerificationErrorArgs.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\X509Certificate.cpp">
      <Filter>SSLCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSClientSession.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSSessionInstantiator.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSStreamFactory.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureServerSocket.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureServerSocketImpl.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureSocketImpl.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocket.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocketImpl.cpp">
      <Filter>SSLSockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureSMTPClientSession.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>