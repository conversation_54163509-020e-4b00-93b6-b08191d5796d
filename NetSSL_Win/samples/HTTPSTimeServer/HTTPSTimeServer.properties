# This is a sample configuration file for HTTPSTimeServer

HTTPSTimeServer.format = %W, %e %b %y %H:%M:%S %Z
HTTPSTimeServer.port   = 9443

schannel.server.certificatePath = ${application.configDir}any.pfx
schannel.server.privateKeyPassphraseHandler.name = KeyFileHandler
schannel.server.privateKeyPassphraseHandler.options.password = secret
schannel.server.verificationMode = none
schannel.server.useMachineStore = false
schannel.server.useStrongCrypto = true

logging.loggers.root.channel.class = ConsoleChannel
logging.loggers.app.name = Application
logging.loggers.app.channel = c1
logging.formatters.f1.class = PatternFormatter
logging.formatters.f1.pattern = [%p] %t
logging.channels.c1.class = ConsoleChannel
logging.channels.c1.formatter = f1
