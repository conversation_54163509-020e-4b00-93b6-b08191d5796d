<AppConfig>
	<schannel>
		<server>
			<certificatePath>any.pfx</certificatePath>
			<verificationMode>none</verificationMode>
			<useMachineStore>false</useMachineStore>
			<privateKeyPassphraseHandler>
				<name>KeyFileHandler</name>
				<options>
					<password>secret</password>
				</options>
			</privateKeyPassphraseHandler>
			<invalidCertificateHandler>
				<name>AcceptCertificateHandler</name>
				<options>
				</options>
			</invalidCertificateHandler>
		</server>
		<client>
			<verificationMode>relaxed</verificationMode>
			<useMachineStore>false</useMachineStore>
			<privateKeyPassphraseHandler>
				<name>KeyFileHandler</name>
				<options>
					<password>secret</password>
				</options>
			</privateKeyPassphraseHandler>
			<invalidCertificateHandler>
				<name>AcceptCertificateHandler</name>
				<options>
				</options>
			</invalidCertificateHandler>
		</client>
	</schannel>
	<testsuite>
		<proxy>
			<host>proxy.aon.at</host>
			<port>8080</port>
		</proxy>
	</testsuite>
</AppConfig>
