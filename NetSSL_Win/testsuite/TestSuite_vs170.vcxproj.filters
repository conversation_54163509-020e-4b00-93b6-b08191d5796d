<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="HTTPS">
      <UniqueIdentifier>{d25a7448-ef98-42ed-b0b9-40f8e5dcc011}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPS\Header Files">
      <UniqueIdentifier>{02849cdc-34aa-403d-a0fd-f36f26cc9551}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPS\Source Files">
      <UniqueIdentifier>{76a288f1-1bed-44b7-a7df-e1e1cc1a622a}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite">
      <UniqueIdentifier>{b075c76f-7948-40f0-8f37-7ff6e2ab1ee7}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Header Files">
      <UniqueIdentifier>{8625eb47-f429-4bfb-98e5-2d96e2c95619}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Source Files">
      <UniqueIdentifier>{d3860ac7-18fa-47a8-917e-4085fb90b0a4}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver">
      <UniqueIdentifier>{dfaac5f7-7cbc-4f3d-a730-087c01c9676e}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver\Source Files">
      <UniqueIdentifier>{3220210c-be32-4b59-899e-cf44423e2425}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer">
      <UniqueIdentifier>{602e92f6-db01-4821-8141-66b4b2e378f1}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Header Files">
      <UniqueIdentifier>{7bc9bbe2-0162-4144-9c08-9b2af574a4e3}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Source Files">
      <UniqueIdentifier>{1bd53155-2233-462c-89d1-bae457e19b4b}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSServer">
      <UniqueIdentifier>{98dd8a86-c336-48e9-b1a1-68a6b2d2652a}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSServer\Header Files">
      <UniqueIdentifier>{166274eb-db2f-4099-a181-185457e0582f}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSServer\Source Files">
      <UniqueIdentifier>{8512b8d8-bcae-4c80-b094-3f3db7dcbf2d}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient">
      <UniqueIdentifier>{c033670a-e73b-42f1-8736-2592b1c09bfc}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Header Files">
      <UniqueIdentifier>{4c15eef1-75ea-460a-a71f-034f17c03010}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPSClient\Source Files">
      <UniqueIdentifier>{90c43e80-1ba2-4e5c-9054-2796df1113e4}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket">
      <UniqueIdentifier>{04bc257c-ada9-4922-b5ef-22e505d8212f}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Header Files">
      <UniqueIdentifier>{79d4cac4-4127-40ba-b039-eff4bf4e473d}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Source Files">
      <UniqueIdentifier>{706074a0-340b-49d0-a058-3543522ef6ab}</UniqueIdentifier>
    </Filter>
    <Filter Include="SecureSocket">
      <UniqueIdentifier>{010f9272-1b8e-46f1-8cae-4649e0459133}</UniqueIdentifier>
    </Filter>
    <Filter Include="SecureSocket\Header Files">
      <UniqueIdentifier>{8c5d49dd-dd83-49e0-af18-93b1b7e1b135}</UniqueIdentifier>
    </Filter>
    <Filter Include="SecureSocket\Source Files">
      <UniqueIdentifier>{5912c872-6357-4db0-af6b-c4c03591f3ad}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\HTTPSTestServer.h">
      <Filter>HTTPS\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NetSSLTestSuite.h">
      <Filter>_Suite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TCPServerTest.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TCPServerTestSuite.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSServerTest.h">
      <Filter>HTTPSServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSServerTestSuite.h">
      <Filter>HTTPSServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSClientSessionTest.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSClientTestSuite.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPSStreamFactoryTest.h">
      <Filter>HTTPSClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SecureStreamSocketTest.h">
      <Filter>SecureSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SecureStreamSocketTestSuite.h">
      <Filter>SecureSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\WebSocketTest.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\WebSocketTestSuite.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\HTTPSTestServer.cpp">
      <Filter>HTTPS\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NetSSLTestSuite.cpp">
      <Filter>_Suite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Driver.cpp">
      <Filter>_Driver\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerTest.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerTestSuite.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSServerTest.cpp">
      <Filter>HTTPSServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSServerTestSuite.cpp">
      <Filter>HTTPSServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSClientSessionTest.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSClientTestSuite.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPSStreamFactoryTest.cpp">
      <Filter>HTTPSClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocketTest.cpp">
      <Filter>SecureSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SecureStreamSocketTestSuite.cpp">
      <Filter>SecureSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WebSocketTest.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WebSocketTestSuite.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>