/*************************************************
*      Perl-Compatible Regular Expressions       *
*************************************************/

/* PCRE is a library of functions to support regular expressions whose syntax
and semantics are as close as possible to those of the Perl 5 language.

                       Written by <PERSON>
     Original API code Copyright (c) 1997-2012 University of Cambridge
          New API code Copyright (c) 2016-2022 University of Cambridge

This module is auto-generated from Unicode data files. DO NOT EDIT MANUALLY!
Instead, modify the maint/GenerateUcd.py script and run it to generate
a new version of this code.

-----------------------------------------------------------------------------
Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

    * Redistributions of source code must retain the above copyright notice,
      this list of conditions and the following disclaimer.

    * Redistributions in binary form must reproduce the above copyright
      notice, this list of conditions and the following disclaimer in the
      documentation and/or other materials provided with the distribution.

    * Neither the name of the University of Cambridge nor the names of its
      contributors may be used to endorse or promote products derived from
      this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
-----------------------------------------------------------------------------
*/

/* This file contains tables of Unicode properties that are extracted from
Unicode data files. See the comments at the start of maint/GenerateUcd.py for
details.

As well as being part of the PCRE2 library, this file is #included by the
pcre2test program, which redefines the PRIV macro to change table names from
_pcre2_xxx to xxxx, thereby avoiding name clashes with the library. At present,
just one of these tables is actually needed. When compiling the library, some
headers are needed. */

#ifndef PCRE2_PCRE2TEST
#include "pcre2_config.h"
#include "pcre2_internal.h"
#endif /* PCRE2_PCRE2TEST */

/* The tables herein are needed only when UCP support is built, and in PCRE2
that happens automatically with UTF support. This module should not be
referenced otherwise, so it should not matter whether it is compiled or not.
However a comment was received about space saving - maybe the guy linked all
the modules rather than using a library - so we include a condition to cut out
the tables when not needed. But don't leave a totally empty module because some
compilers barf at that. Instead, just supply some small dummy tables. */

#ifndef SUPPORT_UNICODE
const ucd_record PRIV(ucd_records)[] = {{0,0,0,0,0,0,0}};
const uint16_t PRIV(ucd_stage1)[] = {0};
const uint16_t PRIV(ucd_stage2)[] = {0};
const uint32_t PRIV(ucd_caseless_sets)[] = {0};
#else

/* Total size: 112564 bytes, block size: 128. */

const char *PRIV(unicode_version) = "15.0.0";

/* When recompiling tables with a new Unicode version, please check the types
in this structure definition with those in pcre2_internal.h (the actual field
names will be different).

typedef struct {
uint8_t property_0;
uint8_t property_1;
uint8_t property_2;
uint8_t property_3;
int32_t property_4;
uint16_t property_5;
uint16_t property_6;
} ucd_record;
*/

/* If the 32-bit library is run in non-32-bit mode, character values greater
than 0x10ffff may be encountered. For these we set up a special record. */

#if PCRE2_CODE_UNIT_WIDTH == 32
const ucd_record PRIV(dummy_ucd_record)[] = {{
  ucp_Unknown,    /* script */
  ucp_Cn,         /* type unassigned */
  ucp_gbOther,    /* grapheme break property */
  0,              /* case set */
  0,              /* other case */
  0 | (ucp_bidiL << UCD_BIDICLASS_SHIFT), /* script extension and bidi class */
  0,              /* bool properties offset */
  }};
#endif

/* This table contains lists of characters that are caseless sets of
more than one character. Each list is terminated by NOTACHAR. */

const uint32_t PRIV(ucd_caseless_sets)[] = {
  NOTACHAR,
  0x0053,  0x0073,  0x017f,  NOTACHAR,
  0x01c4,  0x01c5,  0x01c6,  NOTACHAR,
  0x01c7,  0x01c8,  0x01c9,  NOTACHAR,
  0x01ca,  0x01cb,  0x01cc,  NOTACHAR,
  0x01f1,  0x01f2,  0x01f3,  NOTACHAR,
  0x0345,  0x0399,  0x03b9,  0x1fbe,  NOTACHAR,
  0x00b5,  0x039c,  0x03bc,  NOTACHAR,
  0x03a3,  0x03c2,  0x03c3,  NOTACHAR,
  0x0392,  0x03b2,  0x03d0,  NOTACHAR,
  0x0398,  0x03b8,  0x03d1,  0x03f4,  NOTACHAR,
  0x03a6,  0x03c6,  0x03d5,  NOTACHAR,
  0x03a0,  0x03c0,  0x03d6,  NOTACHAR,
  0x039a,  0x03ba,  0x03f0,  NOTACHAR,
  0x03a1,  0x03c1,  0x03f1,  NOTACHAR,
  0x0395,  0x03b5,  0x03f5,  NOTACHAR,
  0x0412,  0x0432,  0x1c80,  NOTACHAR,
  0x0414,  0x0434,  0x1c81,  NOTACHAR,
  0x041e,  0x043e,  0x1c82,  NOTACHAR,
  0x0421,  0x0441,  0x1c83,  NOTACHAR,
  0x0422,  0x0442,  0x1c84,  0x1c85,  NOTACHAR,
  0x042a,  0x044a,  0x1c86,  NOTACHAR,
  0x0462,  0x0463,  0x1c87,  NOTACHAR,
  0x1e60,  0x1e61,  0x1e9b,  NOTACHAR,
  0x03a9,  0x03c9,  0x2126,  NOTACHAR,
  0x004b,  0x006b,  0x212a,  NOTACHAR,
  0x00c5,  0x00e5,  0x212b,  NOTACHAR,
  0x1c88,  0xa64a,  0xa64b,  NOTACHAR,
};

/* When #included in pcre2test, we don't need the table of digit sets, nor the
the large main UCD tables. */

#ifndef PCRE2_PCRE2TEST

/* This table lists the code points for the '9' characters in each set of
decimal digits. It is used to ensure that all the digits in a script run come
from the same set. */

const uint32_t PRIV(ucd_digit_sets)[] = {
  68,  /* Number of subsequent values */
  0x00039, 0x00669, 0x006f9, 0x007c9, 0x0096f, 0x009ef, 0x00a6f, 0x00aef,
  0x00b6f, 0x00bef, 0x00c6f, 0x00cef, 0x00d6f, 0x00def, 0x00e59, 0x00ed9,
  0x00f29, 0x01049, 0x01099, 0x017e9, 0x01819, 0x0194f, 0x019d9, 0x01a89,
  0x01a99, 0x01b59, 0x01bb9, 0x01c49, 0x01c59, 0x0a629, 0x0a8d9, 0x0a909,
  0x0a9d9, 0x0a9f9, 0x0aa59, 0x0abf9, 0x0ff19, 0x104a9, 0x10d39, 0x1106f,
  0x110f9, 0x1113f, 0x111d9, 0x112f9, 0x11459, 0x114d9, 0x11659, 0x116c9,
  0x11739, 0x118e9, 0x11959, 0x11c59, 0x11d59, 0x11da9, 0x11f59, 0x16a69,
  0x16ac9, 0x16b59, 0x1d7d7, 0x1d7e1, 0x1d7eb, 0x1d7f5, 0x1d7ff, 0x1e149,
  0x1e2f9, 0x1e4f9, 0x1e959, 0x1fbf9,
};

/* This vector is a list of script bitsets for the Script Extension property.
The number of 32-bit words in each bitset is #defined in pcre2_ucp.h as
ucd_script_sets_item_size. */

const uint32_t PRIV(ucd_script_sets)[] = {
 0x00000000u, 0x00000000u, 0x00000000u,
 0x00000080u, 0x00000000u, 0x00000000u,
 0x00000040u, 0x00000000u, 0x00000000u,
 0x00000000u, 0x00004000u, 0x00000000u,
 0x00000002u, 0x00000000u, 0x00000000u,
 0x00800000u, 0x00000000u, 0x00000000u,
 0x00000001u, 0x00000000u, 0x00000000u,
 0x00000000u, 0x00000000u, 0x00000001u,
 0x00000010u, 0x00000000u, 0x00000000u,
 0x00000008u, 0x00000004u, 0x00000000u,
 0x00000008u, 0x40000000u, 0x00000000u,
 0x00000008u, 0x00000040u, 0x00000000u,
 0x00000018u, 0x00000000u, 0x00000000u,
 0x00000028u, 0x00000000u, 0x00000000u,
 0x000000c0u, 0x00000000u, 0x00000000u,
 0x00c00000u, 0x00000000u, 0x00000000u,
 0x00000000u, 0x00000102u, 0x00000000u,
 0x80000000u, 0x00000001u, 0x00000000u,
 0x00000004u, 0x00000008u, 0x00000000u,
 0x00000005u, 0x00000000u, 0x00000000u,
 0x00000004u, 0x00200000u, 0x00000000u,
 0x00000014u, 0x00000000u, 0x00000000u,
 0x00000040u, 0x00008000u, 0x00000000u,
 0x00000040u, 0x00000000u, 0x00000001u,
 0x00000040u, 0x00001000u, 0x00000000u,
 0x00000840u, 0x00000000u, 0x00000000u,
 0x00020001u, 0x00000000u, 0x00000000u,
 0x00000800u, 0x00008000u, 0x00000000u,
 0x00000200u, 0x00010000u, 0x00000000u,
 0x00000100u, 0x02000000u, 0x00000000u,
 0x00800001u, 0x00000000u, 0x00000000u,
 0x00300000u, 0x00000000u, 0x00000000u,
 0x00002000u, 0x00000000u, 0x00000001u,
 0x00080001u, 0x00000000u, 0x00000000u,
 0x00000000u, 0x00080000u, 0x00000008u,
 0x00080000u, 0x00000020u, 0x00000000u,
 0x00000038u, 0x00000000u, 0x00000000u,
 0x00000028u, 0x00000000u, 0x00000002u,
 0x00000080u, 0x00000810u, 0x00000000u,
 0x40010000u, 0x00000800u, 0x00000000u,
 0x80000000u, 0x00000001u, 0x00000004u,
 0x80000000u, 0x00020001u, 0x00000000u,
 0x00002040u, 0x00008000u, 0x00000000u,
 0x00000041u, 0x00008000u, 0x00000000u,
 0x00b00000u, 0x00000000u, 0x00000000u,
 0x00010001u, 0x00000080u, 0x00000000u,
 0x000020c0u, 0x00008000u, 0x00000000u,
 0x1e000000u, 0x00000000u, 0x00000000u,
 0x00000040u, 0x10040200u, 0x00000000u,
 0x00f40000u, 0x00000000u, 0x00000000u,
 0x00000038u, 0x40000040u, 0x00000002u,
 0x01f40000u, 0x00000000u, 0x00000000u,
 0x00007c40u, 0x00000000u, 0x00000000u,
 0x00000038u, 0x44000040u, 0x00000002u,
 0x000034c0u, 0x01008000u, 0x00000001u,
 0x00000018u, 0xc4480400u, 0x00000008u,
 0x00000340u, 0x11952200u, 0x00000000u,
 0x00007fc1u, 0x01008000u, 0x00000000u,
 0x00007fc1u, 0x01009000u, 0x00000000u,
 0x00002340u, 0x11952200u, 0x00000001u,
 0x00006340u, 0x11952200u, 0x00000001u,
 0x0000ffc0u, 0x3984a010u, 0x00000001u,
 0x2000ffc0u, 0x3984a010u, 0x00000001u,
};

/* This vector is a list of bitsets for Boolean properties. The number of
32_bit words in each bitset is #defined as ucd_boolprop_sets_item_size in
pcre2_ucp.h. */

const uint32_t PRIV(ucd_boolprop_sets)[] = {
 0x00000000u, 0x00000000u,
 0x00000001u, 0x00000000u,
 0x00000001u, 0x00020040u,
 0x00800001u, 0x00020040u,
 0x00800001u, 0x00002820u,
 0x00800001u, 0x00000120u,
 0x00830001u, 0x00000020u,
 0x00800001u, 0x00000020u,
 0x00800021u, 0x00000120u,
 0x00800011u, 0x00000020u,
 0x00800001u, 0x00000028u,
 0x00800001u, 0x00002020u,
 0x00801001u, 0x00000020u,
 0x00800021u, 0x00002820u,
 0x24830003u, 0x00040000u,
 0x00800021u, 0x00002020u,
 0x00800011u, 0x00000028u,
 0x648003c7u, 0x000c8000u,
 0x608003c5u, 0x000c8000u,
 0x00808021u, 0x00000028u,
 0x20800001u, 0x00040000u,
 0x00808021u, 0x00000020u,
 0x64800d47u, 0x000c0004u,
 0x60800d45u, 0x000c0004u,
 0x60800d45u, 0x000c1004u,
 0x00000000u, 0x00020040u,
 0x00800000u, 0x00020000u,
 0x00800000u, 0x00000020u,
 0x00808020u, 0x00000000u,
 0x00a10000u, 0x00000020u,
 0x60800044u, 0x000c0004u,
 0x00800010u, 0x00000120u,
 0x00800000u, 0x00000028u,
 0x00002020u, 0x00000000u,
 0x00800000u, 0x00000000u,
 0x60800dc4u, 0x000c0004u,
 0x20c08020u, 0x00040000u,
 0x608003c4u, 0x000c8000u,
 0x60800d44u, 0x000c0004u,
 0x60800d44u, 0x000c1004u,
 0x60804dc4u, 0x000c0004u,
 0x60800004u, 0x000c0000u,
 0x608007c4u, 0x000c8000u,
 0x60800bc4u, 0x000c0000u,
 0x60808064u, 0x000c0004u,
 0x60808064u, 0x000c1004u,
 0x60808024u, 0x000c0000u,
 0x60c08024u, 0x000c0000u,
 0x21008020u, 0x00040000u,
 0x21008de4u, 0x00040004u,
 0x21002020u, 0x00040000u,
 0x21000020u, 0x00040000u,
 0x60808064u, 0x00000004u,
 0x00800000u, 0x00002000u,
 0x20800020u, 0x00042000u,
 0x60800dc4u, 0x000c000cu,
 0x60800044u, 0x000c8008u,
 0x60800044u, 0x000c8000u,
 0x608003c4u, 0x000c8008u,
 0x00800000u, 0x00000008u,
 0x01000020u, 0x00000000u,
 0x00800020u, 0x00000000u,
 0x00800000u, 0x00002800u,
 0x00801000u, 0x00000000u,
 0x21008024u, 0x00040000u,
 0x21000024u, 0x00040000u,
 0x00000020u, 0x00000080u,
 0x00002028u, 0x00000000u,
 0x60c00024u, 0x000c0000u,
 0x20800000u, 0x00040000u,
 0x60804004u, 0x000c0000u,
 0x60800024u, 0x000c0000u,
 0x20800004u, 0x00040000u,
 0x23008020u, 0x00040000u,
 0x21000004u, 0x00040000u,
 0x21408020u, 0x00040000u,
 0x60800004u, 0x00040000u,
 0x23000024u, 0x00040000u,
 0x60800004u, 0x000c0002u,
 0x00800010u, 0x00000000u,
 0x20808000u, 0x00040000u,
 0x21004024u, 0x00040000u,
 0x20808004u, 0x00040000u,
 0x60800944u, 0x000c0004u,
 0x60800064u, 0x000c0004u,
 0x60802004u, 0x000c0000u,
 0x60800344u, 0x000c8000u,
 0x22808000u, 0x00040000u,
 0x22800000u, 0x00040000u,
 0x00c00000u, 0x00000000u,
 0x21002020u, 0x00050000u,
 0x61000024u, 0x000c0000u,
 0x23000020u, 0x00040000u,
 0x01008020u, 0x00000000u,
 0x21408024u, 0x00040000u,
 0x00808000u, 0x00000000u,
 0x60800044u, 0x000c1004u,
 0x60800064u, 0x000c1004u,
 0x01002020u, 0x00000001u,
 0x00022020u, 0x00000001u,
 0x00002028u, 0x00000040u,
 0x00801000u, 0x00000020u,
 0x00800020u, 0x00000120u,
 0x00800000u, 0x00000120u,
 0x00800020u, 0x00000020u,
 0x00a10000u, 0x00002820u,
 0x00800000u, 0x00002820u,
 0x20800000u, 0x00040008u,
 0x00800010u, 0x00000020u,
 0x00002020u, 0x00000008u,
 0x00002000u, 0x00000000u,
 0x00006020u, 0x00000000u,
 0x00801000u, 0x00000008u,
 0x00800010u, 0x00000008u,
 0x21000020u, 0x00040008u,
 0x01020020u, 0x00000000u,
 0x60800044u, 0x000c000cu,
 0x60800000u, 0x000c0008u,
 0x00a10000u, 0x00000000u,
 0x60800000u, 0x000c0000u,
 0x60800004u, 0x000c0008u,
 0x60a10044u, 0x000c0004u,
 0x60800044u, 0x000c100cu,
 0x00a10000u, 0x00000028u,
 0x00800010u, 0x00000028u,
 0x00801000u, 0x00000028u,
 0x00b10000u, 0x00000020u,
 0x00804010u, 0x00000020u,
 0x00a00000u, 0x00000020u,
 0x00000000u, 0x00000020u,
 0x008003c4u, 0x00008000u,
 0x00a103c4u, 0x00008000u,
 0x00800d44u, 0x00000004u,
 0x00b10000u, 0x00000028u,
 0x00a00000u, 0x00000028u,
 0x00a90000u, 0x00000020u,
 0x00b90000u, 0x00000020u,
 0x00808024u, 0x00000020u,
 0x00800000u, 0x00002020u,
 0x00800000u, 0x00000200u,
 0x08800000u, 0x00000000u,
 0x10800000u, 0x00000000u,
 0xe0800004u, 0x000c0000u,
 0x21008000u, 0x00040000u,
 0x00a11000u, 0x00000020u,
 0x60808020u, 0x00000000u,
 0xe0800004u, 0x000c4000u,
 0x60808004u, 0x000c0000u,
 0x60800004u, 0x00000000u,
 0x00000000u, 0x00000010u,
 0x21022020u, 0x00050000u,
 0x00800000u, 0x00000100u,
 0x00800020u, 0x00002800u,
 0x00800020u, 0x00002000u,
 0x00800020u, 0x00000100u,
 0x24800000u, 0x00040000u,
 0x648003c4u, 0x000c8000u,
 0x00808020u, 0x00000008u,
 0x64800d44u, 0x000c0004u,
 0x00800010u, 0x00000100u,
 0x61008024u, 0x00040000u,
 0x00000020u, 0x00000000u,
 0x60c00004u, 0x000c0000u,
 0x21400020u, 0x00040000u,
 0xa1000020u, 0x00040000u,
 0x21000000u, 0x00040000u,
 0x00a00000u, 0x00000000u,
 0x00b10000u, 0x00000000u,
 0x00200000u, 0x00000000u,
 0x00800044u, 0x00008000u,
 0x00a10044u, 0x00008000u,
 0x00930000u, 0x00000400u,
 0x00b90000u, 0x00000000u,
 0x00a90000u, 0x00000000u,
 0x00970020u, 0x00000000u,
 0x00b30000u, 0x00000000u,
 0x01022020u, 0x00000000u,
};

/* These are the main two-stage UCD tables. The fields in each record are:
script (8 bits), character type (8 bits), grapheme break property (8 bits),
offset to multichar other cases or zero (8 bits), offset to other case or zero
(32 bits, signed), bidi class (5 bits) and script extension (11 bits) packed
into a 16-bit field, and offset in binary properties table (16 bits). */

const ucd_record PRIV(ucd_records)[] = { /* 17076 bytes, record size 12 */
  {    69,      0,      2,      0,      0,   6144,      2, }, /*   0 */
  {    69,      0,      2,      0,      0,  43008,      4, }, /*   1 */
  {    69,      0,      1,      0,      0,   4096,      4, }, /*   2 */
  {    69,      0,      2,      0,      0,  45056,      4, }, /*   3 */
  {    69,      0,      0,      0,      0,   4096,      4, }, /*   4 */
  {    69,      0,      2,      0,      0,   4096,      2, }, /*   5 */
  {    69,      0,      2,      0,      0,  43008,      2, }, /*   6 */
  {    69,     29,     12,      0,      0,  45056,      6, }, /*   7 */
  {    69,     21,     12,      0,      0,  28672,      8, }, /*   8 */
  {    69,     21,     12,      0,      0,  28672,     10, }, /*   9 */
  {    69,     21,     12,      0,      0,  14336,     12, }, /*  10 */
  {    69,     23,     12,      0,      0,  14336,     14, }, /*  11 */
  {    69,     21,     12,      0,      0,  14336,     14, }, /*  12 */
  {    69,     21,     12,      0,      0,  28672,     14, }, /*  13 */
  {    69,     21,     12,      0,      0,  28672,     16, }, /*  14 */
  {    69,     22,     12,      0,      0,  28672,     18, }, /*  15 */
  {    69,     18,     12,      0,      0,  28672,     18, }, /*  16 */
  {    69,     21,     12,      0,      0,  28672,     12, }, /*  17 */
  {    69,     25,     12,      0,      0,  12288,     20, }, /*  18 */
  {    69,     21,     12,      0,      0,   8192,     22, }, /*  19 */
  {    69,     17,     12,      0,      0,  12288,     24, }, /*  20 */
  {    69,     21,     12,      0,      0,   8192,     26, }, /*  21 */
  {    69,     21,     12,      0,      0,   8192,     14, }, /*  22 */
  {    69,     13,     12,      0,      0,  10240,     28, }, /*  23 */
  {    69,     21,     12,      0,      0,   8192,     30, }, /*  24 */
  {    69,     21,     12,      0,      0,  28672,     22, }, /*  25 */
  {    69,     25,     12,      0,      0,  28672,     32, }, /*  26 */
  {    69,     25,     12,      0,      0,  28672,     20, }, /*  27 */
  {     0,      9,     12,      0,     32,  18432,     34, }, /*  28 */
  {     0,      9,     12,      0,     32,  18432,     36, }, /*  29 */
  {     0,      9,     12,    100,     32,  18432,     36, }, /*  30 */
  {     0,      9,     12,      1,     32,  18432,     36, }, /*  31 */
  {    69,     24,     12,      0,      0,  28672,     38, }, /*  32 */
  {    69,     16,     12,      0,      0,  28672,     40, }, /*  33 */
  {    69,     24,     12,      0,      0,  28672,     42, }, /*  34 */
  {     0,      5,     12,      0,    -32,  18432,     44, }, /*  35 */
  {     0,      5,     12,      0,    -32,  18432,     46, }, /*  36 */
  {     0,      5,     12,      0,    -32,  18432,     48, }, /*  37 */
  {     0,      5,     12,    100,    -32,  18432,     46, }, /*  38 */
  {     0,      5,     12,      1,    -32,  18432,     46, }, /*  39 */
  {    69,      0,      2,      0,      0,   6144,      0, }, /*  40 */
  {    69,      0,      2,      0,      0,   4096,     50, }, /*  41 */
  {    69,     29,     12,      0,      0,   8192,     52, }, /*  42 */
  {    69,     21,     12,      0,      0,  28672,     54, }, /*  43 */
  {    69,     23,     12,      0,      0,  14336,     54, }, /*  44 */
  {    69,     26,     12,      0,      0,  28672,     54, }, /*  45 */
  {    69,     24,     12,      0,      0,  28672,     56, }, /*  46 */
  {    69,     26,     14,      0,      0,  28672,     58, }, /*  47 */
  {     0,      7,     12,      0,      0,  18432,     60, }, /*  48 */
  {    69,     20,     12,      0,      0,  28672,     62, }, /*  49 */
  {    69,     25,     12,      0,      0,  28672,     64, }, /*  50 */
  {    69,      1,      2,      0,      0,   6144,     66, }, /*  51 */
  {    69,     26,     12,      0,      0,  14336,     54, }, /*  52 */
  {    69,     25,     12,      0,      0,  14336,     64, }, /*  53 */
  {    69,     15,     12,      0,      0,  10240,     68, }, /*  54 */
  {    69,      5,     12,     26,    775,  18432,     70, }, /*  55 */
  {    69,     21,     12,      0,      0,  28672,     72, }, /*  56 */
  {    69,     19,     12,      0,      0,  28672,     62, }, /*  57 */
  {    69,     15,     12,      0,      0,  28672,     68, }, /*  58 */
  {     0,      9,     12,      0,     32,  18432,     74, }, /*  59 */
  {     0,      9,     12,    104,     32,  18432,     74, }, /*  60 */
  {     0,      5,     12,      0,   7615,  18432,     70, }, /*  61 */
  {     0,      5,     12,      0,    -32,  18432,     76, }, /*  62 */
  {     0,      5,     12,    104,    -32,  18432,     76, }, /*  63 */
  {     0,      5,     12,      0,    121,  18432,     76, }, /*  64 */
  {     0,      9,     12,      0,      1,  18432,     74, }, /*  65 */
  {     0,      5,     12,      0,     -1,  18432,     76, }, /*  66 */
  {     0,      5,     12,      0,     -1,  18432,     78, }, /*  67 */
  {     0,      9,     12,      0,      0,  18432,     74, }, /*  68 */
  {     0,      5,     12,      0,      0,  18432,     76, }, /*  69 */
  {     0,      5,     12,      0,      0,  18432,     60, }, /*  70 */
  {     0,      5,     12,      0,      0,  18432,     80, }, /*  71 */
  {     0,      9,     12,      0,   -121,  18432,     74, }, /*  72 */
  {     0,      5,     12,      1,      0,  18432,     70, }, /*  73 */
  {     0,      5,     12,      0,    195,  18432,     76, }, /*  74 */
  {     0,      9,     12,      0,    210,  18432,     74, }, /*  75 */
  {     0,      9,     12,      0,    206,  18432,     74, }, /*  76 */
  {     0,      9,     12,      0,    205,  18432,     74, }, /*  77 */
  {     0,      9,     12,      0,     79,  18432,     74, }, /*  78 */
  {     0,      9,     12,      0,    202,  18432,     74, }, /*  79 */
  {     0,      9,     12,      0,    203,  18432,     74, }, /*  80 */
  {     0,      9,     12,      0,    207,  18432,     74, }, /*  81 */
  {     0,      5,     12,      0,     97,  18432,     76, }, /*  82 */
  {     0,      9,     12,      0,    211,  18432,     74, }, /*  83 */
  {     0,      9,     12,      0,    209,  18432,     74, }, /*  84 */
  {     0,      5,     12,      0,    163,  18432,     76, }, /*  85 */
  {     0,      9,     12,      0,    213,  18432,     74, }, /*  86 */
  {     0,      5,     12,      0,    130,  18432,     76, }, /*  87 */
  {     0,      9,     12,      0,    214,  18432,     74, }, /*  88 */
  {     0,      9,     12,      0,    218,  18432,     74, }, /*  89 */
  {     0,      9,     12,      0,    217,  18432,     74, }, /*  90 */
  {     0,      9,     12,      0,    219,  18432,     74, }, /*  91 */
  {     0,      7,     12,      0,      0,  18432,     82, }, /*  92 */
  {     0,      5,     12,      0,     56,  18432,     76, }, /*  93 */
  {     0,      9,     12,      5,      2,  18432,     84, }, /*  94 */
  {     0,      8,     12,      5,      1,  18432,     86, }, /*  95 */
  {     0,      5,     12,      5,     -2,  18432,     76, }, /*  96 */
  {     0,      9,     12,      9,      2,  18432,     84, }, /*  97 */
  {     0,      8,     12,      9,      1,  18432,     86, }, /*  98 */
  {     0,      5,     12,      9,     -2,  18432,     76, }, /*  99 */
  {     0,      9,     12,     13,      2,  18432,     84, }, /* 100 */
  {     0,      8,     12,     13,      1,  18432,     86, }, /* 101 */
  {     0,      5,     12,     13,     -2,  18432,     76, }, /* 102 */
  {     0,      5,     12,      0,    -79,  18432,     76, }, /* 103 */
  {     0,      9,     12,     17,      2,  18432,     84, }, /* 104 */
  {     0,      8,     12,     17,      1,  18432,     86, }, /* 105 */
  {     0,      5,     12,     17,     -2,  18432,     76, }, /* 106 */
  {     0,      9,     12,      0,    -97,  18432,     74, }, /* 107 */
  {     0,      9,     12,      0,    -56,  18432,     74, }, /* 108 */
  {     0,      9,     12,      0,   -130,  18432,     74, }, /* 109 */
  {     0,      9,     12,      0,  10795,  18432,     74, }, /* 110 */
  {     0,      9,     12,      0,   -163,  18432,     74, }, /* 111 */
  {     0,      9,     12,      0,  10792,  18432,     74, }, /* 112 */
  {     0,      5,     12,      0,  10815,  18432,     76, }, /* 113 */
  {     0,      9,     12,      0,   -195,  18432,     74, }, /* 114 */
  {     0,      9,     12,      0,     69,  18432,     74, }, /* 115 */
  {     0,      9,     12,      0,     71,  18432,     74, }, /* 116 */
  {     0,      5,     12,      0,  10783,  18432,     76, }, /* 117 */
  {     0,      5,     12,      0,  10780,  18432,     76, }, /* 118 */
  {     0,      5,     12,      0,  10782,  18432,     76, }, /* 119 */
  {     0,      5,     12,      0,   -210,  18432,     76, }, /* 120 */
  {     0,      5,     12,      0,   -206,  18432,     76, }, /* 121 */
  {     0,      5,     12,      0,   -205,  18432,     76, }, /* 122 */
  {     0,      5,     12,      0,   -202,  18432,     76, }, /* 123 */
  {     0,      5,     12,      0,   -203,  18432,     76, }, /* 124 */
  {     0,      5,     12,      0,  42319,  18432,     76, }, /* 125 */
  {     0,      5,     12,      0,  42315,  18432,     76, }, /* 126 */
  {     0,      5,     12,      0,   -207,  18432,     76, }, /* 127 */
  {     0,      5,     12,      0,  42280,  18432,     76, }, /* 128 */
  {     0,      5,     12,      0,  42308,  18432,     76, }, /* 129 */
  {     0,      5,     12,      0,   -209,  18432,     78, }, /* 130 */
  {     0,      5,     12,      0,   -211,  18432,     76, }, /* 131 */
  {     0,      5,     12,      0,  10743,  18432,     76, }, /* 132 */
  {     0,      5,     12,      0,  42305,  18432,     76, }, /* 133 */
  {     0,      5,     12,      0,  10749,  18432,     76, }, /* 134 */
  {     0,      5,     12,      0,   -213,  18432,     76, }, /* 135 */
  {     0,      5,     12,      0,   -214,  18432,     76, }, /* 136 */
  {     0,      5,     12,      0,  10727,  18432,     76, }, /* 137 */
  {     0,      5,     12,      0,   -218,  18432,     76, }, /* 138 */
  {     0,      5,     12,      0,  42307,  18432,     76, }, /* 139 */
  {     0,      5,     12,      0,  42282,  18432,     76, }, /* 140 */
  {     0,      5,     12,      0,    -69,  18432,     76, }, /* 141 */
  {     0,      5,     12,      0,   -217,  18432,     76, }, /* 142 */
  {     0,      5,     12,      0,    -71,  18432,     76, }, /* 143 */
  {     0,      5,     12,      0,   -219,  18432,     76, }, /* 144 */
  {     0,      5,     12,      0,  42261,  18432,     78, }, /* 145 */
  {     0,      5,     12,      0,  42258,  18432,     76, }, /* 146 */
  {     0,      6,     12,      0,      0,  18432,     88, }, /* 147 */
  {     0,      6,     12,      0,      0,  18432,     90, }, /* 148 */
  {    69,      6,     12,      0,      0,  28672,     92, }, /* 149 */
  {    69,      6,     12,      0,      0,  18432,     92, }, /* 150 */
  {    69,      6,     12,      0,      0,  18432,     88, }, /* 151 */
  {    69,      6,     12,      0,      0,  18432,     94, }, /* 152 */
  {    22,     24,     12,      0,      0,  28672,     56, }, /* 153 */
  {    84,     12,      3,      0,      0,  26624,     96, }, /* 154 */
  {    84,     12,      3,      0,      0,  26636,     96, }, /* 155 */
  {    84,     12,      3,     21,    116,  26636,     98, }, /* 156 */
  {    84,     12,      3,      0,      0,  26624,    100, }, /* 157 */
  {    84,     12,      3,      0,      0,  26624,    102, }, /* 158 */
  {    84,     12,      3,      0,      0,  26642,    102, }, /* 159 */
  {     1,      9,     12,      0,      1,  18432,     74, }, /* 160 */
  {     1,      5,     12,      0,     -1,  18432,     76, }, /* 161 */
  {     1,     24,     12,      0,      0,  28672,     56, }, /* 162 */
  {    68,      2,     12,      0,      0,  18432,      0, }, /* 163 */
  {     1,      6,     12,      0,      0,  18432,    104, }, /* 164 */
  {     1,      5,     12,      0,    130,  18432,     76, }, /* 165 */
  {    69,     21,     12,      0,      0,  28672,    106, }, /* 166 */
  {     1,      9,     12,      0,    116,  18432,     74, }, /* 167 */
  {     1,      9,     12,      0,     38,  18432,     74, }, /* 168 */
  {    69,     21,     12,      0,      0,  28672,    108, }, /* 169 */
  {     1,      9,     12,      0,     37,  18432,     74, }, /* 170 */
  {     1,      9,     12,      0,     64,  18432,     74, }, /* 171 */
  {     1,      9,     12,      0,     63,  18432,     74, }, /* 172 */
  {     1,      5,     12,      0,      0,  18432,     76, }, /* 173 */
  {     1,      9,     12,      0,     32,  18432,     74, }, /* 174 */
  {     1,      9,     12,     34,     32,  18432,     74, }, /* 175 */
  {     1,      9,     12,     59,     32,  18432,     74, }, /* 176 */
  {     1,      9,     12,     38,     32,  18432,     74, }, /* 177 */
  {     1,      9,     12,     21,     32,  18432,     74, }, /* 178 */
  {     1,      9,     12,     51,     32,  18432,     74, }, /* 179 */
  {     1,      9,     12,     26,     32,  18432,     74, }, /* 180 */
  {     1,      9,     12,     47,     32,  18432,     74, }, /* 181 */
  {     1,      9,     12,     55,     32,  18432,     74, }, /* 182 */
  {     1,      9,     12,     30,     32,  18432,     74, }, /* 183 */
  {     1,      9,     12,     43,     32,  18432,     74, }, /* 184 */
  {     1,      9,     12,     96,     32,  18432,     74, }, /* 185 */
  {     1,      5,     12,      0,    -38,  18432,     76, }, /* 186 */
  {     1,      5,     12,      0,    -37,  18432,     76, }, /* 187 */
  {     1,      5,     12,      0,    -32,  18432,     76, }, /* 188 */
  {     1,      5,     12,     34,    -32,  18432,     76, }, /* 189 */
  {     1,      5,     12,     59,    -32,  18432,     76, }, /* 190 */
  {     1,      5,     12,     38,    -32,  18432,     76, }, /* 191 */
  {     1,      5,     12,     21,   -116,  18432,     76, }, /* 192 */
  {     1,      5,     12,     51,    -32,  18432,     76, }, /* 193 */
  {     1,      5,     12,     26,   -775,  18432,     76, }, /* 194 */
  {     1,      5,     12,     47,    -32,  18432,     76, }, /* 195 */
  {     1,      5,     12,     55,    -32,  18432,     76, }, /* 196 */
  {     1,      5,     12,     30,      1,  18432,     70, }, /* 197 */
  {     1,      5,     12,     30,    -32,  18432,     76, }, /* 198 */
  {     1,      5,     12,     43,    -32,  18432,     76, }, /* 199 */
  {     1,      5,     12,     96,    -32,  18432,     76, }, /* 200 */
  {     1,      5,     12,      0,    -64,  18432,     76, }, /* 201 */
  {     1,      5,     12,      0,    -63,  18432,     76, }, /* 202 */
  {     1,      9,     12,      0,      8,  18432,     74, }, /* 203 */
  {     1,      5,     12,     34,    -30,  18432,    110, }, /* 204 */
  {     1,      5,     12,     38,    -25,  18432,    110, }, /* 205 */
  {     1,      9,     12,      0,      0,  18432,    112, }, /* 206 */
  {     1,      9,     12,      0,      0,  18432,    114, }, /* 207 */
  {     1,      5,     12,     43,    -15,  18432,    110, }, /* 208 */
  {     1,      5,     12,     47,    -22,  18432,     70, }, /* 209 */
  {     1,      5,     12,      0,     -8,  18432,     76, }, /* 210 */
  {    34,      9,     12,      0,      1,  18432,     74, }, /* 211 */
  {    34,      5,     12,      0,     -1,  18432,     76, }, /* 212 */
  {     1,      5,     12,     51,    -54,  18432,    110, }, /* 213 */
  {     1,      5,     12,     55,    -48,  18432,    110, }, /* 214 */
  {     1,      5,     12,      0,      7,  18432,     76, }, /* 215 */
  {     1,      5,     12,      0,   -116,  18432,     78, }, /* 216 */
  {     1,      9,     12,     38,    -60,  18432,    116, }, /* 217 */
  {     1,      5,     12,     59,    -64,  18432,    110, }, /* 218 */
  {     1,     25,     12,      0,      0,  28672,    118, }, /* 219 */
  {     1,      9,     12,      0,     -7,  18432,     74, }, /* 220 */
  {     1,      5,     12,      0,      0,  18432,     60, }, /* 221 */
  {     1,      9,     12,      0,   -130,  18432,     74, }, /* 222 */
  {     2,      9,     12,      0,     80,  18432,     74, }, /* 223 */
  {     2,      9,     12,      0,     32,  18432,     74, }, /* 224 */
  {     2,      9,     12,     63,     32,  18432,     74, }, /* 225 */
  {     2,      9,     12,     67,     32,  18432,     74, }, /* 226 */
  {     2,      9,     12,     71,     32,  18432,     74, }, /* 227 */
  {     2,      9,     12,     75,     32,  18432,     74, }, /* 228 */
  {     2,      9,     12,     79,     32,  18432,     74, }, /* 229 */
  {     2,      9,     12,     84,     32,  18432,     74, }, /* 230 */
  {     2,      5,     12,      0,    -32,  18432,     76, }, /* 231 */
  {     2,      5,     12,     63,    -32,  18432,     76, }, /* 232 */
  {     2,      5,     12,     67,    -32,  18432,     76, }, /* 233 */
  {     2,      5,     12,     71,    -32,  18432,     76, }, /* 234 */
  {     2,      5,     12,     75,    -32,  18432,     76, }, /* 235 */
  {     2,      5,     12,     79,    -32,  18432,     76, }, /* 236 */
  {     2,      5,     12,     84,    -32,  18432,     76, }, /* 237 */
  {     2,      5,     12,      0,    -80,  18432,     76, }, /* 238 */
  {     2,      5,     12,      0,    -80,  18432,     78, }, /* 239 */
  {     2,      9,     12,      0,      1,  18432,     74, }, /* 240 */
  {     2,      5,     12,      0,     -1,  18432,     76, }, /* 241 */
  {     2,      9,     12,     88,      1,  18432,     74, }, /* 242 */
  {     2,      5,     12,     88,     -1,  18432,     76, }, /* 243 */
  {     2,     26,     12,      0,      0,  18432,     68, }, /* 244 */
  {     2,     12,      3,      0,      0,  26684,     96, }, /* 245 */
  {     2,     12,      3,      0,      0,  26678,     96, }, /* 246 */
  {    84,     12,      3,      0,      0,  26681,     96, }, /* 247 */
  {     2,     11,      3,      0,      0,  26624,    120, }, /* 248 */
  {     2,      9,     12,      0,     15,  18432,     74, }, /* 249 */
  {     2,      5,     12,      0,    -15,  18432,     76, }, /* 250 */
  {    70,      9,     12,      0,     48,  18432,     74, }, /* 251 */
  {    70,      6,     12,      0,      0,  18432,     92, }, /* 252 */
  {    70,     21,     12,      0,      0,  18432,     68, }, /* 253 */
  {    70,     21,     12,      0,      0,  18432,    122, }, /* 254 */
  {    70,      5,     12,      0,      0,  18432,     60, }, /* 255 */
  {    70,      5,     12,      0,    -48,  18432,     76, }, /* 256 */
  {    70,      5,     12,      0,      0,  18432,     70, }, /* 257 */
  {    70,     21,     12,      0,      0,  18432,    124, }, /* 258 */
  {    70,     17,     12,      0,      0,  28672,    126, }, /* 259 */
  {    70,     26,     12,      0,      0,  28672,     68, }, /* 260 */
  {    70,     23,     12,      0,      0,  14336,     68, }, /* 261 */
  {    68,      2,     12,      0,      0,  34816,      0, }, /* 262 */
  {    71,     12,      3,      0,      0,  26624,     96, }, /* 263 */
  {    71,     12,      3,      0,      0,  26624,    102, }, /* 264 */
  {    71,     12,      3,      0,      0,  26624,    128, }, /* 265 */
  {    71,     17,     12,      0,      0,  34816,    126, }, /* 266 */
  {    71,     21,     12,      0,      0,  34816,     68, }, /* 267 */
  {    71,     21,     12,      0,      0,  34816,    106, }, /* 268 */
  {    71,     12,      3,      0,      0,  26624,    130, }, /* 269 */
  {    71,      7,     12,      0,      0,  34816,     82, }, /* 270 */
  {    71,     21,     12,      0,      0,  34816,    122, }, /* 271 */
  {     3,      1,      4,      0,      0,   2048,    132, }, /* 272 */
  {    69,      1,      4,      0,      0,   2048,    132, }, /* 273 */
  {     3,     25,     12,      0,      0,  28672,    118, }, /* 274 */
  {     3,     25,     12,      0,      0,      0,    118, }, /* 275 */
  {     3,     21,     12,      0,      0,  14336,     68, }, /* 276 */
  {     3,     23,     12,      0,      0,      0,     68, }, /* 277 */
  {    69,     21,     12,      0,      0,   8342,    106, }, /* 278 */
  {     3,     21,     12,      0,      0,      0,     68, }, /* 279 */
  {     3,     26,     12,      0,      0,  28672,     68, }, /* 280 */
  {     3,     12,      3,      0,      0,  26624,    130, }, /* 281 */
  {    69,     21,     12,      0,      0,    150,    106, }, /* 282 */
  {     3,      1,      2,      0,      0,    108,    134, }, /* 283 */
  {     3,     21,     12,      0,      0,      0,    124, }, /* 284 */
  {    69,     21,     12,      0,      0,    159,    124, }, /* 285 */
  {     3,      7,     12,      0,      0,      0,     82, }, /* 286 */
  {    69,      6,     12,      0,      0,    165,    136, }, /* 287 */
  {    84,     12,      3,      0,      0,  26660,    128, }, /* 288 */
  {    84,     12,      3,      0,      0,  26660,    130, }, /* 289 */
  {     3,     12,      3,      0,      0,  26624,    128, }, /* 290 */
  {     3,     12,      3,      0,      0,  26624,     96, }, /* 291 */
  {     3,     13,     12,      0,      0,   2159,    138, }, /* 292 */
  {     3,     21,     12,      0,      0,   2048,     68, }, /* 293 */
  {     3,      7,     12,      0,      0,      0,    140, }, /* 294 */
  {     3,     21,     12,      0,      0,     30,    124, }, /* 295 */
  {     3,      6,     12,      0,      0,      0,     92, }, /* 296 */
  {     3,     13,     12,      0,      0,  10240,    138, }, /* 297 */
  {     3,     26,     12,      0,      0,      0,     68, }, /* 298 */
  {     4,     21,     12,      0,      0,      0,    124, }, /* 299 */
  {     4,     21,     12,      0,      0,      0,    106, }, /* 300 */
  {     4,     21,     12,      0,      0,      0,     68, }, /* 301 */
  {    68,      2,     12,      0,      0,      0,      0, }, /* 302 */
  {     4,      1,      4,      0,      0,      0,    132, }, /* 303 */
  {     4,      7,     12,      0,      0,      0,     82, }, /* 304 */
  {     4,     12,      3,      0,      0,  26624,    130, }, /* 305 */
  {     4,     12,      3,      0,      0,  26624,    128, }, /* 306 */
  {     4,     12,      3,      0,      0,  26624,     96, }, /* 307 */
  {     5,      7,     12,      0,      0,      0,     82, }, /* 308 */
  {     5,     12,      3,      0,      0,  26624,    128, }, /* 309 */
  {    38,     13,     12,      0,      0,  34816,    138, }, /* 310 */
  {    38,      7,     12,      0,      0,  34816,     82, }, /* 311 */
  {    38,     12,      3,      0,      0,  26624,     96, }, /* 312 */
  {    38,      6,     12,      0,      0,  34816,     92, }, /* 313 */
  {    38,     26,     12,      0,      0,  28672,     68, }, /* 314 */
  {    38,     21,     12,      0,      0,  28672,     68, }, /* 315 */
  {    38,     21,     12,      0,      0,  28672,    106, }, /* 316 */
  {    38,     21,     12,      0,      0,  28672,    124, }, /* 317 */
  {    38,      6,     12,      0,      0,  34816,    136, }, /* 318 */
  {    38,     12,      3,      0,      0,  26624,    102, }, /* 319 */
  {    38,     23,     12,      0,      0,  34816,     68, }, /* 320 */
  {   110,      7,     12,      0,      0,  34816,     82, }, /* 321 */
  {   110,     12,      3,      0,      0,  26624,    130, }, /* 322 */
  {   110,     12,      3,      0,      0,  26624,     96, }, /* 323 */
  {   110,      6,     12,      0,      0,  34816,    142, }, /* 324 */
  {   110,     12,      3,      0,      0,  26624,    102, }, /* 325 */
  {   110,     21,     12,      0,      0,  34816,    106, }, /* 326 */
  {   110,     21,     12,      0,      0,  34816,    124, }, /* 327 */
  {    42,      7,     12,      0,      0,  34816,     82, }, /* 328 */
  {    42,     12,      3,      0,      0,  26624,    102, }, /* 329 */
  {    42,     21,     12,      0,      0,  34816,    106, }, /* 330 */
  {     3,     24,     12,      0,      0,      0,    122, }, /* 331 */
  {     3,     12,      3,      0,      0,  26624,    102, }, /* 332 */
  {     6,     12,      3,      0,      0,  26624,    130, }, /* 333 */
  {     6,     10,      5,      0,      0,  18432,    144, }, /* 334 */
  {     6,      7,     12,      0,      0,  18432,     82, }, /* 335 */
  {     6,     12,      3,      0,      0,  26624,     96, }, /* 336 */
  {     6,     12,      3,      0,      0,  26624,    146, }, /* 337 */
  {    84,     12,      3,      0,      0,  26798,     96, }, /* 338 */
  {    84,     12,      3,      0,      0,  26795,     96, }, /* 339 */
  {    69,     21,     12,      0,      0,  18615,    124, }, /* 340 */
  {    69,     21,     12,      0,      0,  18618,    124, }, /* 341 */
  {     6,     13,     12,      0,      0,  18576,    138, }, /* 342 */
  {     6,     21,     12,      0,      0,  18432,     68, }, /* 343 */
  {     6,      6,     12,      0,      0,  18432,     92, }, /* 344 */
  {     7,      7,     12,      0,      0,  18432,     82, }, /* 345 */
  {     7,     12,      3,      0,      0,  26624,    130, }, /* 346 */
  {     7,     10,      5,      0,      0,  18432,    144, }, /* 347 */
  {     7,     12,      3,      0,      0,  26624,     96, }, /* 348 */
  {     7,     10,      3,      0,      0,  18432,    148, }, /* 349 */
  {     7,     12,      3,      0,      0,  26624,    146, }, /* 350 */
  {     7,     13,     12,      0,      0,  18546,    138, }, /* 351 */
  {     7,     23,     12,      0,      0,  14336,     68, }, /* 352 */
  {     7,     15,     12,      0,      0,  18432,     68, }, /* 353 */
  {     7,     26,     12,      0,      0,  18432,     68, }, /* 354 */
  {     7,     21,     12,      0,      0,  18432,     68, }, /* 355 */
  {     7,     12,      3,      0,      0,  26624,    102, }, /* 356 */
  {     8,     12,      3,      0,      0,  26624,    130, }, /* 357 */
  {     8,     10,      5,      0,      0,  18432,    144, }, /* 358 */
  {     8,      7,     12,      0,      0,  18432,     82, }, /* 359 */
  {     8,     12,      3,      0,      0,  26624,     96, }, /* 360 */
  {     8,     12,      3,      0,      0,  26624,    146, }, /* 361 */
  {     8,     13,     12,      0,      0,  18519,    138, }, /* 362 */
  {     8,     21,     12,      0,      0,  18432,     68, }, /* 363 */
  {     9,     12,      3,      0,      0,  26624,    130, }, /* 364 */
  {     9,     10,      5,      0,      0,  18432,    144, }, /* 365 */
  {     9,      7,     12,      0,      0,  18432,     82, }, /* 366 */
  {     9,     12,      3,      0,      0,  26624,     96, }, /* 367 */
  {     9,     12,      3,      0,      0,  26624,    146, }, /* 368 */
  {     9,     13,     12,      0,      0,  18516,    138, }, /* 369 */
  {     9,     21,     12,      0,      0,  18432,     68, }, /* 370 */
  {     9,     23,     12,      0,      0,  14336,     68, }, /* 371 */
  {    10,     12,      3,      0,      0,  26624,    130, }, /* 372 */
  {    10,     10,      5,      0,      0,  18432,    144, }, /* 373 */
  {    10,      7,     12,      0,      0,  18432,     82, }, /* 374 */
  {    10,     12,      3,      0,      0,  26624,     96, }, /* 375 */
  {    10,     10,      3,      0,      0,  18432,    148, }, /* 376 */
  {    10,     12,      3,      0,      0,  26624,    146, }, /* 377 */
  {    10,     12,      3,      0,      0,  26624,    150, }, /* 378 */
  {    10,     13,     12,      0,      0,  18432,    138, }, /* 379 */
  {    10,     26,     12,      0,      0,  18432,     68, }, /* 380 */
  {    10,     15,     12,      0,      0,  18432,     68, }, /* 381 */
  {    11,     12,      3,      0,      0,  26624,    130, }, /* 382 */
  {    11,      7,     12,      0,      0,  18432,     82, }, /* 383 */
  {    11,     10,      3,      0,      0,  18432,    148, }, /* 384 */
  {    11,     10,      5,      0,      0,  18432,    144, }, /* 385 */
  {    11,     12,      3,      0,      0,  26624,    146, }, /* 386 */
  {    11,     13,     12,      0,      0,  18513,    138, }, /* 387 */
  {    11,     15,     12,      0,      0,  18513,     68, }, /* 388 */
  {    11,     26,     12,      0,      0,  28753,     68, }, /* 389 */
  {    11,     26,     12,      0,      0,  28672,     68, }, /* 390 */
  {    11,     23,     12,      0,      0,  14336,     68, }, /* 391 */
  {    12,     12,      3,      0,      0,  26624,    130, }, /* 392 */
  {    12,     10,      5,      0,      0,  18432,    144, }, /* 393 */
  {    12,      7,     12,      0,      0,  18432,     82, }, /* 394 */
  {    12,     12,      3,      0,      0,  26624,     96, }, /* 395 */
  {    12,     12,      3,      0,      0,  26624,    146, }, /* 396 */
  {    12,     13,     12,      0,      0,  18432,    138, }, /* 397 */
  {    12,     21,     12,      0,      0,  18432,     68, }, /* 398 */
  {    12,     15,     12,      0,      0,  28672,     68, }, /* 399 */
  {    12,     26,     12,      0,      0,  18432,     68, }, /* 400 */
  {    13,      7,     12,      0,      0,  18432,     82, }, /* 401 */
  {    13,     12,      3,      0,      0,  26624,    130, }, /* 402 */
  {    13,     10,      5,      0,      0,  18432,    144, }, /* 403 */
  {    13,     21,     12,      0,      0,  18432,     68, }, /* 404 */
  {    13,     12,      3,      0,      0,  26624,     96, }, /* 405 */
  {    13,     12,      3,      0,      0,  18432,    130, }, /* 406 */
  {    13,     10,      3,      0,      0,  18432,    148, }, /* 407 */
  {    13,     12,      3,      0,      0,  26624,    146, }, /* 408 */
  {    13,     13,     12,      0,      0,  18528,    138, }, /* 409 */
  {    14,     12,      3,      0,      0,  26624,    130, }, /* 410 */
  {    14,     10,      5,      0,      0,  18432,    144, }, /* 411 */
  {    14,      7,     12,      0,      0,  18432,     82, }, /* 412 */
  {    14,     12,      3,      0,      0,  26624,    146, }, /* 413 */
  {    14,     10,      3,      0,      0,  18432,    148, }, /* 414 */
  {    14,      7,      4,      0,      0,  18432,     82, }, /* 415 */
  {    14,     26,     12,      0,      0,  18432,     68, }, /* 416 */
  {    14,     15,     12,      0,      0,  18432,     68, }, /* 417 */
  {    14,     13,     12,      0,      0,  18432,    138, }, /* 418 */
  {    15,     12,      3,      0,      0,  26624,    130, }, /* 419 */
  {    15,     10,      5,      0,      0,  18432,    144, }, /* 420 */
  {    15,      7,     12,      0,      0,  18432,     82, }, /* 421 */
  {    15,     12,      3,      0,      0,  26624,    146, }, /* 422 */
  {    15,     10,      3,      0,      0,  18432,    148, }, /* 423 */
  {    15,     13,     12,      0,      0,  18432,    138, }, /* 424 */
  {    15,     21,     12,      0,      0,  18432,     68, }, /* 425 */
  {    72,      7,     12,      0,      0,  18432,     82, }, /* 426 */
  {    72,     12,      3,      0,      0,  26624,    130, }, /* 427 */
  {    72,      7,      5,      0,      0,  18432,    152, }, /* 428 */
  {    72,     12,      3,      0,      0,  26624,    154, }, /* 429 */
  {    69,     23,     12,      0,      0,  14336,     68, }, /* 430 */
  {    72,      7,     12,      0,      0,  18432,    156, }, /* 431 */
  {    72,      6,     12,      0,      0,  18432,    136, }, /* 432 */
  {    72,     12,      3,      0,      0,  26624,     96, }, /* 433 */
  {    72,     21,     12,      0,      0,  18432,     68, }, /* 434 */
  {    72,     13,     12,      0,      0,  18432,    138, }, /* 435 */
  {    72,     21,     12,      0,      0,  18432,    106, }, /* 436 */
  {    73,      7,     12,      0,      0,  18432,     82, }, /* 437 */
  {    73,     12,      3,      0,      0,  26624,    130, }, /* 438 */
  {    73,      7,      5,      0,      0,  18432,    152, }, /* 439 */
  {    73,     12,      3,      0,      0,  26624,    146, }, /* 440 */
  {    73,      7,     12,      0,      0,  18432,    156, }, /* 441 */
  {    73,      6,     12,      0,      0,  18432,    136, }, /* 442 */
  {    73,     12,      3,      0,      0,  26624,     96, }, /* 443 */
  {    73,     12,      3,      0,      0,  26624,    102, }, /* 444 */
  {    73,     13,     12,      0,      0,  18432,    138, }, /* 445 */
  {    74,      7,     12,      0,      0,  18432,     82, }, /* 446 */
  {    74,     26,     12,      0,      0,  18432,     68, }, /* 447 */
  {    74,     21,     12,      0,      0,  18432,     68, }, /* 448 */
  {    74,     21,     12,      0,      0,  18432,    106, }, /* 449 */
  {    74,     12,      3,      0,      0,  26624,     96, }, /* 450 */
  {    74,     13,     12,      0,      0,  18432,    138, }, /* 451 */
  {    74,     15,     12,      0,      0,  18432,     68, }, /* 452 */
  {    74,     22,     12,      0,      0,  28672,    158, }, /* 453 */
  {    74,     18,     12,      0,      0,  28672,    158, }, /* 454 */
  {    74,     10,      5,      0,      0,  18432,    160, }, /* 455 */
  {    74,     12,      3,      0,      0,  26624,    130, }, /* 456 */
  {    74,     12,      3,      0,      0,  26624,    162, }, /* 457 */
  {    74,     10,      5,      0,      0,  18432,    144, }, /* 458 */
  {    74,     12,      3,      0,      0,  26624,    128, }, /* 459 */
  {    74,     12,      3,      0,      0,  26624,    146, }, /* 460 */
  {    69,     26,     12,      0,      0,  18432,     68, }, /* 461 */
  {    16,      7,     12,      0,      0,  18432,     82, }, /* 462 */
  {    16,     10,     12,      0,      0,  18432,    144, }, /* 463 */
  {    16,     12,      3,      0,      0,  26624,    130, }, /* 464 */
  {    16,     10,      5,      0,      0,  18432,    144, }, /* 465 */
  {    16,     12,      3,      0,      0,  26624,     96, }, /* 466 */
  {    16,     12,      3,      0,      0,  26624,    146, }, /* 467 */
  {    16,     13,     12,      0,      0,  18549,    138, }, /* 468 */
  {    16,     21,     12,      0,      0,  18432,    124, }, /* 469 */
  {    16,     21,     12,      0,      0,  18432,     68, }, /* 470 */
  {    16,     10,     12,      0,      0,  18432,    164, }, /* 471 */
  {    16,     12,      3,      0,      0,  26624,    128, }, /* 472 */
  {    16,     13,     12,      0,      0,  18432,    138, }, /* 473 */
  {    16,     26,     12,      0,      0,  18432,     68, }, /* 474 */
  {    17,      9,     12,      0,   7264,  18432,     74, }, /* 475 */
  {    17,      5,     12,      0,   3008,  18432,    166, }, /* 476 */
  {    69,     21,     12,      0,      0,  18510,     68, }, /* 477 */
  {    17,      6,     12,      0,      0,  18432,    168, }, /* 478 */
  {    18,      7,      6,      0,      0,  18432,     82, }, /* 479 */
  {    18,      7,      6,      0,      0,  18432,    170, }, /* 480 */
  {    18,      7,      7,      0,      0,  18432,    170, }, /* 481 */
  {    18,      7,      7,      0,      0,  18432,     82, }, /* 482 */
  {    18,      7,      8,      0,      0,  18432,     82, }, /* 483 */
  {    75,      7,     12,      0,      0,  18432,     82, }, /* 484 */
  {    75,     12,      3,      0,      0,  26624,     96, }, /* 485 */
  {    75,     21,     12,      0,      0,  18432,     68, }, /* 486 */
  {    75,     21,     12,      0,      0,  18432,    106, }, /* 487 */
  {    75,     21,     12,      0,      0,  18432,    124, }, /* 488 */
  {    75,     15,     12,      0,      0,  18432,    138, }, /* 489 */
  {    75,     15,     12,      0,      0,  18432,     68, }, /* 490 */
  {    75,     26,     12,      0,      0,  28672,     68, }, /* 491 */
  {    76,      9,     12,      0,  38864,  18432,    172, }, /* 492 */
  {    76,      9,     12,      0,      8,  18432,    172, }, /* 493 */
  {    76,      5,     12,      0,     -8,  18432,     70, }, /* 494 */
  {    77,     17,     12,      0,      0,  28672,    126, }, /* 495 */
  {    77,      7,     12,      0,      0,  18432,     82, }, /* 496 */
  {    77,     26,     12,      0,      0,  18432,     68, }, /* 497 */
  {    77,     21,     12,      0,      0,  18432,    124, }, /* 498 */
  {    78,     29,     12,      0,      0,  45056,     52, }, /* 499 */
  {    78,      7,     12,      0,      0,  18432,     82, }, /* 500 */
  {    78,     22,     12,      0,      0,  28672,    158, }, /* 501 */
  {    78,     18,     12,      0,      0,  28672,    158, }, /* 502 */
  {    79,      7,     12,      0,      0,  18432,     82, }, /* 503 */
  {    69,     21,     12,      0,      0,  18432,    106, }, /* 504 */
  {    79,     14,     12,      0,      0,  18432,     82, }, /* 505 */
  {    25,      7,     12,      0,      0,  18432,     82, }, /* 506 */
  {    25,     12,      3,      0,      0,  26624,    130, }, /* 507 */
  {    25,     12,      3,      0,      0,  26624,    146, }, /* 508 */
  {    25,     10,      5,      0,      0,  18432,    174, }, /* 509 */
  {    26,      7,     12,      0,      0,  18432,     82, }, /* 510 */
  {    26,     12,      3,      0,      0,  26624,    130, }, /* 511 */
  {    26,     10,      5,      0,      0,  18432,    176, }, /* 512 */
  {    69,     21,     12,      0,      0,  18573,    124, }, /* 513 */
  {    27,      7,     12,      0,      0,  18432,     82, }, /* 514 */
  {    27,     12,      3,      0,      0,  26624,    130, }, /* 515 */
  {    28,      7,     12,      0,      0,  18432,     82, }, /* 516 */
  {    28,     12,      3,      0,      0,  26624,    130, }, /* 517 */
  {    80,      7,     12,      0,      0,  18432,     82, }, /* 518 */
  {    80,      7,     12,      0,      0,  18432,    140, }, /* 519 */
  {    80,     12,      3,      0,      0,  26624,    100, }, /* 520 */
  {    80,     10,      5,      0,      0,  18432,    144, }, /* 521 */
  {    80,     12,      3,      0,      0,  26624,    130, }, /* 522 */
  {    80,     12,      3,      0,      0,  26624,     96, }, /* 523 */
  {    80,     12,      3,      0,      0,  26624,    146, }, /* 524 */
  {    80,     21,     12,      0,      0,  18432,    106, }, /* 525 */
  {    80,      6,     12,      0,      0,  18432,    142, }, /* 526 */
  {    80,     21,     12,      0,      0,  18432,     68, }, /* 527 */
  {    80,     23,     12,      0,      0,  14336,     68, }, /* 528 */
  {    80,     13,     12,      0,      0,  18432,    138, }, /* 529 */
  {    80,     15,     12,      0,      0,  28672,     68, }, /* 530 */
  {    19,     21,     12,      0,      0,  28672,     68, }, /* 531 */
  {    69,     21,     12,      0,      0,  28777,    106, }, /* 532 */
  {    69,     21,     12,      0,      0,  28777,    124, }, /* 533 */
  {    19,     21,     12,      0,      0,  28672,    106, }, /* 534 */
  {    19,     17,     12,      0,      0,  28672,    126, }, /* 535 */
  {    19,     21,     12,      0,      0,  28672,    124, }, /* 536 */
  {    19,     21,     12,      0,      0,  28672,    178, }, /* 537 */
  {    19,     12,      3,      0,      0,  26624,    180, }, /* 538 */
  {    19,      1,      2,      0,      0,   6144,     66, }, /* 539 */
  {    19,     13,     12,      0,      0,  18432,    138, }, /* 540 */
  {    19,      7,     12,      0,      0,  18432,     82, }, /* 541 */
  {    19,      6,     12,      0,      0,  18432,    136, }, /* 542 */
  {    19,     12,      3,      0,      0,  26624,    182, }, /* 543 */
  {    19,     12,      3,      0,      0,  26624,    130, }, /* 544 */
  {    29,      7,     12,      0,      0,  18432,     82, }, /* 545 */
  {    29,     12,      3,      0,      0,  26624,    130, }, /* 546 */
  {    29,     10,      5,      0,      0,  18432,    144, }, /* 547 */
  {    29,     12,      3,      0,      0,  26624,     96, }, /* 548 */
  {    29,     26,     12,      0,      0,  28672,     68, }, /* 549 */
  {    29,     21,     12,      0,      0,  28672,    124, }, /* 550 */
  {    29,     13,     12,      0,      0,  18432,    138, }, /* 551 */
  {    30,      7,     12,      0,      0,  18432,     82, }, /* 552 */
  {    89,      7,     12,      0,      0,  18432,     82, }, /* 553 */
  {    89,      7,     12,      0,      0,  18432,    156, }, /* 554 */
  {    89,     13,     12,      0,      0,  18432,    138, }, /* 555 */
  {    89,     15,     12,      0,      0,  18432,    138, }, /* 556 */
  {    89,     26,     12,      0,      0,  28672,     68, }, /* 557 */
  {    80,     26,     12,      0,      0,  28672,     68, }, /* 558 */
  {    33,      7,     12,      0,      0,  18432,     82, }, /* 559 */
  {    33,     12,      3,      0,      0,  26624,    130, }, /* 560 */
  {    33,     10,      5,      0,      0,  18432,    144, }, /* 561 */
  {    33,     21,     12,      0,      0,  18432,     68, }, /* 562 */
  {   106,      7,     12,      0,      0,  18432,     82, }, /* 563 */
  {   106,     10,      5,      0,      0,  18432,    144, }, /* 564 */
  {   106,     12,      3,      0,      0,  26624,    130, }, /* 565 */
  {   106,     12,      3,      0,      0,  26624,    184, }, /* 566 */
  {   106,     10,     12,      0,      0,  18432,    144, }, /* 567 */
  {   106,     12,      3,      0,      0,  26624,     96, }, /* 568 */
  {   106,     13,     12,      0,      0,  18432,    138, }, /* 569 */
  {   106,     21,     12,      0,      0,  18432,     68, }, /* 570 */
  {   106,      6,     12,      0,      0,  18432,    136, }, /* 571 */
  {   106,     21,     12,      0,      0,  18432,    124, }, /* 572 */
  {    84,     11,      3,      0,      0,  26624,    186, }, /* 573 */
  {    84,     12,      3,      0,      0,  26624,    130, }, /* 574 */
  {    93,     12,      3,      0,      0,  26624,    130, }, /* 575 */
  {    93,     10,      5,      0,      0,  18432,    144, }, /* 576 */
  {    93,      7,     12,      0,      0,  18432,     82, }, /* 577 */
  {    93,     12,      3,      0,      0,  26624,     96, }, /* 578 */
  {    93,     10,      3,      0,      0,  18432,    148, }, /* 579 */
  {    93,     10,      5,      0,      0,  18432,    174, }, /* 580 */
  {    93,     13,     12,      0,      0,  18432,    138, }, /* 581 */
  {    93,     21,     12,      0,      0,  18432,    124, }, /* 582 */
  {    93,     21,     12,      0,      0,  18432,     68, }, /* 583 */
  {    93,     21,     12,      0,      0,  18432,    106, }, /* 584 */
  {    93,     26,     12,      0,      0,  18432,     68, }, /* 585 */
  {    96,     12,      3,      0,      0,  26624,    130, }, /* 586 */
  {    96,     10,      5,      0,      0,  18432,    144, }, /* 587 */
  {    96,      7,     12,      0,      0,  18432,     82, }, /* 588 */
  {    96,     10,      5,      0,      0,  18432,    174, }, /* 589 */
  {    96,     12,      3,      0,      0,  26624,    146, }, /* 590 */
  {    96,     13,     12,      0,      0,  18432,    138, }, /* 591 */
  {   119,      7,     12,      0,      0,  18432,     82, }, /* 592 */
  {   119,     12,      3,      0,      0,  26624,    102, }, /* 593 */
  {   119,     10,      5,      0,      0,  18432,    144, }, /* 594 */
  {   119,     12,      3,      0,      0,  26624,    130, }, /* 595 */
  {   119,     10,      5,      0,      0,  18432,    176, }, /* 596 */
  {   119,     21,     12,      0,      0,  18432,     68, }, /* 597 */
  {    97,      7,     12,      0,      0,  18432,     82, }, /* 598 */
  {    97,     10,      5,      0,      0,  18432,    144, }, /* 599 */
  {    97,     12,      3,      0,      0,  26624,    130, }, /* 600 */
  {    97,     12,      3,      0,      0,  26624,    188, }, /* 601 */
  {    97,     12,      3,      0,      0,  26624,     96, }, /* 602 */
  {    97,     21,     12,      0,      0,  18432,    124, }, /* 603 */
  {    97,     21,     12,      0,      0,  18432,    106, }, /* 604 */
  {    97,     13,     12,      0,      0,  18432,    138, }, /* 605 */
  {    98,     13,     12,      0,      0,  18432,    138, }, /* 606 */
  {    98,      7,     12,      0,      0,  18432,     82, }, /* 607 */
  {    98,      6,     12,      0,      0,  18432,     92, }, /* 608 */
  {    98,      6,     12,      0,      0,  18432,     94, }, /* 609 */
  {    98,     21,     12,      0,      0,  18432,    124, }, /* 610 */
  {     2,      5,     12,     63,  -6222,  18432,     70, }, /* 611 */
  {     2,      5,     12,     67,  -6221,  18432,     70, }, /* 612 */
  {     2,      5,     12,     71,  -6212,  18432,     70, }, /* 613 */
  {     2,      5,     12,     75,  -6210,  18432,     70, }, /* 614 */
  {     2,      5,     12,     79,  -6210,  18432,     70, }, /* 615 */
  {     2,      5,     12,     79,  -6211,  18432,     70, }, /* 616 */
  {     2,      5,     12,     84,  -6204,  18432,     70, }, /* 617 */
  {     2,      5,     12,     88,  -6180,  18432,     70, }, /* 618 */
  {     2,      5,     12,    108,  35267,  18432,     70, }, /* 619 */
  {    17,      9,     12,      0,  -3008,  18432,     74, }, /* 620 */
  {    96,     21,     12,      0,      0,  18432,     68, }, /* 621 */
  {    84,     12,      3,      0,      0,  26762,     96, }, /* 622 */
  {    84,     12,      3,      0,      0,  26630,     96, }, /* 623 */
  {    69,     21,     12,      0,      0,  18498,    190, }, /* 624 */
  {    84,     12,      3,      0,      0,  26666,     96, }, /* 625 */
  {    84,     12,      3,      0,      0,  26696,     96, }, /* 626 */
  {    84,     12,      3,      0,      0,  26780,     96, }, /* 627 */
  {    69,     10,      5,      0,      0,  18474,    160, }, /* 628 */
  {    69,      7,     12,      0,      0,  18501,     82, }, /* 629 */
  {    69,      7,     12,      0,      0,  18474,     82, }, /* 630 */
  {    69,      7,     12,      0,      0,  18438,     82, }, /* 631 */
  {    69,      7,     12,      0,      0,  18594,     82, }, /* 632 */
  {    69,      7,     12,      0,      0,  18498,     82, }, /* 633 */
  {    84,     12,      3,      0,      0,  26750,     96, }, /* 634 */
  {    69,     10,      5,      0,      0,  18435,    160, }, /* 635 */
  {    84,     12,      3,      0,      0,  26690,     96, }, /* 636 */
  {    69,      7,     12,      0,      0,  18453,     82, }, /* 637 */
  {     2,      5,     12,      0,      0,  18432,     60, }, /* 638 */
  {     1,      6,     12,      0,      0,  18432,     88, }, /* 639 */
  {     2,      6,     12,      0,      0,  18432,    168, }, /* 640 */
  {     0,      5,     12,      0,  35332,  18432,     76, }, /* 641 */
  {     0,      5,     12,      0,   3814,  18432,     76, }, /* 642 */
  {     0,      5,     12,      0,  35384,  18432,     76, }, /* 643 */
  {     0,      5,     12,      0,      0,  18432,    192, }, /* 644 */
  {     0,      6,     12,      0,      0,  18432,    168, }, /* 645 */
  {     0,      6,     12,      0,      0,  18432,    194, }, /* 646 */
  {     1,      6,     12,      0,      0,  18432,    168, }, /* 647 */
  {    84,     12,      3,      0,      0,  26636,    102, }, /* 648 */
  {    84,     12,      3,      0,      0,  26687,     96, }, /* 649 */
  {    84,     12,      3,      0,      0,  26648,     96, }, /* 650 */
  {     0,      9,     12,     92,      1,  18432,     74, }, /* 651 */
  {     0,      5,     12,     92,     -1,  18432,     76, }, /* 652 */
  {     0,      5,     12,      0,      0,  18432,     70, }, /* 653 */
  {     0,      5,     12,     92,    -58,  18432,     70, }, /* 654 */
  {     0,      9,     12,      0,  -7615,  18432,     74, }, /* 655 */
  {     1,      5,     12,      0,      8,  18432,     76, }, /* 656 */
  {     1,      9,     12,      0,     -8,  18432,     74, }, /* 657 */
  {     1,      5,     12,      0,     74,  18432,     76, }, /* 658 */
  {     1,      5,     12,      0,     86,  18432,     76, }, /* 659 */
  {     1,      5,     12,      0,    100,  18432,     76, }, /* 660 */
  {     1,      5,     12,      0,    128,  18432,     76, }, /* 661 */
  {     1,      5,     12,      0,    112,  18432,     76, }, /* 662 */
  {     1,      5,     12,      0,    126,  18432,     76, }, /* 663 */
  {     1,      5,     12,      0,      8,  18432,     70, }, /* 664 */
  {     1,      8,     12,      0,     -8,  18432,     86, }, /* 665 */
  {     1,      5,     12,      0,      0,  18432,     70, }, /* 666 */
  {     1,      5,     12,      0,      9,  18432,     70, }, /* 667 */
  {     1,      9,     12,      0,    -74,  18432,     74, }, /* 668 */
  {     1,      8,     12,      0,     -9,  18432,     86, }, /* 669 */
  {     1,      5,     12,     21,  -7173,  18432,     76, }, /* 670 */
  {     1,      9,     12,      0,    -86,  18432,     74, }, /* 671 */
  {     1,      9,     12,      0,   -100,  18432,     74, }, /* 672 */
  {     1,      9,     12,      0,   -112,  18432,     74, }, /* 673 */
  {     1,      9,     12,      0,   -128,  18432,     74, }, /* 674 */
  {     1,      9,     12,      0,   -126,  18432,     74, }, /* 675 */
  {    69,     29,     12,      0,      0,  45056,     52, }, /* 676 */
  {    84,      1,      3,      0,      0,   6144,    196, }, /* 677 */
  {    84,      1,     13,      0,      0,   6144,    198, }, /* 678 */
  {    69,      1,      2,      0,      0,  18432,    200, }, /* 679 */
  {    69,      1,      2,      0,      0,  34816,    200, }, /* 680 */
  {    69,     17,     12,      0,      0,  28672,    202, }, /* 681 */
  {    69,     21,     12,      0,      0,  28672,     64, }, /* 682 */
  {    69,     20,     12,      0,      0,  28672,    204, }, /* 683 */
  {    69,     19,     12,      0,      0,  28672,    204, }, /* 684 */
  {    69,     22,     12,      0,      0,  28672,    206, }, /* 685 */
  {    69,     20,     12,      0,      0,  28672,    206, }, /* 686 */
  {    69,     19,     12,      0,      0,  28672,    206, }, /* 687 */
  {    69,     21,     12,      0,      0,  28672,    208, }, /* 688 */
  {    69,     27,      2,      0,      0,  45056,     50, }, /* 689 */
  {    69,     28,      2,      0,      0,   4096,     50, }, /* 690 */
  {    69,      1,      2,      0,      0,  20480,    134, }, /* 691 */
  {    69,      1,      2,      0,      0,  36864,    134, }, /* 692 */
  {    69,      1,      2,      0,      0,  30720,    134, }, /* 693 */
  {    69,      1,      2,      0,      0,  24576,    134, }, /* 694 */
  {    69,      1,      2,      0,      0,  40960,    134, }, /* 695 */
  {    69,     29,     12,      0,      0,   8291,     52, }, /* 696 */
  {    69,     21,     12,      0,      0,  14336,     54, }, /* 697 */
  {    69,     21,     12,      0,      0,  14336,     64, }, /* 698 */
  {    69,     21,     14,      0,      0,  28672,    210, }, /* 699 */
  {    69,     21,     12,      0,      0,  28672,    212, }, /* 700 */
  {    69,     16,     12,      0,      0,  28672,    138, }, /* 701 */
  {    69,     16,     12,      0,      0,  28672,    214, }, /* 702 */
  {    69,     25,     12,      0,      0,   8192,     64, }, /* 703 */
  {    69,     22,     12,      0,      0,  28672,    216, }, /* 704 */
  {    69,     18,     12,      0,      0,  28672,    216, }, /* 705 */
  {    69,     21,     12,      0,      0,  28672,    202, }, /* 706 */
  {    69,      1,      2,      0,      0,   6144,    218, }, /* 707 */
  {    68,      2,      2,      0,      0,   6144,    220, }, /* 708 */
  {    69,      1,      2,      0,      0,  22528,    134, }, /* 709 */
  {    69,      1,      2,      0,      0,  38912,    134, }, /* 710 */
  {    69,      1,      2,      0,      0,  16384,    134, }, /* 711 */
  {    69,      1,      2,      0,      0,  32768,    134, }, /* 712 */
  {    69,      1,      2,      0,      0,   6144,    222, }, /* 713 */
  {    69,     25,     12,      0,      0,  12288,    118, }, /* 714 */
  {    69,     25,     12,      0,      0,  12288,    224, }, /* 715 */
  {    69,     25,     12,      0,      0,  28672,    118, }, /* 716 */
  {    69,     22,     12,      0,      0,  28672,    226, }, /* 717 */
  {    69,     18,     12,      0,      0,  28672,    226, }, /* 718 */
  {    68,      2,     12,      0,      0,  14336,      0, }, /* 719 */
  {    84,     12,      3,      0,      0,  26624,    228, }, /* 720 */
  {    84,     11,      3,      0,      0,  26624,    120, }, /* 721 */
  {    84,     11,      3,      0,      0,  26624,    230, }, /* 722 */
  {    84,     12,      3,      0,      0,  26753,    102, }, /* 723 */
  {    69,     26,     12,      0,      0,  28672,     68, }, /* 724 */
  {    69,      9,     12,      0,      0,  18432,    112, }, /* 725 */
  {    69,      5,     12,      0,      0,  18432,    232, }, /* 726 */
  {    69,     25,     12,      0,      0,  28672,    234, }, /* 727 */
  {    69,     26,     14,      0,      0,  28672,    236, }, /* 728 */
  {     1,      9,     12,     96,  -7517,  18432,     74, }, /* 729 */
  {    69,     26,     12,      0,      0,  28672,    118, }, /* 730 */
  {     0,      9,     12,    100,      0,  18432,     74, }, /* 731 */
  {     0,      9,     12,    104,  -8262,  18432,     74, }, /* 732 */
  {    69,     26,     12,      0,      0,  14336,    238, }, /* 733 */
  {     0,      9,     12,      0,     28,  18432,     74, }, /* 734 */
  {    69,      7,     12,      0,      0,  18432,    240, }, /* 735 */
  {    69,      5,     14,      0,      0,  18432,    242, }, /* 736 */
  {    69,      5,     12,      0,      0,  18432,    244, }, /* 737 */
  {     0,      5,     12,      0,    -28,  18432,     76, }, /* 738 */
  {     0,     14,     12,      0,     16,  18432,     74, }, /* 739 */
  {     0,     14,     12,      0,    -16,  18432,     76, }, /* 740 */
  {     0,     14,     12,      0,      0,  18432,     82, }, /* 741 */
  {    69,     25,     14,      0,      0,  28672,    246, }, /* 742 */
  {    69,     26,     14,      0,      0,  28672,    246, }, /* 743 */
  {    69,     26,     12,      0,      0,  28672,     64, }, /* 744 */
  {    69,     25,     12,      0,      0,  28672,    248, }, /* 745 */
  {    69,     25,     12,      0,      0,  12288,    250, }, /* 746 */
  {    69,     22,     12,      0,      0,  28672,    248, }, /* 747 */
  {    69,     18,     12,      0,      0,  28672,    248, }, /* 748 */
  {    69,     26,     14,      0,      0,  28672,    252, }, /* 749 */
  {    69,     22,     12,      0,      0,  28672,    254, }, /* 750 */
  {    69,     18,     12,      0,      0,  28672,    254, }, /* 751 */
  {    69,     26,     12,      0,      0,  18432,     54, }, /* 752 */
  {    69,     26,     14,      0,      0,  28672,    256, }, /* 753 */
  {    68,      2,     12,      0,      0,  18432,    258, }, /* 754 */
  {    69,     26,     12,      0,     26,  18432,    260, }, /* 755 */
  {    69,     26,     14,      0,     26,  18432,    262, }, /* 756 */
  {    69,     26,     12,      0,    -26,  18432,    264, }, /* 757 */
  {    69,     25,     14,      0,      0,  28672,    266, }, /* 758 */
  {    69,     26,     14,      0,      0,  28672,    268, }, /* 759 */
  {    69,     26,     14,      0,      0,  28672,    270, }, /* 760 */
  {    69,     25,     14,      0,      0,  28672,    268, }, /* 761 */
  {    69,     26,     14,      0,      0,  18432,    256, }, /* 762 */
  {    69,     26,     14,      0,      0,  28672,    272, }, /* 763 */
  {    88,     26,     12,      0,      0,  18432,     54, }, /* 764 */
  {    69,     26,     12,      0,      0,  28672,    216, }, /* 765 */
  {    35,      9,     12,      0,     48,  18432,     74, }, /* 766 */
  {    35,      5,     12,      0,    -48,  18432,     76, }, /* 767 */
  {     0,      9,     12,      0, -10743,  18432,     74, }, /* 768 */
  {     0,      9,     12,      0,  -3814,  18432,     74, }, /* 769 */
  {     0,      9,     12,      0, -10727,  18432,     74, }, /* 770 */
  {     0,      5,     12,      0, -10795,  18432,     76, }, /* 771 */
  {     0,      5,     12,      0, -10792,  18432,     76, }, /* 772 */
  {     0,      9,     12,      0, -10780,  18432,     74, }, /* 773 */
  {     0,      9,     12,      0, -10749,  18432,     74, }, /* 774 */
  {     0,      9,     12,      0, -10783,  18432,     74, }, /* 775 */
  {     0,      9,     12,      0, -10782,  18432,     74, }, /* 776 */
  {     0,      9,     12,      0, -10815,  18432,     74, }, /* 777 */
  {    34,      5,     12,      0,      0,  18432,     60, }, /* 778 */
  {    34,     26,     12,      0,      0,  28672,     68, }, /* 779 */
  {    34,     12,      3,      0,      0,  26624,     96, }, /* 780 */
  {    34,     21,     12,      0,      0,  28672,     68, }, /* 781 */
  {    34,     15,     12,      0,      0,  28672,     68, }, /* 782 */
  {    17,      5,     12,      0,  -7264,  18432,     76, }, /* 783 */
  {    90,      7,     12,      0,      0,  18432,     82, }, /* 784 */
  {    90,      6,     12,      0,      0,  18432,    142, }, /* 785 */
  {    90,     21,     12,      0,      0,  18432,     68, }, /* 786 */
  {    90,     12,      3,      0,      0,  26624,    184, }, /* 787 */
  {     2,     12,      3,      0,      0,  26624,    130, }, /* 788 */
  {    69,     20,     12,      0,      0,  28672,    216, }, /* 789 */
  {    69,     19,     12,      0,      0,  28672,    216, }, /* 790 */
  {    69,      6,     12,      0,      0,  28672,    274, }, /* 791 */
  {    69,     21,     12,      0,      0,  28672,    276, }, /* 792 */
  {    69,     21,     12,      0,      0,  28726,     54, }, /* 793 */
  {    23,     26,     12,      0,      0,  28672,    278, }, /* 794 */
  {    69,     26,     12,      0,      0,  28672,    280, }, /* 795 */
  {    69,     26,     12,      0,      0,  28672,    282, }, /* 796 */
  {    69,     21,     12,      0,      0,  28825,    276, }, /* 797 */
  {    69,     21,     12,      0,      0,  28825,    212, }, /* 798 */
  {    69,     21,     12,      0,      0,  28819,     54, }, /* 799 */
  {    23,      6,     12,      0,      0,  18432,    136, }, /* 800 */
  {    69,      7,     12,      0,      0,  18447,    284, }, /* 801 */
  {    23,     14,     12,      0,      0,  18432,    284, }, /* 802 */
  {    69,     22,     12,      0,      0,  28825,    216, }, /* 803 */
  {    69,     18,     12,      0,      0,  28825,    216, }, /* 804 */
  {    69,     22,     12,      0,      0,  28825,     62, }, /* 805 */
  {    69,     18,     12,      0,      0,  28825,     62, }, /* 806 */
  {    69,     26,     12,      0,      0,  28819,     54, }, /* 807 */
  {    69,     17,     12,      0,      0,  28819,    202, }, /* 808 */
  {    69,     22,     12,      0,      0,  28819,    206, }, /* 809 */
  {    69,     18,     12,      0,      0,  28819,    206, }, /* 810 */
  {    84,     12,      3,      0,      0,  26669,     96, }, /* 811 */
  {    18,     10,      3,      0,      0,  18432,    286, }, /* 812 */
  {    69,     17,     14,      0,      0,  28819,    288, }, /* 813 */
  {    69,      6,     12,      0,      0,  18525,    136, }, /* 814 */
  {    69,     26,     12,      0,      0,  28819,     68, }, /* 815 */
  {    23,      6,     12,      0,      0,  18432,    142, }, /* 816 */
  {    69,      7,     12,      0,      0,  18564,     82, }, /* 817 */
  {    69,     21,     14,      0,      0,  28804,    236, }, /* 818 */
  {    69,     26,     12,      0,      0,  28687,     68, }, /* 819 */
  {    20,      7,     12,      0,      0,  18432,     82, }, /* 820 */
  {    84,     12,      3,      0,      0,  26717,     96, }, /* 821 */
  {    69,     24,     12,      0,      0,  28765,    290, }, /* 822 */
  {    20,      6,     12,      0,      0,  18432,    136, }, /* 823 */
  {    69,     17,     12,      0,      0,  28765,    126, }, /* 824 */
  {    21,      7,     12,      0,      0,  18432,     82, }, /* 825 */
  {    69,     21,     12,      0,      0,  28825,     68, }, /* 826 */
  {    69,      6,     12,      0,      0,  18525,     94, }, /* 827 */
  {    21,      6,     12,      0,      0,  18432,    136, }, /* 828 */
  {    22,      7,     12,      0,      0,  18432,     82, }, /* 829 */
  {    18,      7,     12,      0,      0,  18432,     82, }, /* 830 */
  {    18,      7,     12,      0,      0,  18432,    170, }, /* 831 */
  {    69,     26,     12,      0,      0,  18447,     68, }, /* 832 */
  {    69,     15,     12,      0,      0,  18447,     68, }, /* 833 */
  {    18,     26,     12,      0,      0,  18432,     68, }, /* 834 */
  {    18,     26,     12,      0,      0,  28672,     68, }, /* 835 */
  {    69,     15,     12,      0,      0,  18432,     68, }, /* 836 */
  {    69,     26,     14,      0,      0,  18447,    236, }, /* 837 */
  {    21,     26,     12,      0,      0,  18432,     68, }, /* 838 */
  {    23,      7,     12,      0,      0,  18432,    292, }, /* 839 */
  {    24,      7,     12,      0,      0,  18432,     82, }, /* 840 */
  {    24,      6,     12,      0,      0,  18432,    136, }, /* 841 */
  {    24,     26,     12,      0,      0,  28672,     68, }, /* 842 */
  {   111,      7,     12,      0,      0,  18432,     82, }, /* 843 */
  {   111,      6,     12,      0,      0,  18432,    142, }, /* 844 */
  {   111,     21,     12,      0,      0,  18432,    106, }, /* 845 */
  {   111,     21,     12,      0,      0,  18432,    124, }, /* 846 */
  {    99,      7,     12,      0,      0,  18432,     82, }, /* 847 */
  {    99,      6,     12,      0,      0,  18432,    136, }, /* 848 */
  {    99,     21,     12,      0,      0,  28672,    106, }, /* 849 */
  {    99,     21,     12,      0,      0,  28672,    124, }, /* 850 */
  {    99,     13,     12,      0,      0,  18432,    138, }, /* 851 */
  {     2,      9,     12,    108,      1,  18432,     74, }, /* 852 */
  {     2,      5,     12,    108, -35267,  18432,     76, }, /* 853 */
  {     2,      7,     12,      0,      0,  18432,     82, }, /* 854 */
  {     2,     21,     12,      0,      0,  28672,     68, }, /* 855 */
  {     2,     12,      3,      0,      0,  26624,     96, }, /* 856 */
  {     2,      6,     12,      0,      0,  28672,     92, }, /* 857 */
  {     2,      6,     12,      0,      0,  18432,     88, }, /* 858 */
  {   112,      7,     12,      0,      0,  18432,     82, }, /* 859 */
  {   112,     14,     12,      0,      0,  18432,     82, }, /* 860 */
  {   112,     12,      3,      0,      0,  26624,     96, }, /* 861 */
  {   112,     21,     12,      0,      0,  18432,     68, }, /* 862 */
  {   112,     21,     12,      0,      0,  18432,    124, }, /* 863 */
  {   112,     21,     12,      0,      0,  18432,    106, }, /* 864 */
  {    69,     24,     12,      0,      0,  28762,     56, }, /* 865 */
  {     0,      9,     12,      0, -35332,  18432,     74, }, /* 866 */
  {    69,     24,     12,      0,      0,  18432,     56, }, /* 867 */
  {     0,      9,     12,      0, -42280,  18432,     74, }, /* 868 */
  {     0,      5,     12,      0,     48,  18432,     76, }, /* 869 */
  {     0,      9,     12,      0, -42308,  18432,     74, }, /* 870 */
  {     0,      9,     12,      0, -42319,  18432,     74, }, /* 871 */
  {     0,      9,     12,      0, -42315,  18432,     74, }, /* 872 */
  {     0,      9,     12,      0, -42305,  18432,     74, }, /* 873 */
  {     0,      9,     12,      0, -42258,  18432,     74, }, /* 874 */
  {     0,      9,     12,      0, -42282,  18432,     74, }, /* 875 */
  {     0,      9,     12,      0, -42261,  18432,     74, }, /* 876 */
  {     0,      9,     12,      0,    928,  18432,     74, }, /* 877 */
  {     0,      9,     12,      0,    -48,  18432,     74, }, /* 878 */
  {     0,      9,     12,      0, -42307,  18432,     74, }, /* 879 */
  {     0,      9,     12,      0, -35384,  18432,     74, }, /* 880 */
  {    36,      7,     12,      0,      0,  18432,     82, }, /* 881 */
  {    36,     12,      3,      0,      0,  26624,    130, }, /* 882 */
  {    36,     12,      3,      0,      0,  26624,    184, }, /* 883 */
  {    36,     10,      5,      0,      0,  18432,    144, }, /* 884 */
  {    36,     26,     12,      0,      0,  28672,     68, }, /* 885 */
  {    69,     15,     12,      0,      0,  18612,     68, }, /* 886 */
  {    69,     15,     12,      0,      0,  18609,     68, }, /* 887 */
  {    69,     26,     12,      0,      0,  18600,     68, }, /* 888 */
  {    69,     23,     12,      0,      0,  14504,     68, }, /* 889 */
  {    69,     26,     12,      0,      0,  14504,     68, }, /* 890 */
  {    37,      7,     12,      0,      0,  18432,     82, }, /* 891 */
  {    37,     21,     12,      0,      0,  28672,     68, }, /* 892 */
  {    37,     21,     12,      0,      0,  28672,    124, }, /* 893 */
  {   100,     10,      5,      0,      0,  18432,    144, }, /* 894 */
  {   100,      7,     12,      0,      0,  18432,     82, }, /* 895 */
  {   100,     12,      3,      0,      0,  26624,    146, }, /* 896 */
  {   100,     12,      3,      0,      0,  26624,    130, }, /* 897 */
  {   100,     21,     12,      0,      0,  18432,    124, }, /* 898 */
  {   100,     13,     12,      0,      0,  18432,    138, }, /* 899 */
  {     6,     12,      3,      0,      0,  26666,     96, }, /* 900 */
  {     6,      7,     12,      0,      0,  18507,     82, }, /* 901 */
  {    39,     13,     12,      0,      0,  18432,    138, }, /* 902 */
  {    39,      7,     12,      0,      0,  18432,     82, }, /* 903 */
  {    39,     12,      3,      0,      0,  26624,    130, }, /* 904 */
  {    39,     12,      3,      0,      0,  26624,     96, }, /* 905 */
  {    69,     21,     12,      0,      0,  18567,    190, }, /* 906 */
  {    39,     21,     12,      0,      0,  18432,    124, }, /* 907 */
  {   101,      7,     12,      0,      0,  18432,     82, }, /* 908 */
  {   101,     12,      3,      0,      0,  26624,    130, }, /* 909 */
  {   101,     10,      5,      0,      0,  18432,    144, }, /* 910 */
  {   101,     10,      5,      0,      0,  18432,    174, }, /* 911 */
  {   101,     21,     12,      0,      0,  18432,     68, }, /* 912 */
  {    40,     12,      3,      0,      0,  26624,    130, }, /* 913 */
  {    40,     10,      5,      0,      0,  18432,    144, }, /* 914 */
  {    40,      7,     12,      0,      0,  18432,     82, }, /* 915 */
  {    40,     12,      3,      0,      0,  26624,     96, }, /* 916 */
  {    40,     10,      5,      0,      0,  18432,    174, }, /* 917 */
  {    40,     21,     12,      0,      0,  18432,     68, }, /* 918 */
  {    40,     21,     12,      0,      0,  18432,    106, }, /* 919 */
  {    40,     21,     12,      0,      0,  18432,    124, }, /* 920 */
  {    69,      6,     12,      0,      0,  18480,    136, }, /* 921 */
  {    40,     13,     12,      0,      0,  18432,    138, }, /* 922 */
  {    16,      6,     12,      0,      0,  18432,    136, }, /* 923 */
  {   105,      7,     12,      0,      0,  18432,     82, }, /* 924 */
  {   105,     12,      3,      0,      0,  26624,    130, }, /* 925 */
  {   105,     10,      5,      0,      0,  18432,    144, }, /* 926 */
  {   105,     13,     12,      0,      0,  18432,    138, }, /* 927 */
  {   105,     21,     12,      0,      0,  18432,     68, }, /* 928 */
  {   105,     21,     12,      0,      0,  18432,    124, }, /* 929 */
  {   107,      7,     12,      0,      0,  18432,     82, }, /* 930 */
  {   107,     12,      3,      0,      0,  26624,    130, }, /* 931 */
  {   107,      7,     12,      0,      0,  18432,    156, }, /* 932 */
  {   107,     12,      3,      0,      0,  26624,     96, }, /* 933 */
  {   107,      7,     12,      0,      0,  18432,    294, }, /* 934 */
  {   107,      6,     12,      0,      0,  18432,    136, }, /* 935 */
  {   107,     21,     12,      0,      0,  18432,     68, }, /* 936 */
  {   107,     21,     12,      0,      0,  18432,    106, }, /* 937 */
  {   113,      7,     12,      0,      0,  18432,     82, }, /* 938 */
  {   113,     10,      5,      0,      0,  18432,    144, }, /* 939 */
  {   113,     12,      3,      0,      0,  26624,    130, }, /* 940 */
  {   113,     21,     12,      0,      0,  18432,    124, }, /* 941 */
  {   113,      6,     12,      0,      0,  18432,    136, }, /* 942 */
  {   113,     12,      3,      0,      0,  26624,    146, }, /* 943 */
  {     0,      5,     12,      0,   -928,  18432,     76, }, /* 944 */
  {    76,      5,     12,      0, -38864,  18432,     70, }, /* 945 */
  {   113,     10,      5,      0,      0,  18432,    160, }, /* 946 */
  {   113,     13,     12,      0,      0,  18432,    138, }, /* 947 */
  {    18,      7,      9,      0,      0,  18432,     82, }, /* 948 */
  {    18,      7,     10,      0,      0,  18432,     82, }, /* 949 */
  {    68,      4,     12,      0,      0,  18432,      0, }, /* 950 */
  {    68,      3,     12,      0,      0,  18432,      0, }, /* 951 */
  {    23,      7,     12,      0,      0,  18432,    284, }, /* 952 */
  {    71,     25,     12,      0,      0,  12288,    118, }, /* 953 */
  {     3,      7,     12,      0,      0,      0,    296, }, /* 954 */
  {    69,     18,     12,      0,      0,  28705,     54, }, /* 955 */
  {    69,     22,     12,      0,      0,  28705,     54, }, /* 956 */
  {    68,      2,     12,      0,      0,   6144,    298, }, /* 957 */
  {     3,      7,     12,      0,      0,     39,     82, }, /* 958 */
  {     3,     26,     12,      0,      0,  28711,     68, }, /* 959 */
  {    84,     12,      3,      0,      0,  26624,    180, }, /* 960 */
  {    84,     12,      3,      0,      0,  26624,    300, }, /* 961 */
  {    69,     21,     12,      0,      0,  28672,     68, }, /* 962 */
  {    69,     21,     12,      0,      0,  28672,    122, }, /* 963 */
  {    69,     22,     12,      0,      0,  28672,     68, }, /* 964 */
  {    69,     18,     12,      0,      0,  28672,     68, }, /* 965 */
  {    69,     17,     12,      0,      0,  28672,    126, }, /* 966 */
  {    69,     22,     12,      0,      0,  28672,    302, }, /* 967 */
  {    69,     18,     12,      0,      0,  28672,    302, }, /* 968 */
  {    69,     21,     12,      0,      0,   8192,    106, }, /* 969 */
  {    69,     21,     12,      0,      0,   8192,    304, }, /* 970 */
  {    69,     21,     12,      0,      0,   8192,    306, }, /* 971 */
  {    69,     21,     12,      0,      0,  28672,    124, }, /* 972 */
  {    69,     22,     12,      0,      0,  28672,    158, }, /* 973 */
  {    69,     18,     12,      0,      0,  28672,    158, }, /* 974 */
  {    69,     21,     12,      0,      0,  14336,     68, }, /* 975 */
  {    69,     21,     12,      0,      0,  28672,    118, }, /* 976 */
  {    69,     17,     12,      0,      0,  12288,    224, }, /* 977 */
  {    69,     25,     12,      0,      0,  28672,    226, }, /* 978 */
  {    69,     21,     12,      0,      0,  28672,    302, }, /* 979 */
  {    69,     21,     12,      0,      0,  28672,    308, }, /* 980 */
  {    69,     17,     12,      0,      0,  12288,    126, }, /* 981 */
  {    69,     21,     12,      0,      0,   8192,     68, }, /* 982 */
  {    69,     13,     12,      0,      0,  10240,    310, }, /* 983 */
  {     0,      9,     12,      0,     32,  18432,    312, }, /* 984 */
  {    69,     24,     12,      0,      0,  28672,    314, }, /* 985 */
  {     0,      5,     12,      0,    -32,  18432,    316, }, /* 986 */
  {    69,     21,     12,      0,      0,  28825,    124, }, /* 987 */
  {    69,     22,     12,      0,      0,  28825,    318, }, /* 988 */
  {    69,     18,     12,      0,      0,  28825,    318, }, /* 989 */
  {    69,     21,     12,      0,      0,  28825,    106, }, /* 990 */
  {    69,      6,      3,      0,      0,  18525,    320, }, /* 991 */
  {    69,      1,      2,      0,      0,  28672,    322, }, /* 992 */
  {    31,      7,     12,      0,      0,  18432,     82, }, /* 993 */
  {    69,     21,     12,      0,      0,  18552,     68, }, /* 994 */
  {    69,     21,     12,      0,      0,  28792,     68, }, /* 995 */
  {    69,     21,     12,      0,      0,  18483,     68, }, /* 996 */
  {    69,     15,     12,      0,      0,  18555,     68, }, /* 997 */
  {    69,     26,     12,      0,      0,  18483,     68, }, /* 998 */
  {     1,     14,     12,      0,      0,  28672,     82, }, /* 999 */
  {     1,     15,     12,      0,      0,  28672,     68, }, /* 1000 */
  {     1,     26,     12,      0,      0,  28672,     68, }, /* 1001 */
  {     1,     26,     12,      0,      0,  18432,     68, }, /* 1002 */
  {   102,      7,     12,      0,      0,  18432,     82, }, /* 1003 */
  {   103,      7,     12,      0,      0,  18432,     82, }, /* 1004 */
  {    84,     12,      3,      0,      0,  26651,     96, }, /* 1005 */
  {    69,     15,     12,      0,      0,  10267,     68, }, /* 1006 */
  {    81,      7,     12,      0,      0,  18432,     82, }, /* 1007 */
  {    81,     15,     12,      0,      0,  18432,     68, }, /* 1008 */
  {    82,      7,     12,      0,      0,  18432,     82, }, /* 1009 */
  {    82,     14,     12,      0,      0,  18432,     82, }, /* 1010 */
  {    53,      7,     12,      0,      0,  18432,     82, }, /* 1011 */
  {    53,     12,      3,      0,      0,  26624,    130, }, /* 1012 */
  {    85,      7,     12,      0,      0,  18432,     82, }, /* 1013 */
  {    85,     21,     12,      0,      0,  18432,    106, }, /* 1014 */
  {    91,      7,     12,      0,      0,  18432,     82, }, /* 1015 */
  {    91,     21,     12,      0,      0,  18432,    106, }, /* 1016 */
  {    91,     14,     12,      0,      0,  18432,     82, }, /* 1017 */
  {    83,      9,     12,      0,     40,  18432,     74, }, /* 1018 */
  {    83,      5,     12,      0,    -40,  18432,     76, }, /* 1019 */
  {    86,      7,     12,      0,      0,  18432,     82, }, /* 1020 */
  {    87,      7,     12,      0,      0,  18432,     82, }, /* 1021 */
  {    87,     13,     12,      0,      0,  18432,    138, }, /* 1022 */
  {   145,      9,     12,      0,     40,  18432,     74, }, /* 1023 */
  {   145,      5,     12,      0,    -40,  18432,     76, }, /* 1024 */
  {   127,      7,     12,      0,      0,  18432,     82, }, /* 1025 */
  {   125,      7,     12,      0,      0,  18432,     82, }, /* 1026 */
  {   125,     21,     12,      0,      0,  18432,     68, }, /* 1027 */
  {   161,      9,     12,      0,     39,  18432,     74, }, /* 1028 */
  {   161,      5,     12,      0,    -39,  18432,     76, }, /* 1029 */
  {    49,      7,     12,      0,      0,  18432,     82, }, /* 1030 */
  {     0,      6,     12,      0,      0,  18432,     94, }, /* 1031 */
  {    32,      7,     12,      0,      0,  34816,     82, }, /* 1032 */
  {   114,      7,     12,      0,      0,  34816,     82, }, /* 1033 */
  {   114,     21,     12,      0,      0,  34816,    106, }, /* 1034 */
  {   114,     15,     12,      0,      0,  34816,     68, }, /* 1035 */
  {   133,      7,     12,      0,      0,  34816,     82, }, /* 1036 */
  {   133,     26,     12,      0,      0,  34816,     68, }, /* 1037 */
  {   133,     15,     12,      0,      0,  34816,     68, }, /* 1038 */
  {   132,      7,     12,      0,      0,  34816,     82, }, /* 1039 */
  {   132,     15,     12,      0,      0,  34816,     68, }, /* 1040 */
  {   139,      7,     12,      0,      0,  34816,     82, }, /* 1041 */
  {   139,     15,     12,      0,      0,  34816,     68, }, /* 1042 */
  {    95,      7,     12,      0,      0,  34816,     82, }, /* 1043 */
  {    95,     15,     12,      0,      0,  34816,     68, }, /* 1044 */
  {    95,     21,     12,      0,      0,  28672,    106, }, /* 1045 */
  {   104,      7,     12,      0,      0,  34816,     82, }, /* 1046 */
  {   104,     21,     12,      0,      0,  34816,     68, }, /* 1047 */
  {   122,      7,     12,      0,      0,  34816,     82, }, /* 1048 */
  {   121,      7,     12,      0,      0,  34816,     82, }, /* 1049 */
  {   121,     15,     12,      0,      0,  34816,     68, }, /* 1050 */
  {    92,      7,     12,      0,      0,  34816,     82, }, /* 1051 */
  {    92,     12,      3,      0,      0,  26624,    130, }, /* 1052 */
  {    92,     12,      3,      0,      0,  26624,    102, }, /* 1053 */
  {    92,     12,      3,      0,      0,  26624,    184, }, /* 1054 */
  {    92,     15,     12,      0,      0,  34816,     68, }, /* 1055 */
  {    92,     21,     12,      0,      0,  34816,     68, }, /* 1056 */
  {    92,     21,     12,      0,      0,  34816,    124, }, /* 1057 */
  {   115,      7,     12,      0,      0,  34816,     82, }, /* 1058 */
  {   115,     15,     12,      0,      0,  34816,     68, }, /* 1059 */
  {   115,     21,     12,      0,      0,  34816,     68, }, /* 1060 */
  {   131,      7,     12,      0,      0,  34816,     82, }, /* 1061 */
  {   131,     15,     12,      0,      0,  34816,     68, }, /* 1062 */
  {    51,      7,     12,      0,      0,  34816,     82, }, /* 1063 */
  {    51,     26,     12,      0,      0,  34816,     68, }, /* 1064 */
  {    51,     12,      3,      0,      0,  26624,     96, }, /* 1065 */
  {    51,     15,     12,      0,      0,  34816,     68, }, /* 1066 */
  {    51,     21,     12,      0,      0,  34816,    106, }, /* 1067 */
  {    51,     21,     12,      0,      0,  34918,    106, }, /* 1068 */
  {    51,     21,     12,      0,      0,  34816,     68, }, /* 1069 */
  {   108,      7,     12,      0,      0,  34816,     82, }, /* 1070 */
  {   108,     21,     12,      0,      0,  28672,     68, }, /* 1071 */
  {   108,     21,     12,      0,      0,  28672,    106, }, /* 1072 */
  {   116,      7,     12,      0,      0,  34816,     82, }, /* 1073 */
  {   116,     15,     12,      0,      0,  34816,     68, }, /* 1074 */
  {   117,      7,     12,      0,      0,  34816,     82, }, /* 1075 */
  {   117,     15,     12,      0,      0,  34816,     68, }, /* 1076 */
  {    54,      7,     12,      0,      0,  34816,     82, }, /* 1077 */
  {    54,     21,     12,      0,      0,  34816,    106, }, /* 1078 */
  {    54,     15,     12,      0,      0,  34816,     68, }, /* 1079 */
  {   118,      7,     12,      0,      0,  34816,     82, }, /* 1080 */
  {   140,      9,     12,      0,     64,  34816,     74, }, /* 1081 */
  {   140,      5,     12,      0,    -64,  34816,     76, }, /* 1082 */
  {   140,     15,     12,      0,      0,  34816,     68, }, /* 1083 */
  {    62,      7,     12,      0,      0,      0,     82, }, /* 1084 */
  {    62,      7,     12,      0,      0,      0,    294, }, /* 1085 */
  {    62,     12,      3,      0,      0,  26624,    128, }, /* 1086 */
  {    62,     13,     12,      0,      0,   2048,    138, }, /* 1087 */
  {     3,     15,     12,      0,      0,   2048,     68, }, /* 1088 */
  {    65,      7,     12,      0,      0,  34816,     82, }, /* 1089 */
  {    65,     12,      3,      0,      0,  26624,    130, }, /* 1090 */
  {    65,     17,     12,      0,      0,  34816,    126, }, /* 1091 */
  {   152,      7,     12,      0,      0,  34816,     82, }, /* 1092 */
  {   152,     15,     12,      0,      0,  34816,     68, }, /* 1093 */
  {    63,      7,     12,      0,      0,      0,     82, }, /* 1094 */
  {    63,     12,      3,      0,      0,  26624,     96, }, /* 1095 */
  {    63,     15,     12,      0,      0,      0,     68, }, /* 1096 */
  {    63,     21,     12,      0,      0,      0,    124, }, /* 1097 */
  {    67,      7,     12,      0,      0,  34816,     82, }, /* 1098 */
  {    67,     12,      3,      0,      0,  26624,     96, }, /* 1099 */
  {    67,     21,     12,      0,      0,  34816,    124, }, /* 1100 */
  {   156,      7,     12,      0,      0,  34816,     82, }, /* 1101 */
  {   156,     15,     12,      0,      0,  34816,     68, }, /* 1102 */
  {   153,      7,     12,      0,      0,  34816,     82, }, /* 1103 */
  {   120,     10,      5,      0,      0,  18432,    144, }, /* 1104 */
  {   120,     12,      3,      0,      0,  26624,    130, }, /* 1105 */
  {   120,      7,     12,      0,      0,  18432,     82, }, /* 1106 */
  {   120,     12,      3,      0,      0,  26624,    146, }, /* 1107 */
  {   120,     21,     12,      0,      0,  18432,    124, }, /* 1108 */
  {   120,     21,     12,      0,      0,  18432,    106, }, /* 1109 */
  {   120,     15,     12,      0,      0,  28672,     68, }, /* 1110 */
  {   120,     13,     12,      0,      0,  18432,    138, }, /* 1111 */
  {   120,     12,      3,      0,      0,  26624,    184, }, /* 1112 */
  {    41,     12,      3,      0,      0,  26624,    130, }, /* 1113 */
  {    41,     10,      5,      0,      0,  18432,    144, }, /* 1114 */
  {    41,      7,     12,      0,      0,  18432,     82, }, /* 1115 */
  {    41,     12,      3,      0,      0,  26624,    146, }, /* 1116 */
  {    41,     12,      3,      0,      0,  26624,     96, }, /* 1117 */
  {    41,     21,     12,      0,      0,  18432,     68, }, /* 1118 */
  {    41,      1,      4,      0,      0,  18432,    132, }, /* 1119 */
  {    41,     21,     12,      0,      0,  18432,    124, }, /* 1120 */
  {   124,      7,     12,      0,      0,  18432,     82, }, /* 1121 */
  {   124,     13,     12,      0,      0,  18432,    138, }, /* 1122 */
  {    43,     12,      3,      0,      0,  26624,    130, }, /* 1123 */
  {    43,      7,     12,      0,      0,  18432,     82, }, /* 1124 */
  {    43,     10,      5,      0,      0,  18432,    144, }, /* 1125 */
  {    43,     12,      3,      0,      0,  26624,    146, }, /* 1126 */
  {    43,     13,     12,      0,      0,  18432,    138, }, /* 1127 */
  {    43,     21,     12,      0,      0,  18432,     68, }, /* 1128 */
  {    43,     21,     12,      0,      0,  18432,    124, }, /* 1129 */
  {    50,      7,     12,      0,      0,  18432,     82, }, /* 1130 */
  {    50,     12,      3,      0,      0,  26624,     96, }, /* 1131 */
  {    50,     21,     12,      0,      0,  18432,     68, }, /* 1132 */
  {    44,     12,      3,      0,      0,  26624,    130, }, /* 1133 */
  {    44,     10,      5,      0,      0,  18432,    144, }, /* 1134 */
  {    44,      7,     12,      0,      0,  18432,     82, }, /* 1135 */
  {    44,     10,      5,      0,      0,  18432,    174, }, /* 1136 */
  {    44,      7,      4,      0,      0,  18432,     82, }, /* 1137 */
  {    44,     21,     12,      0,      0,  18432,    124, }, /* 1138 */
  {    44,     21,     12,      0,      0,  18432,     68, }, /* 1139 */
  {    44,     12,      3,      0,      0,  26624,    102, }, /* 1140 */
  {    44,     12,      3,      0,      0,  26624,     96, }, /* 1141 */
  {    44,     13,     12,      0,      0,  18432,    138, }, /* 1142 */
  {    15,     15,     12,      0,      0,  18432,     68, }, /* 1143 */
  {    48,      7,     12,      0,      0,  18432,     82, }, /* 1144 */
  {    48,     10,      5,      0,      0,  18432,    144, }, /* 1145 */
  {    48,     12,      3,      0,      0,  26624,    130, }, /* 1146 */
  {    48,     10,      5,      0,      0,  18432,    174, }, /* 1147 */
  {    48,     12,      3,      0,      0,  26624,     96, }, /* 1148 */
  {    48,     21,     12,      0,      0,  18432,    124, }, /* 1149 */
  {    48,     21,     12,      0,      0,  18432,    106, }, /* 1150 */
  {    48,     21,     12,      0,      0,  18432,     68, }, /* 1151 */
  {    57,      7,     12,      0,      0,  18432,     82, }, /* 1152 */
  {    57,     21,     12,      0,      0,  18432,    124, }, /* 1153 */
  {    55,      7,     12,      0,      0,  18432,     82, }, /* 1154 */
  {    55,     12,      3,      0,      0,  26624,    130, }, /* 1155 */
  {    55,     10,      5,      0,      0,  18432,    144, }, /* 1156 */
  {    55,     12,      3,      0,      0,  26624,     96, }, /* 1157 */
  {    55,     12,      3,      0,      0,  26624,    146, }, /* 1158 */
  {    55,     13,     12,      0,      0,  18432,    138, }, /* 1159 */
  {    47,     12,      3,      0,      0,  26624,    130, }, /* 1160 */
  {    47,     12,      3,      0,      0,  26705,    130, }, /* 1161 */
  {    47,     10,      5,      0,      0,  18432,    144, }, /* 1162 */
  {    47,     10,      5,      0,      0,  18513,    144, }, /* 1163 */
  {    47,      7,     12,      0,      0,  18432,     82, }, /* 1164 */
  {    84,     12,      3,      0,      0,  26705,    102, }, /* 1165 */
  {    47,     12,      3,      0,      0,  26705,     96, }, /* 1166 */
  {    47,     10,      3,      0,      0,  18432,    148, }, /* 1167 */
  {    47,     10,      5,      0,      0,  18432,    174, }, /* 1168 */
  {    47,      7,     12,      0,      0,  18432,    324, }, /* 1169 */
  {    47,     12,      3,      0,      0,  26624,     96, }, /* 1170 */
  {   144,      7,     12,      0,      0,  18432,     82, }, /* 1171 */
  {   144,     10,      5,      0,      0,  18432,    144, }, /* 1172 */
  {   144,     12,      3,      0,      0,  26624,    130, }, /* 1173 */
  {   144,     12,      3,      0,      0,  26624,    146, }, /* 1174 */
  {   144,     12,      3,      0,      0,  26624,     96, }, /* 1175 */
  {   144,     21,     12,      0,      0,  18432,    124, }, /* 1176 */
  {   144,     21,     12,      0,      0,  18432,    106, }, /* 1177 */
  {   144,     21,     12,      0,      0,  18432,     68, }, /* 1178 */
  {   144,     13,     12,      0,      0,  18432,    138, }, /* 1179 */
  {   144,     12,      3,      0,      0,  26624,    102, }, /* 1180 */
  {    56,      7,     12,      0,      0,  18432,     82, }, /* 1181 */
  {    56,     10,      3,      0,      0,  18432,    148, }, /* 1182 */
  {    56,     10,      5,      0,      0,  18432,    144, }, /* 1183 */
  {    56,     12,      3,      0,      0,  26624,    130, }, /* 1184 */
  {    56,     12,      3,      0,      0,  26624,    146, }, /* 1185 */
  {    56,     12,      3,      0,      0,  26624,     96, }, /* 1186 */
  {    56,     21,     12,      0,      0,  18432,     68, }, /* 1187 */
  {    56,     13,     12,      0,      0,  18432,    138, }, /* 1188 */
  {   135,      7,     12,      0,      0,  18432,     82, }, /* 1189 */
  {   135,     10,      3,      0,      0,  18432,    148, }, /* 1190 */
  {   135,     10,      5,      0,      0,  18432,    144, }, /* 1191 */
  {   135,     12,      3,      0,      0,  26624,    130, }, /* 1192 */
  {   135,     12,      3,      0,      0,  26624,    146, }, /* 1193 */
  {   135,     12,      3,      0,      0,  26624,     96, }, /* 1194 */
  {   135,     21,     12,      0,      0,  18432,     68, }, /* 1195 */
  {   135,     21,     12,      0,      0,  18432,    124, }, /* 1196 */
  {   135,     21,     12,      0,      0,  18432,    106, }, /* 1197 */
  {   135,     21,     12,      0,      0,  18432,    178, }, /* 1198 */
  {    52,      7,     12,      0,      0,  18432,     82, }, /* 1199 */
  {    52,     10,      5,      0,      0,  18432,    144, }, /* 1200 */
  {    52,     12,      3,      0,      0,  26624,    130, }, /* 1201 */
  {    52,     12,      3,      0,      0,  26624,    146, }, /* 1202 */
  {    52,     21,     12,      0,      0,  18432,    124, }, /* 1203 */
  {    52,     21,     12,      0,      0,  18432,     68, }, /* 1204 */
  {    52,     13,     12,      0,      0,  18432,    138, }, /* 1205 */
  {    45,      7,     12,      0,      0,  18432,     82, }, /* 1206 */
  {    45,     12,      3,      0,      0,  26624,    130, }, /* 1207 */
  {    45,     10,      5,      0,      0,  18432,    144, }, /* 1208 */
  {    45,     10,      5,      0,      0,  18432,    174, }, /* 1209 */
  {    45,     12,      3,      0,      0,  26624,     96, }, /* 1210 */
  {    45,     21,     12,      0,      0,  18432,     68, }, /* 1211 */
  {    45,     13,     12,      0,      0,  18432,    138, }, /* 1212 */
  {   137,      7,     12,      0,      0,  18432,     82, }, /* 1213 */
  {   137,     12,      3,      0,      0,  26624,    130, }, /* 1214 */
  {   137,     10,     12,      0,      0,  18432,    144, }, /* 1215 */
  {   137,     10,      5,      0,      0,  18432,    144, }, /* 1216 */
  {   137,     12,      3,      0,      0,  26624,    146, }, /* 1217 */
  {   137,     13,     12,      0,      0,  18432,    138, }, /* 1218 */
  {   137,     15,     12,      0,      0,  18432,     68, }, /* 1219 */
  {   137,     21,     12,      0,      0,  18432,    124, }, /* 1220 */
  {   137,     26,     12,      0,      0,  18432,     68, }, /* 1221 */
  {    60,      7,     12,      0,      0,  18432,     82, }, /* 1222 */
  {    60,     10,      5,      0,      0,  18432,    144, }, /* 1223 */
  {    60,     12,      3,      0,      0,  26624,    130, }, /* 1224 */
  {    60,     12,      3,      0,      0,  26624,    146, }, /* 1225 */
  {    60,     12,      3,      0,      0,  26624,     96, }, /* 1226 */
  {    60,     21,     12,      0,      0,  18432,     68, }, /* 1227 */
  {   136,      9,     12,      0,     32,  18432,     74, }, /* 1228 */
  {   136,      5,     12,      0,    -32,  18432,     76, }, /* 1229 */
  {   136,     13,     12,      0,      0,  18432,    138, }, /* 1230 */
  {   136,     15,     12,      0,      0,  18432,     68, }, /* 1231 */
  {   136,      7,     12,      0,      0,  18432,     82, }, /* 1232 */
  {   157,      7,     12,      0,      0,  18432,     82, }, /* 1233 */
  {   157,     10,      3,      0,      0,  18432,    148, }, /* 1234 */
  {   157,     10,      5,      0,      0,  18432,    144, }, /* 1235 */
  {   157,     12,      3,      0,      0,  26624,    130, }, /* 1236 */
  {   157,     10,      5,      0,      0,  18432,    174, }, /* 1237 */
  {   157,     12,      3,      0,      0,  26624,    146, }, /* 1238 */
  {   157,      7,      4,      0,      0,  18432,     82, }, /* 1239 */
  {   157,     12,      3,      0,      0,  26624,     96, }, /* 1240 */
  {   157,     21,     12,      0,      0,  18432,    124, }, /* 1241 */
  {   157,     21,     12,      0,      0,  18432,     68, }, /* 1242 */
  {   157,     13,     12,      0,      0,  18432,    138, }, /* 1243 */
  {    64,      7,     12,      0,      0,  18432,     82, }, /* 1244 */
  {    64,     10,      5,      0,      0,  18432,    144, }, /* 1245 */
  {    64,     12,      3,      0,      0,  26624,    130, }, /* 1246 */
  {    64,     12,      3,      0,      0,  26624,    146, }, /* 1247 */
  {    64,     21,     12,      0,      0,  18432,     68, }, /* 1248 */
  {   149,      7,     12,      0,      0,  18432,     82, }, /* 1249 */
  {   149,     12,      3,      0,      0,  26624,    130, }, /* 1250 */
  {   149,     12,      3,      0,      0,  18432,    130, }, /* 1251 */
  {   149,     12,      3,      0,      0,  26624,    102, }, /* 1252 */
  {   149,     12,      3,      0,      0,  26624,    146, }, /* 1253 */
  {   149,     10,      5,      0,      0,  18432,    144, }, /* 1254 */
  {   149,      7,      4,      0,      0,  18432,     82, }, /* 1255 */
  {   149,     21,     12,      0,      0,  18432,     68, }, /* 1256 */
  {   149,     21,     12,      0,      0,  18432,    124, }, /* 1257 */
  {   148,      7,     12,      0,      0,  18432,     82, }, /* 1258 */
  {   148,     12,      3,      0,      0,  26624,    130, }, /* 1259 */
  {   148,     10,      5,      0,      0,  18432,    144, }, /* 1260 */
  {   148,      7,      4,      0,      0,  18432,     82, }, /* 1261 */
  {   148,     12,      3,      0,      0,  26624,    326, }, /* 1262 */
  {   148,     12,      3,      0,      0,  26624,    146, }, /* 1263 */
  {   148,     21,     12,      0,      0,  18432,     68, }, /* 1264 */
  {   148,     21,     12,      0,      0,  18432,    124, }, /* 1265 */
  {   148,     21,     12,      0,      0,  18432,    106, }, /* 1266 */
  {   134,      7,     12,      0,      0,  18432,     82, }, /* 1267 */
  {   142,      7,     12,      0,      0,  18432,     82, }, /* 1268 */
  {   142,     10,      5,      0,      0,  18432,    144, }, /* 1269 */
  {   142,     12,      3,      0,      0,  26624,    130, }, /* 1270 */
  {   142,     12,      3,      0,      0,  18432,    146, }, /* 1271 */
  {   142,     21,     12,      0,      0,  18432,    124, }, /* 1272 */
  {   142,     21,     12,      0,      0,  18432,    106, }, /* 1273 */
  {   142,     21,     12,      0,      0,  18432,     68, }, /* 1274 */
  {   142,     13,     12,      0,      0,  18432,    138, }, /* 1275 */
  {   142,     15,     12,      0,      0,  18432,     68, }, /* 1276 */
  {   143,     21,     12,      0,      0,  18432,     68, }, /* 1277 */
  {   143,     21,     12,      0,      0,  18432,    106, }, /* 1278 */
  {   143,      7,     12,      0,      0,  18432,     82, }, /* 1279 */
  {   143,     12,      3,      0,      0,  26624,    130, }, /* 1280 */
  {   143,     10,      5,      0,      0,  18432,    144, }, /* 1281 */
  {    59,      7,     12,      0,      0,  18432,     82, }, /* 1282 */
  {    59,     12,      3,      0,      0,  26624,    130, }, /* 1283 */
  {    59,     12,      3,      0,      0,  26624,     96, }, /* 1284 */
  {    59,     12,      3,      0,      0,  26624,    146, }, /* 1285 */
  {    59,      7,      4,      0,      0,  18432,     82, }, /* 1286 */
  {    59,     13,     12,      0,      0,  18432,    138, }, /* 1287 */
  {    61,      7,     12,      0,      0,  18432,     82, }, /* 1288 */
  {    61,     10,      5,      0,      0,  18432,    144, }, /* 1289 */
  {    61,     12,      3,      0,      0,  26624,    130, }, /* 1290 */
  {    61,     12,      3,      0,      0,  26624,    146, }, /* 1291 */
  {    61,     13,     12,      0,      0,  18432,    138, }, /* 1292 */
  {   150,      7,     12,      0,      0,  18432,     82, }, /* 1293 */
  {   150,     12,      3,      0,      0,  26624,    130, }, /* 1294 */
  {   150,     10,      5,      0,      0,  18432,    144, }, /* 1295 */
  {   150,     21,     12,      0,      0,  18432,    124, }, /* 1296 */
  {   162,     12,      3,      0,      0,  26624,    130, }, /* 1297 */
  {   162,      7,      4,      0,      0,  18432,     82, }, /* 1298 */
  {   162,     10,      5,      0,      0,  18432,    144, }, /* 1299 */
  {   162,      7,     12,      0,      0,  18432,     82, }, /* 1300 */
  {   162,     10,      5,      0,      0,  18432,    176, }, /* 1301 */
  {   162,     12,      3,      0,      0,  26624,    184, }, /* 1302 */
  {   162,     21,     12,      0,      0,  18432,    124, }, /* 1303 */
  {   162,     21,     12,      0,      0,  18432,     68, }, /* 1304 */
  {   162,     13,     12,      0,      0,  18432,    138, }, /* 1305 */
  {    11,     15,     12,      0,      0,  18432,     68, }, /* 1306 */
  {    11,     21,     12,      0,      0,  18432,     68, }, /* 1307 */
  {    94,      7,     12,      0,      0,  18432,     82, }, /* 1308 */
  {    94,     14,     12,      0,      0,  18432,     82, }, /* 1309 */
  {    94,     21,     12,      0,      0,  18432,    106, }, /* 1310 */
  {    66,      7,     12,      0,      0,  18432,     82, }, /* 1311 */
  {    66,     21,     12,      0,      0,  18432,     68, }, /* 1312 */
  {   109,      7,     12,      0,      0,  18432,     82, }, /* 1313 */
  {   109,      1,      2,      0,      0,  18432,    322, }, /* 1314 */
  {   109,     12,      3,      0,      0,  26624,    102, }, /* 1315 */
  {   109,     12,      3,      0,      0,  26624,     96, }, /* 1316 */
  {   138,      7,     12,      0,      0,  18432,     82, }, /* 1317 */
  {   130,      7,     12,      0,      0,  18432,     82, }, /* 1318 */
  {   130,     13,     12,      0,      0,  18432,    138, }, /* 1319 */
  {   130,     21,     12,      0,      0,  18432,    124, }, /* 1320 */
  {   159,      7,     12,      0,      0,  18432,     82, }, /* 1321 */
  {   159,     13,     12,      0,      0,  18432,    138, }, /* 1322 */
  {   126,      7,     12,      0,      0,  18432,     82, }, /* 1323 */
  {   126,     12,      3,      0,      0,  26624,     96, }, /* 1324 */
  {   126,     21,     12,      0,      0,  18432,    124, }, /* 1325 */
  {   128,      7,     12,      0,      0,  18432,     82, }, /* 1326 */
  {   128,     12,      3,      0,      0,  26624,     96, }, /* 1327 */
  {   128,     21,     12,      0,      0,  18432,    124, }, /* 1328 */
  {   128,     21,     12,      0,      0,  18432,    106, }, /* 1329 */
  {   128,     21,     12,      0,      0,  18432,     68, }, /* 1330 */
  {   128,     26,     12,      0,      0,  18432,     68, }, /* 1331 */
  {   128,      6,     12,      0,      0,  18432,    142, }, /* 1332 */
  {   128,      6,     12,      0,      0,  18432,    136, }, /* 1333 */
  {   128,     13,     12,      0,      0,  18432,    138, }, /* 1334 */
  {   128,     15,     12,      0,      0,  18432,     68, }, /* 1335 */
  {   151,      9,     12,      0,     32,  18432,     74, }, /* 1336 */
  {   151,      5,     12,      0,    -32,  18432,     76, }, /* 1337 */
  {   151,     15,     12,      0,      0,  18432,     68, }, /* 1338 */
  {   151,     21,     12,      0,      0,  18432,    106, }, /* 1339 */
  {   151,     21,     12,      0,      0,  18432,    124, }, /* 1340 */
  {   151,     21,     12,      0,      0,  18432,     68, }, /* 1341 */
  {   123,      7,     12,      0,      0,  18432,     82, }, /* 1342 */
  {   123,     12,      3,      0,      0,  26624,    130, }, /* 1343 */
  {   123,     10,      5,      0,      0,  18432,    144, }, /* 1344 */
  {   123,     12,      3,      0,      0,  26624,    128, }, /* 1345 */
  {   123,      6,     12,      0,      0,  18432,     92, }, /* 1346 */
  {   146,      6,     12,      0,      0,  18432,    136, }, /* 1347 */
  {   147,      6,     12,      0,      0,  18432,    136, }, /* 1348 */
  {    23,     21,     12,      0,      0,  28672,     68, }, /* 1349 */
  {   158,     12,      3,      0,      0,  26624,    328, }, /* 1350 */
  {    23,     10,      5,      0,      0,  18432,    164, }, /* 1351 */
  {   146,      7,     12,      0,      0,  18432,    284, }, /* 1352 */
  {   158,      7,     12,      0,      0,  18432,    284, }, /* 1353 */
  {    21,      6,     12,      0,      0,  18432,     92, }, /* 1354 */
  {   147,      7,     12,      0,      0,  18432,    284, }, /* 1355 */
  {    46,      7,     12,      0,      0,  18432,     82, }, /* 1356 */
  {    46,     26,     12,      0,      0,  18432,     68, }, /* 1357 */
  {    46,     12,      3,      0,      0,  26624,    102, }, /* 1358 */
  {    46,     12,      3,      0,      0,  26624,    130, }, /* 1359 */
  {    46,     21,     12,      0,      0,  18432,    124, }, /* 1360 */
  {    69,      1,      2,      0,      0,   6153,     66, }, /* 1361 */
  {    69,     10,      3,      0,      0,  18432,    330, }, /* 1362 */
  {    69,     10,      5,      0,      0,  18432,    138, }, /* 1363 */
  {    69,     10,      5,      0,      0,  18432,    160, }, /* 1364 */
  {    69,     10,      3,      0,      0,  18432,    286, }, /* 1365 */
  {     1,     12,      3,      0,      0,  26624,    102, }, /* 1366 */
  {    69,     25,     12,      0,      0,  18432,    118, }, /* 1367 */
  {    69,     13,     12,      0,      0,  10240,    214, }, /* 1368 */
  {   141,     26,     12,      0,      0,  18432,     68, }, /* 1369 */
  {   141,     12,      3,      0,      0,  26624,    102, }, /* 1370 */
  {   141,     21,     12,      0,      0,  18432,    106, }, /* 1371 */
  {   141,     21,     12,      0,      0,  18432,    124, }, /* 1372 */
  {   141,     21,     12,      0,      0,  18432,     68, }, /* 1373 */
  {    35,     12,      3,      0,      0,  26624,    130, }, /* 1374 */
  {     2,      6,     12,      0,      0,  18432,     90, }, /* 1375 */
  {   154,      7,     12,      0,      0,  18432,     82, }, /* 1376 */
  {   154,     12,      3,      0,      0,  26624,     96, }, /* 1377 */
  {   154,      6,     12,      0,      0,  18432,    142, }, /* 1378 */
  {   154,      6,     12,      0,      0,  18432,    136, }, /* 1379 */
  {   154,     13,     12,      0,      0,  18432,    138, }, /* 1380 */
  {   154,     26,     12,      0,      0,  18432,     68, }, /* 1381 */
  {   160,      7,     12,      0,      0,  18432,     82, }, /* 1382 */
  {   160,     12,      3,      0,      0,  26624,     96, }, /* 1383 */
  {   155,      7,     12,      0,      0,  18432,     82, }, /* 1384 */
  {   155,     12,      3,      0,      0,  26624,     96, }, /* 1385 */
  {   155,     13,     12,      0,      0,  18432,    138, }, /* 1386 */
  {   155,     23,     12,      0,      0,  14336,     68, }, /* 1387 */
  {   163,      7,     12,      0,      0,  18432,     82, }, /* 1388 */
  {   163,      6,     12,      0,      0,  18432,    142, }, /* 1389 */
  {   163,     12,      3,      0,      0,  26624,    102, }, /* 1390 */
  {   163,     13,     12,      0,      0,  18432,    138, }, /* 1391 */
  {   129,      7,     12,      0,      0,  34816,     82, }, /* 1392 */
  {   129,     15,     12,      0,      0,  34816,     68, }, /* 1393 */
  {   129,     12,      3,      0,      0,  26624,     96, }, /* 1394 */
  {    58,      9,     12,      0,     34,  34816,     74, }, /* 1395 */
  {    58,      5,     12,      0,    -34,  34816,     76, }, /* 1396 */
  {    58,     12,      3,      0,      0,  26624,    150, }, /* 1397 */
  {    58,     12,      3,      0,      0,  26624,    130, }, /* 1398 */
  {    58,     12,      3,      0,      0,  26624,     96, }, /* 1399 */
  {    58,      6,     12,      0,      0,  34816,    142, }, /* 1400 */
  {    58,     13,     12,      0,      0,  34816,    138, }, /* 1401 */
  {    58,     21,     12,      0,      0,  34816,     68, }, /* 1402 */
  {    69,     15,     12,      0,      0,      0,     68, }, /* 1403 */
  {    69,     26,     12,      0,      0,      0,     68, }, /* 1404 */
  {    69,     23,     12,      0,      0,      0,     68, }, /* 1405 */
  {     3,      7,     12,      0,      0,      0,    240, }, /* 1406 */
  {    69,     26,     14,      0,      0,  28672,    332, }, /* 1407 */
  {    69,     26,     14,      0,      0,  28672,    334, }, /* 1408 */
  {    68,      2,     14,      0,      0,  18432,    336, }, /* 1409 */
  {    69,     26,     12,      0,      0,  18432,    338, }, /* 1410 */
  {    69,     26,     14,      0,      0,  18432,    340, }, /* 1411 */
  {    69,     26,     14,      0,      0,  18432,    334, }, /* 1412 */
  {    69,     26,     11,      0,      0,  18432,    342, }, /* 1413 */
  {    20,     26,     12,      0,      0,  18432,     68, }, /* 1414 */
  {    69,     26,     14,      0,      0,  18432,    236, }, /* 1415 */
  {    69,     26,     14,      0,      0,  18447,    334, }, /* 1416 */
  {    69,     26,     14,      0,      0,  28672,    344, }, /* 1417 */
  {    69,     26,     14,      0,      0,  28672,    346, }, /* 1418 */
  {    69,     24,      3,      0,      0,  28672,    348, }, /* 1419 */
  {    69,     26,     14,      0,      0,  28672,    350, }, /* 1420 */
  {    69,     13,     12,      0,      0,  10240,    138, }, /* 1421 */
  {    69,      1,      3,      0,      0,   6144,    352, }, /* 1422 */
};

const uint16_t PRIV(ucd_stage1)[] = { /* 17408 bytes */
  0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, /* U+0000 */
 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, /* U+0800 */
 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 41, 41, 42, 43, 44, 45, /* U+1000 */
 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, /* U+1800 */
 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, /* U+2000 */
 78, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, /* U+2800 */
 93, 94, 95, 96, 97, 98, 99,100,101,101,101,101,101,101,101,101, /* U+3000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+3800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+4000 */
101,101,101,101,101,101,101,101,101,101,101,102,101,101,101,101, /* U+4800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+5000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+5800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+6000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+6800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+7000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+7800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+8000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+8800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+9000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+9800 */
103,104,104,104,104,104,104,104,104,105,106,106,107,108,109,110, /* U+A000 */
111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,119, /* U+A800 */
120,121,122,123,124,125,119,120,121,122,123,124,125,119,120,121, /* U+B000 */
122,123,124,125,119,120,121,122,123,124,125,119,120,121,122,123, /* U+B800 */
124,125,119,120,121,122,123,124,125,119,120,121,122,123,124,125, /* U+C000 */
119,120,121,122,123,124,125,119,120,121,122,123,124,125,119,120, /* U+C800 */
121,122,123,124,125,119,120,121,122,123,124,125,119,120,121,126, /* U+D000 */
127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127, /* U+D800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+E000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+E800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F000 */
128,128,129,129,130,131,132,133,134,135,136,137,138,139,140,141, /* U+F800 */
142,143,144,145,146,147,148,149,150,151,152,153,154,154,155,156, /* U+10000 */
157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172, /* U+10800 */
173,174,175,176,177,178,179,146,180,181,146,182,183,184,185,146, /* U+11000 */
186,187,188,189,190,191,192,146,193,194,195,196,146,197,198,199, /* U+11800 */
200,200,200,200,200,200,200,201,202,200,203,146,146,146,146,146, /* U+12000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,204, /* U+12800 */
205,205,205,205,205,205,205,205,206,146,146,146,146,146,146,146, /* U+13000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+13800 */
146,146,146,146,146,146,146,146,207,207,207,207,208,146,146,146, /* U+14000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+14800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+15000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+15800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+16000 */
209,209,209,209,210,211,212,213,146,146,146,146,214,215,216,217, /* U+16800 */
218,218,218,218,218,218,218,218,218,218,218,218,218,218,218,218, /* U+17000 */
218,218,218,218,218,218,218,218,218,218,218,218,218,218,218,218, /* U+17800 */
218,218,218,218,218,218,218,218,218,218,218,218,218,218,218,219, /* U+18000 */
218,218,218,218,218,218,220,220,220,221,222,146,146,146,146,146, /* U+18800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+19000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+19800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+1A000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,223, /* U+1A800 */
224,225,226,227,227,228,146,146,146,146,146,146,146,146,146,146, /* U+1B000 */
146,146,146,146,146,146,146,146,229,230,146,146,146,146,146,146, /* U+1B800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+1C000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,231,232, /* U+1C800 */
233,234,235,236,237,238,239,146,240,241,242,243,244,245,246,247, /* U+1D000 */
248,248,248,248,249,250,146,146,146,146,146,146,146,146,251,146, /* U+1D800 */
252,253,254,146,146,255,146,146,146,256,146,146,146,146,146,257, /* U+1E000 */
258,259,260,168,168,168,168,168,261,262,263,168,264,265,168,168, /* U+1E800 */
266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281, /* U+1F000 */
282,283,284,285,286,287,288,289,271,271,271,271,271,271,271,290, /* U+1F800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+20000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+20800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+21000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+21800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+22000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+22800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+23000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+23800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+24000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+24800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+25000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+25800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+26000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+26800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+27000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+27800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+28000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+28800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+29000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+29800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,291,101,101, /* U+2A000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+2A800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,292,101, /* U+2B000 */
293,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+2B800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+2C000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,294,101,101, /* U+2C800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+2D000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+2D800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+2E000 */
101,101,101,101,101,101,101,295,146,146,146,146,146,146,146,146, /* U+2E800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+2F000 */
129,129,129,129,296,146,146,146,146,146,146,146,146,146,146,297, /* U+2F800 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+30000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+30800 */
101,101,101,101,101,101,298,101,101,101,101,101,101,101,101,101, /* U+31000 */
101,101,101,101,101,101,101,101,101,101,101,101,101,101,101,101, /* U+31800 */
101,101,101,101,101,101,101,299,146,146,146,146,146,146,146,146, /* U+32000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+32800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+33000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+33800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+34000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+34800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+35000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+35800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+36000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+36800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+37000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+37800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+38000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+38800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+39000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+39800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+3A000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+3A800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+3B000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+3B800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+3C000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+3C800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+3D000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+3D800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+3E000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+3E800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+3F000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,297, /* U+3F800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+40000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+40800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+41000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+41800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+42000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+42800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+43000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+43800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+44000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+44800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+45000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+45800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+46000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+46800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+47000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+47800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+48000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+48800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+49000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+49800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+4A000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+4A800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+4B000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+4B800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+4C000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+4C800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+4D000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+4D800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+4E000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+4E800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+4F000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,297, /* U+4F800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+50000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+50800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+51000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+51800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+52000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+52800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+53000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+53800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+54000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+54800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+55000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+55800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+56000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+56800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+57000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+57800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+58000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+58800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+59000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+59800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+5A000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+5A800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+5B000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+5B800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+5C000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+5C800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+5D000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+5D800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+5E000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+5E800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+5F000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,297, /* U+5F800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+60000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+60800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+61000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+61800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+62000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+62800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+63000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+63800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+64000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+64800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+65000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+65800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+66000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+66800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+67000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+67800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+68000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+68800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+69000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+69800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+6A000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+6A800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+6B000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+6B800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+6C000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+6C800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+6D000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+6D800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+6E000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+6E800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+6F000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,297, /* U+6F800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+70000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+70800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+71000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+71800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+72000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+72800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+73000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+73800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+74000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+74800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+75000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+75800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+76000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+76800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+77000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+77800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+78000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+78800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+79000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+79800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+7A000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+7A800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+7B000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+7B800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+7C000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+7C800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+7D000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+7D800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+7E000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+7E800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+7F000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,297, /* U+7F800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+80000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+80800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+81000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+81800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+82000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+82800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+83000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+83800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+84000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+84800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+85000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+85800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+86000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+86800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+87000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+87800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+88000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+88800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+89000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+89800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+8A000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+8A800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+8B000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+8B800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+8C000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+8C800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+8D000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+8D800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+8E000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+8E800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+8F000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,297, /* U+8F800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+90000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+90800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+91000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+91800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+92000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+92800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+93000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+93800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+94000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+94800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+95000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+95800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+96000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+96800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+97000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+97800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+98000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+98800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+99000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+99800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+9A000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+9A800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+9B000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+9B800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+9C000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+9C800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+9D000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+9D800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+9E000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+9E800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+9F000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,297, /* U+9F800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A0000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A0800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A1000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A1800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A2000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A2800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A3000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A3800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A4000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A4800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A5000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A5800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A6000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A6800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A7000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A7800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A8000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A8800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A9000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+A9800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+AA000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+AA800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+AB000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+AB800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+AC000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+AC800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+AD000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+AD800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+AE000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+AE800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+AF000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,297, /* U+AF800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B0000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B0800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B1000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B1800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B2000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B2800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B3000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B3800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B4000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B4800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B5000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B5800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B6000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B6800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B7000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B7800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B8000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B8800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B9000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+B9800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+BA000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+BA800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+BB000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+BB800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+BC000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+BC800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+BD000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+BD800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+BE000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+BE800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+BF000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,297, /* U+BF800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C0000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C0800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C1000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C1800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C2000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C2800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C3000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C3800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C4000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C4800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C5000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C5800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C6000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C6800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C7000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C7800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C8000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C8800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C9000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+C9800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+CA000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+CA800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+CB000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+CB800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+CC000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+CC800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+CD000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+CD800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+CE000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+CE800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+CF000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,297, /* U+CF800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D0000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D0800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D1000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D1800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D2000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D2800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D3000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D3800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D4000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D4800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D5000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D5800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D6000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D6800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D7000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D7800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D8000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D8800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D9000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+D9800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+DA000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+DA800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+DB000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+DB800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+DC000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+DC800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+DD000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+DD800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+DE000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+DE800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+DF000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,297, /* U+DF800 */
300,301,302,303,301,301,301,301,301,301,301,301,301,301,301,301, /* U+E0000 */
301,301,301,301,301,301,301,301,301,301,301,301,301,301,301,301, /* U+E0800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E1000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E1800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E2000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E2800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E3000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E3800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E4000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E4800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E5000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E5800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E6000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E6800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E7000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E7800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E8000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E8800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E9000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+E9800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+EA000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+EA800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+EB000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+EB800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+EC000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+EC800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+ED000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+ED800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+EE000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+EE800 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146, /* U+EF000 */
146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,297, /* U+EF800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F0000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F0800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F1000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F1800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F2000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F2800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F3000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F3800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F4000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F4800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F5000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F5800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F6000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F6800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F7000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F7800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F8000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F8800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F9000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+F9800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+FA000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+FA800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+FB000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+FB800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+FC000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+FC800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+FD000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+FD800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+FE000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+FE800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+FF000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,304, /* U+FF800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+100000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+100800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+101000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+101800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+102000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+102800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+103000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+103800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+104000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+104800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+105000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+105800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+106000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+106800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+107000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+107800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+108000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+108800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+109000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+109800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+10A000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+10A800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+10B000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+10B800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+10C000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+10C800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+10D000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+10D800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+10E000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+10E800 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,128, /* U+10F000 */
128,128,128,128,128,128,128,128,128,128,128,128,128,128,128,304, /* U+10F800 */
};

const uint16_t PRIV(ucd_stage2)[] = { /* 78080 bytes, block = 128 */

/* block 0 */
  0,  0,  0,  0,  0,  0,  0,  0,  0,  1,  2,  1,  3,  4,  0,  0,
  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  5,  5,  5,  6,
  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22,
 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 24, 25, 26, 27, 26,  8,
 13, 28, 28, 28, 28, 28, 28, 29, 29, 29, 29, 30, 29, 29, 29, 29,
 29, 29, 29, 31, 29, 29, 29, 29, 29, 29, 29, 15, 13, 16, 32, 33,
 34, 35, 35, 35, 35, 35, 35, 36, 36, 37, 37, 38, 36, 36, 36, 36,
 36, 36, 36, 39, 36, 36, 36, 36, 36, 36, 36, 15, 27, 16, 27,  0,

/* block 1 */
 40, 40, 40, 40, 40, 41, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40,
 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40, 40,
 42, 43, 44, 44, 44, 44, 45, 43, 46, 47, 48, 49, 50, 51, 47, 46,
 52, 53, 54, 54, 46, 55, 43, 56, 46, 54, 48, 57, 58, 58, 58, 43,
 59, 59, 59, 59, 59, 60, 59, 59, 59, 59, 59, 59, 59, 59, 59, 59,
 59, 59, 59, 59, 59, 59, 59, 50, 59, 59, 59, 59, 59, 59, 59, 61,
 62, 62, 62, 62, 62, 63, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62,
 62, 62, 62, 62, 62, 62, 62, 50, 62, 62, 62, 62, 62, 62, 62, 64,

/* block 2 */
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 67,
 68, 69, 65, 66, 65, 66, 65, 66, 70, 65, 66, 65, 66, 65, 66, 65,
 66, 65, 66, 65, 66, 65, 66, 65, 66, 71, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 72, 65, 66, 65, 66, 65, 66, 73,

/* block 3 */
 74, 75, 65, 66, 65, 66, 76, 65, 66, 77, 77, 65, 66, 70, 78, 79,
 80, 65, 66, 77, 81, 82, 83, 84, 65, 66, 85, 70, 83, 86, 87, 88,
 65, 66, 65, 66, 65, 66, 89, 65, 66, 89, 70, 70, 65, 66, 89, 65,
 66, 90, 90, 65, 66, 65, 66, 91, 65, 66, 70, 92, 65, 66, 70, 93,
 92, 92, 92, 92, 94, 95, 96, 97, 98, 99,100,101,102, 65, 66, 65,
 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,103, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 69,104,105,106, 65, 66,107,108, 65, 66, 65, 66, 65, 66, 65, 66,

/* block 4 */
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
109, 70, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 70, 70, 70, 70, 70, 70,110, 65, 66,111,112,113,
113, 65, 66,114,115,116, 65, 66, 65, 67, 65, 66, 65, 66, 65, 66,
117,118,119,120,121, 70,122,122, 70,123, 70,124,125, 70, 70, 70,
122,126, 70,127, 70,128,129, 70,130,131,129,132,133, 70, 70,131,
 70,134,135, 70, 70,136, 70, 70, 70, 70, 70, 70, 70,137, 70, 70,

/* block 5 */
138, 70,139,138, 70, 70, 70,140,138,141,142,142,143, 70, 70, 70,
 70, 70,144, 70, 92, 70, 70, 70, 70, 70, 70, 70, 70,145,146, 70,
 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70,
147,147,148,147,147,147,147,147,147,149,149,150,150,150,150,150,
151,151, 46, 46, 46, 46,149,149,149,149,149,149,149,149,149,149,
152,152, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46,
147,147,147,147,147, 46, 46, 46, 46, 46,153,153,149, 46,150, 46,
 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46, 46,

/* block 6 */
154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,
154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,
154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,
154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,
154,154,155,154,154,156,154,154,154,154,154,154,154,154,154,157,
154,154,154,154,154,154,154,154,158,158,158,158,158,154,154,154,
154,154,154,159,159,159,159,159,159,159,159,159,159,159,159,159,
160,161,160,161,149,162,160,161,163,163,164,165,165,165,166,167,

/* block 7 */
163,163,163,163,162, 46,168,169,170,170,170,163,171,163,172,172,
173,174,175,174,174,176,174,174,177,178,179,174,180,174,174,174,
181,182,163,183,174,174,184,174,174,185,174,174,186,187,187,187,
173,188,189,188,188,190,188,188,191,192,193,188,194,188,188,188,
195,196,197,198,188,188,199,188,188,200,188,188,201,202,202,203,
204,205,206,207,207,208,209,210,160,161,160,161,160,161,160,161,
160,161,211,212,211,212,211,212,211,212,211,212,211,212,211,212,
213,214,215,216,217,218,219,160,161,220,160,161,221,222,222,222,

/* block 8 */
223,223,223,223,223,223,223,223,223,223,223,223,223,223,223,223,
224,224,225,224,226,224,224,224,224,224,224,224,224,224,227,224,
224,228,229,224,224,224,224,224,224,224,230,224,224,224,224,224,
231,231,232,231,233,231,231,231,231,231,231,231,231,231,234,231,
231,235,236,231,231,231,231,231,231,231,237,231,231,231,231,231,
238,238,238,238,238,238,239,238,239,238,238,238,238,238,238,238,
240,241,242,243,240,241,240,241,240,241,240,241,240,241,240,241,
240,241,240,241,240,241,240,241,240,241,240,241,240,241,240,241,

/* block 9 */
240,241,244,245,246,247,247,246,248,248,240,241,240,241,240,241,
240,241,240,241,240,241,240,241,240,241,240,241,240,241,240,241,
240,241,240,241,240,241,240,241,240,241,240,241,240,241,240,241,
240,241,240,241,240,241,240,241,240,241,240,241,240,241,240,241,
249,240,241,240,241,240,241,240,241,240,241,240,241,240,241,250,
240,241,240,241,240,241,240,241,240,241,240,241,240,241,240,241,
240,241,240,241,240,241,240,241,240,241,240,241,240,241,240,241,
240,241,240,241,240,241,240,241,240,241,240,241,240,241,240,241,

/* block 10 */
240,241,240,241,240,241,240,241,240,241,240,241,240,241,240,241,
240,241,240,241,240,241,240,241,240,241,240,241,240,241,240,241,
240,241,240,241,240,241,240,241,240,241,240,241,240,241,240,241,
163,251,251,251,251,251,251,251,251,251,251,251,251,251,251,251,
251,251,251,251,251,251,251,251,251,251,251,251,251,251,251,251,
251,251,251,251,251,251,251,163,163,252,253,253,253,253,253,254,
255,256,256,256,256,256,256,256,256,256,256,256,256,256,256,256,
256,256,256,256,256,256,256,256,256,256,256,256,256,256,256,256,

/* block 11 */
256,256,256,256,256,256,256,257,255,258,259,163,163,260,260,261,
262,263,263,263,263,263,263,263,263,263,263,263,263,263,263,263,
263,263,264,263,263,263,263,263,263,263,263,263,263,263,263,263,
265,265,265,265,265,265,265,265,265,265,265,265,265,265,266,265,
267,265,265,268,265,269,267,269,262,262,262,262,262,262,262,262,
270,270,270,270,270,270,270,270,270,270,270,270,270,270,270,270,
270,270,270,270,270,270,270,270,270,270,270,262,262,262,262,270,
270,270,270,267,271,262,262,262,262,262,262,262,262,262,262,262,

/* block 12 */
272,272,272,272,272,273,274,274,275,276,276,277,278,279,280,280,
281,281,281,281,281,281,281,281,281,281,281,282,283,284,284,285,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
287,286,286,286,286,286,286,286,286,286,286,288,288,288,288,288,
288,288,288,289,289,289,281,290,291,281,281,281,281,281,281,281,
292,292,292,292,292,292,292,292,292,292,276,293,293,279,286,286,
289,286,286,294,286,286,286,286,286,286,286,286,286,286,286,286,

/* block 13 */
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,295,286,281,281,281,281,281,281,281,273,280,291,
291,281,281,281,281,296,296,281,281,280,291,291,291,281,286,286,
297,297,297,297,297,297,297,297,297,297,286,286,286,298,298,286,

/* block 14 */
299,299,299,300,300,300,300,300,300,300,300,301,300,301,302,303,
304,305,304,304,304,304,304,304,304,304,304,304,304,304,304,304,
304,304,304,304,304,304,304,304,304,304,304,304,304,304,304,304,
306,306,306,306,306,306,306,306,306,306,306,306,306,306,306,306,
307,307,307,307,307,307,307,307,307,307,307,302,302,304,304,304,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,

/* block 15 */
308,308,308,308,308,308,308,308,308,308,308,308,308,308,308,308,
308,308,308,308,308,308,308,308,308,308,308,308,308,308,308,308,
308,308,308,308,308,308,309,309,309,309,309,309,309,309,309,309,
309,308,302,302,302,302,302,302,302,302,302,302,302,302,302,302,
310,310,310,310,310,310,310,310,310,310,311,311,311,311,311,311,
311,311,311,311,311,311,311,311,311,311,311,311,311,311,311,311,
311,311,311,311,311,311,311,311,311,311,311,312,312,312,312,312,
312,312,312,312,313,313,314,315,316,317,318,262,262,319,320,320,

/* block 16 */
321,321,321,321,321,321,321,321,321,321,321,321,321,321,321,321,
321,321,321,321,321,321,322,322,323,323,324,322,322,322,322,322,
322,322,322,322,324,322,322,322,324,322,322,322,322,325,262,262,
326,326,326,326,326,326,326,327,326,327,326,326,326,327,327,262,
328,328,328,328,328,328,328,328,328,328,328,328,328,328,328,328,
328,328,328,328,328,328,328,328,328,329,329,329,262,262,330,262,
304,304,304,304,304,304,304,304,304,304,304,302,302,302,302,302,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,

/* block 17 */
286,286,286,286,286,286,286,286,331,286,286,286,286,286,286,302,
272,272,302,302,302,302,302,302,291,291,291,291,291,291,291,291,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,296,291,291,291,291,291,291,
291,291,291,332,281,281,281,281,281,281,281,281,281,281,281,281,
332,332,273,290,290,290,290,290,290,290,291,291,291,291,291,291,
290,290,290,290,290,290,290,290,290,290,290,290,290,290,290,281,

/* block 18 */
333,333,333,334,335,335,335,335,335,335,335,335,335,335,335,335,
335,335,335,335,335,335,335,335,335,335,335,335,335,335,335,335,
335,335,335,335,335,335,335,335,335,335,335,335,335,335,335,335,
335,335,335,335,335,335,335,335,335,335,333,334,336,335,334,334,
334,333,333,333,333,333,333,333,333,334,334,334,334,337,334,334,
335,338,339,154,154,333,333,333,335,335,335,335,335,335,335,335,
335,335,333,333,340,341,342,342,342,342,342,342,342,342,342,342,
343,344,335,335,335,335,335,335,335,335,335,335,335,335,335,335,

/* block 19 */
345,346,347,347,163,345,345,345,345,345,345,345,345,163,163,345,
345,163,163,345,345,345,345,345,345,345,345,345,345,345,345,345,
345,345,345,345,345,345,345,345,345,163,345,345,345,345,345,345,
345,163,345,163,163,163,345,345,345,345,163,163,348,345,349,347,
347,346,346,346,346,163,163,347,347,163,163,347,347,350,345,163,
163,163,163,163,163,163,163,349,163,163,163,163,345,345,163,345,
345,345,346,346,163,163,351,351,351,351,351,351,351,351,351,351,
345,345,352,352,353,353,353,353,353,353,354,352,345,355,356,163,

/* block 20 */
163,357,357,358,163,359,359,359,359,359,359,163,163,163,163,359,
359,163,163,359,359,359,359,359,359,359,359,359,359,359,359,359,
359,359,359,359,359,359,359,359,359,163,359,359,359,359,359,359,
359,163,359,359,163,359,359,163,359,359,163,163,360,163,358,358,
358,357,357,163,163,163,163,357,357,163,163,357,357,361,163,163,
163,357,163,163,163,163,163,163,163,359,359,359,359,163,359,163,
163,163,163,163,163,163,362,362,362,362,362,362,362,362,362,362,
357,357,359,359,359,357,363,163,163,163,163,163,163,163,163,163,

/* block 21 */
163,364,364,365,163,366,366,366,366,366,366,366,366,366,163,366,
366,366,163,366,366,366,366,366,366,366,366,366,366,366,366,366,
366,366,366,366,366,366,366,366,366,163,366,366,366,366,366,366,
366,163,366,366,163,366,366,366,366,366,163,163,367,366,365,365,
365,364,364,364,364,364,163,364,364,365,163,365,365,368,163,163,
366,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
366,366,364,364,163,163,369,369,369,369,369,369,369,369,369,369,
370,371,163,163,163,163,163,163,163,366,364,364,364,367,367,367,

/* block 22 */
163,372,373,373,163,374,374,374,374,374,374,374,374,163,163,374,
374,163,163,374,374,374,374,374,374,374,374,374,374,374,374,374,
374,374,374,374,374,374,374,374,374,163,374,374,374,374,374,374,
374,163,374,374,163,374,374,374,374,374,163,163,375,374,376,372,
373,372,372,372,372,163,163,373,373,163,163,373,373,377,163,163,
163,163,163,163,163,378,372,376,163,163,163,163,374,374,163,374,
374,374,372,372,163,163,379,379,379,379,379,379,379,379,379,379,
380,374,381,381,381,381,381,381,163,163,163,163,163,163,163,163,

/* block 23 */
163,163,382,383,163,383,383,383,383,383,383,163,163,163,383,383,
383,163,383,383,383,383,163,163,163,383,383,163,383,163,383,383,
163,163,163,383,383,163,163,163,383,383,383,163,163,163,383,383,
383,383,383,383,383,383,383,383,383,383,163,163,163,163,384,385,
382,385,385,163,163,163,385,385,385,163,385,385,385,386,163,163,
383,163,163,163,163,163,163,384,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,387,387,387,387,387,387,387,387,387,387,
388,388,388,389,390,390,390,390,390,391,390,163,163,163,163,163,

/* block 24 */
392,393,393,393,392,394,394,394,394,394,394,394,394,163,394,394,
394,163,394,394,394,394,394,394,394,394,394,394,394,394,394,394,
394,394,394,394,394,394,394,394,394,163,394,394,394,394,394,394,
394,394,394,394,394,394,394,394,394,394,163,163,395,394,392,392,
392,393,393,393,393,163,392,392,392,163,392,392,392,396,163,163,
163,163,163,163,163,392,392,163,394,394,394,163,163,394,163,163,
394,394,392,392,163,163,397,397,397,397,397,397,397,397,397,397,
163,163,163,163,163,163,163,398,399,399,399,399,399,399,399,400,

/* block 25 */
401,402,403,403,404,401,401,401,401,401,401,401,401,163,401,401,
401,163,401,401,401,401,401,401,401,401,401,401,401,401,401,401,
401,401,401,401,401,401,401,401,401,163,401,401,401,401,401,401,
401,401,401,401,163,401,401,401,401,401,163,163,405,401,403,406,
403,403,407,403,403,163,406,403,403,163,403,403,402,408,163,163,
163,163,163,163,163,407,407,163,163,163,163,163,163,401,401,163,
401,401,402,402,163,163,409,409,409,409,409,409,409,409,409,409,
163,401,401,403,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 26 */
410,410,411,411,412,412,412,412,412,412,412,412,412,163,412,412,
412,163,412,412,412,412,412,412,412,412,412,412,412,412,412,412,
412,412,412,412,412,412,412,412,412,412,412,412,412,412,412,412,
412,412,412,412,412,412,412,412,412,412,412,413,413,412,414,411,
411,410,410,410,410,163,411,411,411,163,411,411,411,413,415,416,
163,163,163,163,412,412,412,414,417,417,417,417,417,417,417,412,
412,412,410,410,163,163,418,418,418,418,418,418,418,418,418,418,
417,417,417,417,417,417,417,417,417,416,412,412,412,412,412,412,

/* block 27 */
163,419,420,420,163,421,421,421,421,421,421,421,421,421,421,421,
421,421,421,421,421,421,421,163,163,163,421,421,421,421,421,421,
421,421,421,421,421,421,421,421,421,421,421,421,421,421,421,421,
421,421,163,421,421,421,421,421,421,421,421,421,163,421,163,163,
421,421,421,421,421,421,421,163,163,163,422,163,163,163,163,423,
420,420,419,419,419,163,419,163,420,420,420,420,420,420,420,423,
163,163,163,163,163,163,424,424,424,424,424,424,424,424,424,424,
163,163,420,420,425,163,163,163,163,163,163,163,163,163,163,163,

/* block 28 */
163,426,426,426,426,426,426,426,426,426,426,426,426,426,426,426,
426,426,426,426,426,426,426,426,426,426,426,426,426,426,426,426,
426,426,426,426,426,426,426,426,426,426,426,426,426,426,426,426,
426,427,426,428,427,427,427,427,427,427,429,163,163,163,163,430,
431,431,431,431,431,426,432,433,433,433,433,433,433,427,433,434,
435,435,435,435,435,435,435,435,435,435,436,436,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 29 */
163,437,437,163,437,163,437,437,437,437,437,163,437,437,437,437,
437,437,437,437,437,437,437,437,437,437,437,437,437,437,437,437,
437,437,437,437,163,437,163,437,437,437,437,437,437,437,437,437,
437,438,437,439,438,438,438,438,438,438,440,438,438,437,163,163,
441,441,441,441,441,163,442,163,443,443,443,443,443,438,444,163,
445,445,445,445,445,445,445,445,445,445,163,163,437,437,437,437,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 30 */
446,447,447,447,448,448,448,448,449,448,448,448,448,449,449,449,
449,449,449,447,448,447,447,447,450,450,447,447,447,447,447,447,
451,451,451,451,451,451,451,451,451,451,452,452,452,452,452,452,
452,452,452,452,447,450,447,450,447,450,453,454,453,454,455,455,
446,446,446,446,446,446,446,446,163,446,446,446,446,446,446,446,
446,446,446,446,446,446,446,446,446,446,446,446,446,446,446,446,
446,446,446,446,446,446,446,446,446,446,446,446,446,163,163,163,
163,456,456,456,456,456,456,457,456,457,456,456,456,456,456,458,

/* block 31 */
456,456,459,459,460,448,450,450,446,446,446,446,446,456,456,456,
456,456,456,456,456,456,456,456,163,456,456,456,456,456,456,456,
456,456,456,456,456,456,456,456,456,456,456,456,456,456,456,456,
456,456,456,456,456,456,456,456,456,456,456,456,456,163,447,447,
447,447,447,447,447,447,450,447,447,447,447,447,447,163,447,447,
448,448,448,448,448,461,461,461,461,448,448,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 32 */
462,462,462,462,462,462,462,462,462,462,462,462,462,462,462,462,
462,462,462,462,462,462,462,462,462,462,462,462,462,462,462,462,
462,462,462,462,462,462,462,462,462,462,462,463,463,464,464,464,
464,465,464,464,464,464,464,466,463,467,467,465,465,464,464,462,
468,468,468,468,468,468,468,468,468,468,469,469,470,470,470,470,
462,462,462,462,462,462,465,465,464,464,462,462,462,462,464,464,
464,462,463,471,471,462,462,463,463,471,471,471,471,471,462,462,
462,464,464,464,464,462,462,462,462,462,462,462,462,462,462,462,

/* block 33 */
462,462,464,463,465,464,464,471,471,471,471,471,471,472,462,471,
473,473,473,473,473,473,473,473,473,473,471,471,463,464,474,474,
475,475,475,475,475,475,475,475,475,475,475,475,475,475,475,475,
475,475,475,475,475,475,475,475,475,475,475,475,475,475,475,475,
475,475,475,475,475,475,163,475,163,163,163,163,163,475,163,163,
476,476,476,476,476,476,476,476,476,476,476,476,476,476,476,476,
476,476,476,476,476,476,476,476,476,476,476,476,476,476,476,476,
476,476,476,476,476,476,476,476,476,476,476,477,478,476,476,476,

/* block 34 */
479,479,479,479,479,479,479,479,479,479,479,479,479,479,479,479,
479,479,479,479,479,479,479,479,479,479,479,479,479,479,479,479,
479,479,479,479,479,479,479,479,479,479,479,479,479,479,479,479,
479,479,479,479,479,479,479,479,479,479,479,479,479,479,479,479,
479,479,479,479,479,479,479,479,479,479,479,479,479,479,479,479,
479,479,479,479,479,479,479,479,479,479,479,479,479,479,479,480,
481,482,482,482,482,482,482,482,482,482,482,482,482,482,482,482,
482,482,482,482,482,482,482,482,482,482,482,482,482,482,482,482,

/* block 35 */
482,482,482,482,482,482,482,482,482,482,482,482,482,482,482,482,
482,482,482,482,482,482,482,482,482,482,482,482,482,482,482,482,
482,482,482,482,482,482,482,482,483,483,483,483,483,483,483,483,
483,483,483,483,483,483,483,483,483,483,483,483,483,483,483,483,
483,483,483,483,483,483,483,483,483,483,483,483,483,483,483,483,
483,483,483,483,483,483,483,483,483,483,483,483,483,483,483,483,
483,483,483,483,483,483,483,483,483,483,483,483,483,483,483,483,
483,483,483,483,483,483,483,483,483,483,483,483,483,483,483,483,

/* block 36 */
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,
484,484,484,484,484,484,484,484,484,163,484,484,484,484,163,163,
484,484,484,484,484,484,484,163,484,163,484,484,484,484,163,163,
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,

/* block 37 */
484,484,484,484,484,484,484,484,484,163,484,484,484,484,163,163,
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,
484,163,484,484,484,484,163,163,484,484,484,484,484,484,484,163,
484,163,484,484,484,484,163,163,484,484,484,484,484,484,484,484,
484,484,484,484,484,484,484,163,484,484,484,484,484,484,484,484,
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,

/* block 38 */
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,
484,163,484,484,484,484,163,163,484,484,484,484,484,484,484,484,
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,
484,484,484,484,484,484,484,484,484,484,484,163,163,485,485,485,
486,487,488,487,487,487,487,488,488,489,489,489,489,489,489,489,
489,489,490,490,490,490,490,490,490,490,490,490,490,163,163,163,

/* block 39 */
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,
491,491,491,491,491,491,491,491,491,491,163,163,163,163,163,163,
492,492,492,492,492,492,492,492,492,492,492,492,492,492,492,492,
492,492,492,492,492,492,492,492,492,492,492,492,492,492,492,492,
492,492,492,492,492,492,492,492,492,492,492,492,492,492,492,492,
492,492,492,492,492,492,492,492,492,492,492,492,492,492,492,492,
492,492,492,492,492,492,492,492,492,492,492,492,492,492,492,492,
493,493,493,493,493,493,163,163,494,494,494,494,494,494,163,163,

/* block 40 */
495,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,

/* block 41 */
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,

/* block 42 */
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,497,498,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,

/* block 43 */
499,500,500,500,500,500,500,500,500,500,500,500,500,500,500,500,
500,500,500,500,500,500,500,500,500,500,500,501,502,163,163,163,
503,503,503,503,503,503,503,503,503,503,503,503,503,503,503,503,
503,503,503,503,503,503,503,503,503,503,503,503,503,503,503,503,
503,503,503,503,503,503,503,503,503,503,503,503,503,503,503,503,
503,503,503,503,503,503,503,503,503,503,503,503,503,503,503,503,
503,503,503,503,503,503,503,503,503,503,503,504,504,504,505,505,
505,503,503,503,503,503,503,503,503,163,163,163,163,163,163,163,

/* block 44 */
506,506,506,506,506,506,506,506,506,506,506,506,506,506,506,506,
506,506,507,507,508,509,163,163,163,163,163,163,163,163,163,506,
510,510,510,510,510,510,510,510,510,510,510,510,510,510,510,510,
510,510,511,511,512,513,513,163,163,163,163,163,163,163,163,163,
514,514,514,514,514,514,514,514,514,514,514,514,514,514,514,514,
514,514,515,515,163,163,163,163,163,163,163,163,163,163,163,163,
516,516,516,516,516,516,516,516,516,516,516,516,516,163,516,516,
516,163,517,517,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 45 */
518,518,518,518,518,518,518,518,518,518,518,518,518,518,518,518,
518,518,518,518,518,518,518,518,518,518,518,518,518,518,518,518,
518,518,518,519,519,518,518,518,518,518,518,518,518,518,518,518,
518,518,518,518,520,520,521,522,522,522,522,522,522,522,521,521,
521,521,521,521,521,521,522,521,521,523,523,523,523,523,523,523,
523,523,524,523,525,525,525,526,527,527,525,528,518,523,163,163,
529,529,529,529,529,529,529,529,529,529,163,163,163,163,163,163,
530,530,530,530,530,530,530,530,530,530,163,163,163,163,163,163,

/* block 46 */
531,531,532,533,534,532,535,531,534,536,537,538,538,538,539,538,
540,540,540,540,540,540,540,540,540,540,163,163,163,163,163,163,
541,541,541,541,541,541,541,541,541,541,541,541,541,541,541,541,
541,541,541,541,541,541,541,541,541,541,541,541,541,541,541,541,
541,541,541,542,541,541,541,541,541,541,541,541,541,541,541,541,
541,541,541,541,541,541,541,541,541,541,541,541,541,541,541,541,
541,541,541,541,541,541,541,541,541,541,541,541,541,541,541,541,
541,541,541,541,541,541,541,541,541,163,163,163,163,163,163,163,

/* block 47 */
541,541,541,541,541,543,543,541,541,541,541,541,541,541,541,541,
541,541,541,541,541,541,541,541,541,541,541,541,541,541,541,541,
541,541,541,541,541,541,541,541,541,544,541,163,163,163,163,163,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
496,496,496,496,496,496,163,163,163,163,163,163,163,163,163,163,

/* block 48 */
545,545,545,545,545,545,545,545,545,545,545,545,545,545,545,545,
545,545,545,545,545,545,545,545,545,545,545,545,545,545,545,163,
546,546,546,547,547,547,547,546,546,547,547,547,163,163,163,163,
547,547,546,547,547,547,547,547,547,548,548,548,163,163,163,163,
549,163,163,163,550,550,551,551,551,551,551,551,551,551,551,551,
552,552,552,552,552,552,552,552,552,552,552,552,552,552,552,552,
552,552,552,552,552,552,552,552,552,552,552,552,552,552,163,163,
552,552,552,552,552,163,163,163,163,163,163,163,163,163,163,163,

/* block 49 */
553,553,553,553,553,553,553,553,553,553,553,553,553,553,553,553,
553,553,553,553,553,553,553,553,553,553,553,553,553,553,553,553,
553,553,553,553,553,553,553,553,553,553,553,553,163,163,163,163,
553,553,553,553,553,554,554,554,553,553,554,553,553,553,553,553,
553,553,553,553,553,553,553,553,553,553,163,163,163,163,163,163,
555,555,555,555,555,555,555,555,555,555,556,163,163,163,557,557,
558,558,558,558,558,558,558,558,558,558,558,558,558,558,558,558,
558,558,558,558,558,558,558,558,558,558,558,558,558,558,558,558,

/* block 50 */
559,559,559,559,559,559,559,559,559,559,559,559,559,559,559,559,
559,559,559,559,559,559,559,560,560,561,561,560,163,163,562,562,
563,563,563,563,563,563,563,563,563,563,563,563,563,563,563,563,
563,563,563,563,563,563,563,563,563,563,563,563,563,563,563,563,
563,563,563,563,563,563,563,563,563,563,563,563,563,563,563,563,
563,563,563,563,563,564,565,564,565,565,565,565,565,565,565,163,
566,567,565,567,567,565,565,565,565,565,565,565,565,564,564,564,
564,564,564,565,565,568,568,568,568,568,568,568,568,163,163,568,

/* block 51 */
569,569,569,569,569,569,569,569,569,569,163,163,163,163,163,163,
569,569,569,569,569,569,569,569,569,569,163,163,163,163,163,163,
570,570,570,570,570,570,570,571,572,572,572,572,570,570,163,163,
154,154,154,154,154,154,154,154,154,154,154,154,154,154,573,574,
574,154,154,154,154,154,154,154,154,154,154,154,574,574,574,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 52 */
575,575,575,575,576,577,577,577,577,577,577,577,577,577,577,577,
577,577,577,577,577,577,577,577,577,577,577,577,577,577,577,577,
577,577,577,577,577,577,577,577,577,577,577,577,577,577,577,577,
577,577,577,577,578,579,575,575,575,575,575,576,575,576,576,576,
576,576,575,576,580,577,577,577,577,577,577,577,577,163,163,163,
581,581,581,581,581,581,581,581,581,581,582,582,583,584,582,582,
583,585,585,585,585,585,585,585,585,585,585,578,578,578,578,578,
578,578,578,578,585,585,585,585,585,585,585,585,585,582,582,163,

/* block 53 */
586,586,587,588,588,588,588,588,588,588,588,588,588,588,588,588,
588,588,588,588,588,588,588,588,588,588,588,588,588,588,588,588,
588,587,586,586,586,586,587,587,586,586,589,590,586,586,588,588,
591,591,591,591,591,591,591,591,591,591,588,588,588,588,588,588,
592,592,592,592,592,592,592,592,592,592,592,592,592,592,592,592,
592,592,592,592,592,592,592,592,592,592,592,592,592,592,592,592,
592,592,592,592,592,592,593,594,595,595,594,594,594,595,594,595,
595,595,596,596,163,163,163,163,163,163,163,163,597,597,597,597,

/* block 54 */
598,598,598,598,598,598,598,598,598,598,598,598,598,598,598,598,
598,598,598,598,598,598,598,598,598,598,598,598,598,598,598,598,
598,598,598,598,599,599,599,599,599,599,599,599,600,600,600,600,
600,600,600,600,599,599,601,602,163,163,163,603,603,604,604,604,
605,605,605,605,605,605,605,605,605,605,163,163,163,598,598,598,
606,606,606,606,606,606,606,606,606,606,607,607,607,607,607,607,
607,607,607,607,607,607,607,607,607,607,607,607,607,607,607,607,
607,607,607,607,607,607,607,607,608,608,608,609,608,608,610,610,

/* block 55 */
611,612,613,614,615,616,617,618,619,163,163,163,163,163,163,163,
620,620,620,620,620,620,620,620,620,620,620,620,620,620,620,620,
620,620,620,620,620,620,620,620,620,620,620,620,620,620,620,620,
620,620,620,620,620,620,620,620,620,620,620,163,163,620,620,620,
621,621,621,621,621,621,621,621,163,163,163,163,163,163,163,163,
622,623,622,624,623,625,625,626,625,626,627,623,626,626,623,623,
626,628,623,623,623,623,623,623,623,629,630,631,631,625,631,631,
631,631,632,633,634,630,630,635,636,636,637,163,163,163,163,163,

/* block 56 */
 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70,
 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70,
 70, 70, 70, 70, 70, 70,221,221,221,221,221,638,147,147,147,147,
147,147,147,147,147,147,147,147,147,147,147,147,147,147,147,147,
147,147,147,147,147,147,147,147,147,147,147,147,147,147,147,147,
147,147,147,147,147,147,147,147,147,147,147,147,147,639,639,639,
639,639,148,147,147,147,639,639,639,639,639, 70, 70, 70, 70, 70,
 70, 70, 70, 70, 70, 70, 70, 70,640,641, 70, 70, 70,642, 70, 70,

/* block 57 */
 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70,643, 70,
 70, 70, 70, 70, 70, 70,644, 70, 70, 70, 70,645,645,645,645,645,
645,645,645,645,646,645,645,645,646,645,645,645,645,645,645,645,
645,645,645,645,645,645,645,645,645,645,645,645,645,645,645,647,
648,648,158,158,154,154,154,154,154,154,154,154,154,154,154,154,
158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,
158,158,158,158,158,158,158,574,574,574,574,574,574,574,574,574,
574,574,574,574,574,154,154,154,649,154,650,154,154,154,154,154,

/* block 58 */
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 67, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
651,652, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,

/* block 59 */
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 69, 69, 69, 69,653,654, 70, 70,655, 70,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 67, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,

/* block 60 */
656,656,656,656,656,656,656,656,657,657,657,657,657,657,657,657,
656,656,656,656,656,656,163,163,657,657,657,657,657,657,163,163,
656,656,656,656,656,656,656,656,657,657,657,657,657,657,657,657,
656,656,656,656,656,656,656,656,657,657,657,657,657,657,657,657,
656,656,656,656,656,656,163,163,657,657,657,657,657,657,163,163,
173,656,173,656,173,656,173,656,163,657,163,657,163,657,163,657,
656,656,656,656,656,656,656,656,657,657,657,657,657,657,657,657,
658,658,659,659,659,659,660,660,661,661,662,662,663,663,163,163,

/* block 61 */
664,664,664,664,664,664,664,664,665,665,665,665,665,665,665,665,
664,664,664,664,664,664,664,664,665,665,665,665,665,665,665,665,
664,664,664,664,664,664,664,664,665,665,665,665,665,665,665,665,
656,656,666,667,666,163,173,666,657,657,668,668,669,162,670,162,
162,162,666,667,666,163,173,666,671,671,671,671,669,162,162,162,
656,656,173,173,163,163,173,173,657,657,672,672,163,162,162,162,
656,656,173,173,173,215,173,173,657,657,673,673,220,162,162,162,
163,163,666,667,666,163,173,666,674,674,675,675,669,162,162,163,

/* block 62 */
676,676,676,676,676,676,676,676,676,676,676, 51,677,678,679,680,
681,681,681,681,681,681,682, 43,683,684,685,686,686,687,685,686,
 43, 43, 43, 43,688, 43, 43,688,689,690,691,692,693,694,695,696,
697,697,698,698,698, 43, 43, 43, 43, 49, 57, 43,699,700, 43,701,
702, 43, 43, 43,703,704,705,700,700,699, 43, 43, 43, 43, 43, 43,
 43, 43, 50,706,701, 43, 43, 43, 43, 43, 43, 43, 43, 43, 43,676,
 51,707,707,707,707,708,709,710,711,712,713,713,713,713,713,713,
 54,646,163,163, 54, 54, 54, 54, 54, 54,714,715,716,717,718,645,

/* block 63 */
 54, 54, 54, 54, 54, 54, 54, 54, 54, 54,714,715,716,717,718,163,
645,645,645,645,645,645,645,645,645,645,645,645,645,163,163,163,
430,430,430,430,430,430,430,430,430,430,430,430,430,430,430,430,
430,430,430,430,430,430,430,430,430,430,430,430,430,430,430,430,
430,719,719,719,719,719,719,719,719,719,719,719,719,719,719,719,
720,720,720,720,720,720,720,720,720,720,720,720,720,721,721,721,
721,720,721,722,721,720,720,158,158,158,158,720,720,720,720,720,
723,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 64 */
724,724,725,724,724,724,724,725,724,724,726,725,725,725,726,726,
725,725,725,726,724,725,724,724,727,725,725,725,725,725,724,724,
724,724,728,724,725,724,729,724,725,730,731,732,725,725,733,726,
725,725,734,725,726,735,735,735,735,736,724,724,726,726,725,725,
716,716,716,716,716,725,726,726,737,737,724,716,724,724,738,461,
 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58,
739,739,739,739,739,739,739,739,739,739,739,739,739,739,739,739,
740,740,740,740,740,740,740,740,740,740,740,740,740,740,740,740,

/* block 65 */
741,741,741, 65, 66,741,741,741,741, 58,724,724,163,163,163,163,
 50, 50, 50, 50,742,743,743,743,743,743, 50, 50,744,744,744,744,
 50,744,744, 50,744,744, 50,744, 45,743,743,744,744,744, 50, 45,
744,744, 45, 45, 45, 45,744,744, 45, 45, 45, 45,744,744,744,744,
744,744,744,744,744,744,744,744,744,744,744,744,744,744, 50, 50,
744,744, 50,744, 50,744,744,744,744,744,744,744, 45,744, 45, 45,
 45, 45, 45, 45,744,744, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,

/* block 66 */
 50, 50, 50, 50, 50, 50, 50, 50,745,745,745,745,745,745, 50, 50,
 50, 50,746, 53, 50,745, 50, 50, 50, 50, 50, 50, 50, 50, 50,745,
745,745,745, 50,745, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,745,745, 50, 50,
 50, 50, 50,745, 50,745, 50, 50, 50, 50, 50, 50,745, 50, 50, 50,
 50, 50,745,745,745,745, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50,745,745,745,745,745,745,745,745, 50, 50,745,745,
745,745,745,745,745,745,745,745,745,745,745,745,745,745,745,745,

/* block 67 */
745,745,745,745,745,745,745,745,745,745,745,745, 50, 50, 50,745,
745,745,745, 50, 50, 50, 50, 50,745, 50, 50, 50, 50, 50, 50, 50,
 50, 50,745,745, 50, 50,745, 50,745,745, 50,745, 50, 50, 50, 50,
745,745,745,745,745,745,745,745,745, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50, 50, 50, 50, 50, 50,745,745,745,745,745, 50, 50,
745,745, 50, 50, 50, 50,745,745,745,745,745,745,745,745,745,745,
745,745,745,745,745,745,745,745,745,745,745,745,745,745, 50, 50,
745,745,745,745,745, 50,745,745, 50, 50,745,745,745,745,745, 50,

/* block 68 */
 45, 45, 45, 45, 45, 45, 45, 45,747,748,747,748, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,749,749, 45, 45, 45, 45,
 50, 50, 45, 45, 45, 45, 45, 45, 47,750,751, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45,752,752,752,752,752,752,752,752,752,752,
752,752,752,752,752,752,752,752,752,752,752,752,752,752,752,752,
752,752,752,752,752,752,752,752,752,752,752,752,752,752,752,752,
752,752,752,752,752,752,752,752,752,752,752,752,752,752,752,752,
752,752,752,752,752,752,752,752,752,752,752, 45, 50, 45, 45, 45,

/* block 69 */
 45, 45, 45, 45, 45, 45, 45, 45,753, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45,752, 45, 45, 45, 45, 45, 50, 50, 50, 50, 50,
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50,744,744, 45,744, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 47,
744, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 50, 50, 50, 50,
 50, 50,744, 45, 45, 45, 45, 45, 45,749,749,749,749, 47, 47, 47,
749, 47, 47,749, 45, 45, 45, 45, 47, 47, 47, 45, 45, 45, 45, 45,

/* block 70 */
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45,754,754,754,754,754,754,754,754,754,
754,754,754,754,754,754,754,754,754,754,754,754,754,754,754,754,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,754,754,754,754,754,
754,754,754,754,754,754,754,754,754,754,754,754,754,754,754,754,
 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58,
 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58,

/* block 71 */
 58, 58, 58, 58, 58, 58, 58, 58, 54, 54, 54, 54, 54, 54, 54, 54,
 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,755,755,755,755,755,755,755,755,755,755,
755,755,756,755,755,755,755,755,755,755,755,755,755,755,755,755,
757,757,757,757,757,757,757,757,757,757,757,757,757,757,757,757,
757,757,757,757,757,757,757,757,757,757, 58, 58, 58, 58, 58, 58,
 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58,

/* block 72 */
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,

/* block 73 */
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
744,744, 45, 45, 45, 45, 45, 45, 45, 45, 47, 47, 45, 45,744,744,
744,744,744,744,744,744,743, 50, 45, 45, 45, 45,744,744,744,744,
743, 50, 45, 45, 45, 45,744,744, 45, 45,744,744, 45, 45, 45,744,
744,744,744,744, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45,744, 45,744, 45, 45,744,744,744,744,744,744, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 50, 50, 50,742,742,758,758, 50,

/* block 74 */
 47, 47, 47, 47, 47,759,744,753,753,753,753,753,753,753, 47,753,
753, 47,753, 45,749,749,753,753, 47,753,753,753,753,760,753,753,
 47,753, 47, 47,753,753, 47,753,753,753, 47,753,753,753, 47, 47,
753,753,753,753,753,753,753,753, 47, 47, 47,753,753,753,753,753,
743,753,743,753,753,753,753,753,749,749,749,749,749,749,749,749,
749,749,749,749,753,753,753,753,753,753,753,753,753,753,753, 47,
743,759,759,743,753, 47, 47,753, 47,753,753,753,753,759,759,761,
753,753,753,753,753,753,753,753,753,753,753, 47,753,753, 47,749,

/* block 75 */
753,753,753,753,753,753, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
753,753, 47,749, 47, 47, 47, 47,753, 47,753, 47, 47,753,753,753,
 47,749,753,753,753,753,753, 47,753,753,749,749,762,753,753,753,
 47, 47,753,753,753,753,753,753,753,753,753,753,753,749,749,753,
753,753,753,753,749,749,753,753, 47,753,753,753,753,753,749, 47,
753, 47,753, 47,749,753,753,753,753,753,753,753,753,753,753,753,
753,753,753,753,753,753,753,753,753, 47,749,753,753,753,753,753,
 47, 47,749,749, 47,749,753, 47, 47,760,749,753,753,749,753,753,

/* block 76 */
753,753, 47,753,753,749, 45, 45, 47, 47,763,763,760,760,753, 47,
753,753, 47, 45, 47, 45, 47, 45, 45, 45, 45, 45, 45, 47, 45, 45,
 45, 47, 45, 45, 45, 45, 45, 45,749, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 47, 47, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 47, 45, 45, 47, 45, 45, 45, 45,749, 45,749, 45,
 45, 45, 45,749,749,749, 45,749, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 47, 47,753,753,753,704,705,704,705,704,705,704,705,
704,705,704,705,704,705, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58,

/* block 77 */
 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58,
 58, 58, 58, 58, 45,749,749,749, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 47, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
749, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,749,
 50, 50, 50,745,745,747,748, 50,745,745, 50,745, 50,745, 50, 50,
 50, 50, 50, 50, 50,745,745, 50, 50, 50, 50, 50,745,745,745, 50,
 50, 50,745,745,745,745,747,748,747,748,747,748,747,748,747,748,
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,

/* block 78 */
764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,
764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,
764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,
764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,
764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,
764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,
764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,
764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,764,

/* block 79 */
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50,742,742, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,

/* block 80 */
 50, 50, 50,747,748,747,748,747,748,747,748,747,748,747,748,747,
748,747,748,747,748,747,748,747,748, 50, 50,745, 50, 50, 50, 50,
745, 50, 50,745,745,745, 50, 50,745,745,745,745,745,745,745,745,
 50, 50, 50, 50, 50, 50, 50, 50,745, 50, 50, 50, 50, 50, 50, 50,
745,745, 50, 50,745,745, 50, 50, 50, 50, 50, 50, 50, 50, 50,745,
745,745,745, 50,745,745, 50, 50,747,748,747,748, 50, 50, 50, 50,
 50, 50, 50, 50, 50, 50, 50, 50,745,745, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50, 50,745, 50, 50,745,745, 50, 50,747,748, 50, 50,

/* block 81 */
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,745,745,745,745, 50,
 50, 50, 50, 50,745,745, 50, 50, 50, 50, 50, 50,745,745, 50, 50,
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50,745,745, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50, 50, 50, 50, 50, 50,745,745,745,745,745,745,745,

/* block 82 */
745,745,745,745,745,745,745,745,745,745,745,745,745,745,745,745,
745,745,745,745,745,745,745,745,745,745,745,745,745,745,745,745,
745,745,745, 50, 50, 50,745,745,745,745,745,745,745,745, 50,745,
745,745,745,745,745,745,745,745,745,745,745,745,745,745,745,745,
745,745,745,745,745,745,745,745,745,745,745,745,745,745,745,745,
745,745,745,745,745,745,745, 50, 50, 50, 50, 50, 50, 50,745, 50,
 50, 50, 50,745,745,745, 50, 50, 50, 50, 50, 50,745,745,745, 50,
 50, 50, 50, 50, 50, 50, 50,745,745,745,745, 50, 50, 50, 50, 50,

/* block 83 */
 45, 45, 45, 45, 45, 47, 47, 47, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,749,749, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50,
 50, 50, 50, 50, 50, 45, 45, 50, 50, 50, 50, 50, 50, 45, 45, 45,
749, 45, 45, 45, 45,749, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45,754,754, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,

/* block 84 */
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45,754, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,
 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45, 45,765, 45,

/* block 85 */
766,766,766,766,766,766,766,766,766,766,766,766,766,766,766,766,
766,766,766,766,766,766,766,766,766,766,766,766,766,766,766,766,
766,766,766,766,766,766,766,766,766,766,766,766,766,766,766,766,
767,767,767,767,767,767,767,767,767,767,767,767,767,767,767,767,
767,767,767,767,767,767,767,767,767,767,767,767,767,767,767,767,
767,767,767,767,767,767,767,767,767,767,767,767,767,767,767,767,
 65, 66,768,769,770,771,772, 65, 66, 65, 66, 65, 66,773,774,775,
776, 70, 65, 66, 70, 65, 66, 70, 70, 70, 70, 70,646,645,777,777,

/* block 86 */
211,212,211,212,211,212,211,212,211,212,211,212,211,212,211,212,
211,212,211,212,211,212,211,212,211,212,211,212,211,212,211,212,
211,212,211,212,211,212,211,212,211,212,211,212,211,212,211,212,
211,212,211,212,211,212,211,212,211,212,211,212,211,212,211,212,
211,212,211,212,211,212,211,212,211,212,211,212,211,212,211,212,
211,212,211,212,211,212,211,212,211,212,211,212,211,212,211,212,
211,212,211,212,778,779,779,779,779,779,779,211,212,211,212,780,
780,780,211,212,163,163,163,163,163,781,781,781,781,782,781,781,

/* block 87 */
783,783,783,783,783,783,783,783,783,783,783,783,783,783,783,783,
783,783,783,783,783,783,783,783,783,783,783,783,783,783,783,783,
783,783,783,783,783,783,163,783,163,163,163,163,163,783,163,163,
784,784,784,784,784,784,784,784,784,784,784,784,784,784,784,784,
784,784,784,784,784,784,784,784,784,784,784,784,784,784,784,784,
784,784,784,784,784,784,784,784,784,784,784,784,784,784,784,784,
784,784,784,784,784,784,784,784,163,163,163,163,163,163,163,785,
786,163,163,163,163,163,163,163,163,163,163,163,163,163,163,787,

/* block 88 */
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,
484,484,484,484,484,484,484,163,163,163,163,163,163,163,163,163,
484,484,484,484,484,484,484,163,484,484,484,484,484,484,484,163,
484,484,484,484,484,484,484,163,484,484,484,484,484,484,484,163,
484,484,484,484,484,484,484,163,484,484,484,484,484,484,484,163,
484,484,484,484,484,484,484,163,484,484,484,484,484,484,484,163,
788,788,788,788,788,788,788,788,788,788,788,788,788,788,788,788,
788,788,788,788,788,788,788,788,788,788,788,788,788,788,788,788,

/* block 89 */
 43, 43,789,790,789,790, 43, 43, 43,789,790, 43,789,790, 43, 43,
 43, 43, 43, 43, 43, 43, 43,681, 43, 43,681, 43,789,790, 43, 43,
789,790,704,705,704,705,704,705,704,705, 43, 43, 43, 43,700,791,
 43, 43, 43, 43, 43, 43, 43, 43, 43, 43,681,681,700, 43, 43, 43,
681,792,685,793, 43, 43, 43, 43, 43, 43, 43, 43,792, 43,792,792,
 45, 45, 43,700,700,704,705,704,705,704,705,704,705,681,754,754,
754,754,754,754,754,754,754,754,754,754,754,754,754,754,754,754,
754,754,754,754,754,754,754,754,754,754,754,754,754,754,754,754,

/* block 90 */
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,163,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 91 */
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,

/* block 92 */
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,794,
794,794,794,794,794,794,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
795,795,796,796,795,795,795,795,795,795,795,795,163,163,163,163,

/* block 93 */
676,797,798,799,724,800,801,802,803,804,803,804,805,806,805,806,
803,804, 45,807,803,804,803,804,803,804,803,804,808,809,810,810,
 45,802,802,802,802,802,802,802,802,802,811,811,811,811,812,812,
813,814,814,814,814,814,724,815,802,802,802,816,817,818,819,819,
163,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,

/* block 94 */
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,163,163,821,821,822,822,823,823,820,
824,825,825,825,825,825,825,825,825,825,825,825,825,825,825,825,
825,825,825,825,825,825,825,825,825,825,825,825,825,825,825,825,
825,825,825,825,825,825,825,825,825,825,825,825,825,825,825,825,
825,825,825,825,825,825,825,825,825,825,825,825,825,825,825,825,
825,825,825,825,825,825,825,825,825,825,825,825,825,825,825,825,
825,825,825,825,825,825,825,825,825,825,825,826,827,828,828,825,

/* block 95 */
163,163,163,163,163,829,829,829,829,829,829,829,829,829,829,829,
829,829,829,829,829,829,829,829,829,829,829,829,829,829,829,829,
829,829,829,829,829,829,829,829,829,829,829,829,829,829,829,829,
163,830,830,830,830,830,830,830,830,830,830,830,830,830,830,830,
830,830,830,830,830,830,830,830,830,830,830,830,830,830,830,830,
830,830,830,830,830,830,830,830,830,830,830,830,830,830,830,830,
830,830,830,830,831,830,830,830,830,830,830,830,830,830,830,830,
830,830,830,830,830,830,830,830,830,830,830,830,830,830,830,830,

/* block 96 */
830,830,830,830,830,830,830,830,830,830,830,830,830,830,830,163,
832,832,833,833,833,833,832,832,832,832,832,832,832,832,832,832,
829,829,829,829,829,829,829,829,829,829,829,829,829,829,829,829,
829,829,829,829,829,829,829,829,829,829,829,829,829,829,829,829,
819,819,819,819,819,819,819,819,819,819,819,819,819,819,819,819,
819,819,819,819,819,819,819,819,819,819,819,819,819,819,819,819,
819,819,819,819,163,163,163,163,163,163,163,163,163,163,163,163,
825,825,825,825,825,825,825,825,825,825,825,825,825,825,825,825,

/* block 97 */
834,834,834,834,834,834,834,834,834,834,834,834,834,834,834,834,
834,834,834,834,834,834,834,834,834,834,834,834,834,835,835,163,
833,833,833,833,833,833,833,833,833,833,832,832,832,832,832,832,
832,832,832,832,832,832,832,832,832,832,832,832,832,832,832,832,
832,832,832,832,832,832,832,832,836,836,836,836,836,836,836,836,
724, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58,
834,834,834,834,834,834,834,834,834,834,834,834,834,834,834,834,
834,834,834,834,834,834,834,834,834,834,834,834,835,835,835,461,

/* block 98 */
833,833,833,833,833,833,833,833,833,833,832,832,832,832,832,832,
832,832,832,832,832,832,832,837,832,837,832,832,832,832,832,832,
832,832,832,832,832,832,832,832,832,832,832,832,832,832,832,832,
832, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58, 58,
832,832,832,832,832,832,832,832,832,832,832,832,724,724,724,724,
838,838,838,838,838,838,838,838,838,838,838,838,838,838,838,838,
838,838,838,838,838,838,838,838,838,838,838,838,838,838,838,838,
838,838,838,838,838,838,838,838,838,838,838,838,838,838,838,832,

/* block 99 */
838,838,838,838,838,838,838,838,838,838,838,838,838,838,838,838,
838,838,838,838,838,838,838,838,838,838,838,838,838,838,838,838,
838,838,838,838,838,838,838,838,838,838,838,838,838,838,838,838,
838,838,838,838,838,838,838,838,838,838,838,838,838,838,838,838,
838,838,838,838,838,838,838,838,838,838,838,838,838,838,838,838,
838,838,838,838,838,838,838,838,832,832,832,832,832,832,832,832,
832,832,832,832,832,832,832,832,832,832,832,832,832,832,832,832,
832,461,461,461,461,461,461,724,724,724,724,832,832,832,832,832,

/* block 100 */
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,724,724,
832,832,832,832,832,832,832,832,832,832,832,832,832,832,832,832,
832,832,832,832,832,832,832,832,832,832,832,832,832,832,832,724,

/* block 101 */
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,

/* block 102 */
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,

/* block 103 */
840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,
840,840,840,840,840,841,840,840,840,840,840,840,840,840,840,840,
840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,
840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,
840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,
840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,
840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,
840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,

/* block 104 */
840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,
840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,
840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,
840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,
840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,
840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,
840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,
840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,840,

/* block 105 */
840,840,840,840,840,840,840,840,840,840,840,840,840,163,163,163,
842,842,842,842,842,842,842,842,842,842,842,842,842,842,842,842,
842,842,842,842,842,842,842,842,842,842,842,842,842,842,842,842,
842,842,842,842,842,842,842,842,842,842,842,842,842,842,842,842,
842,842,842,842,842,842,842,163,163,163,163,163,163,163,163,163,
843,843,843,843,843,843,843,843,843,843,843,843,843,843,843,843,
843,843,843,843,843,843,843,843,843,843,843,843,843,843,843,843,
843,843,843,843,843,843,843,843,844,844,844,844,844,844,845,846,

/* block 106 */
847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,
847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,
847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,
847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,
847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,
847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,
847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,
847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,

/* block 107 */
847,847,847,847,847,847,847,847,847,847,847,847,848,849,850,850,
847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,847,
851,851,851,851,851,851,851,851,851,851,847,847,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
240,241,240,241,240,241,240,241,240,241,852,853,240,241,240,241,
240,241,240,241,240,241,240,241,240,241,240,241,240,241,240,241,
240,241,240,241,240,241,240,241,240,241,240,241,240,241,854,246,
248,248,248,855,788,788,788,788,788,788,788,788,856,856,855,857,

/* block 108 */
240,241,240,241,240,241,240,241,240,241,240,241,240,241,240,241,
240,241,240,241,240,241,240,241,240,241,240,241,858,858,788,788,
859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,
859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,
859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,
859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,
859,859,859,859,859,859,860,860,860,860,860,860,860,860,860,860,
861,861,862,863,864,864,864,863,163,163,163,163,163,163,163,163,

/* block 109 */
865,865,865,865,865,865,865,865, 46, 46, 46, 46, 46, 46, 46, 46,
 46, 46, 46, 46, 46, 46, 46,149,149,149,149,149,149,149,149,149,
 46, 46, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 70, 70, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
645, 70, 70, 70, 70, 70, 70, 70, 70, 65, 66, 65, 66,866, 65, 66,

/* block 110 */
 65, 66, 65, 66, 65, 66, 65, 66,149,867,867, 65, 66,868, 70, 92,
 65, 66, 65, 66,869, 70, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,870,871,872,873,870, 70,
874,875,876,877, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66, 65, 66,
 65, 66, 65, 66,878,879,880, 65, 66, 65, 66,163,163,163,163,163,
 65, 66,163, 70,163, 70, 65, 66, 65, 66,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,645,645,645, 65, 66, 92,147,147, 70, 92, 92, 92, 92, 92,

/* block 111 */
881,881,882,881,881,881,883,881,881,881,881,882,881,881,881,881,
881,881,881,881,881,881,881,881,881,881,881,881,881,881,881,881,
881,881,881,884,884,882,882,884,885,885,885,885,883,163,163,163,
886,886,886,887,887,887,888,888,889,890,163,163,163,163,163,163,
891,891,891,891,891,891,891,891,891,891,891,891,891,891,891,891,
891,891,891,891,891,891,891,891,891,891,891,891,891,891,891,891,
891,891,891,891,891,891,891,891,891,891,891,891,891,891,891,891,
891,891,891,891,892,892,893,893,163,163,163,163,163,163,163,163,

/* block 112 */
894,894,895,895,895,895,895,895,895,895,895,895,895,895,895,895,
895,895,895,895,895,895,895,895,895,895,895,895,895,895,895,895,
895,895,895,895,895,895,895,895,895,895,895,895,895,895,895,895,
895,895,895,895,894,894,894,894,894,894,894,894,894,894,894,894,
894,894,894,894,896,897,163,163,163,163,163,163,163,163,898,898,
899,899,899,899,899,899,899,899,899,899,163,163,163,163,163,163,
336,336,336,336,336,336,336,336,336,336,336,336,336,336,336,336,
336,900,335,901,335,335,335,335,343,343,343,335,343,335,335,333,

/* block 113 */
902,902,902,902,902,902,902,902,902,902,903,903,903,903,903,903,
903,903,903,903,903,903,903,903,903,903,903,903,903,903,903,903,
903,903,903,903,903,903,904,904,904,904,904,905,905,905,906,907,
908,908,908,908,908,908,908,908,908,908,908,908,908,908,908,908,
908,908,908,908,908,908,908,909,909,909,909,909,909,909,909,909,
909,909,910,911,163,163,163,163,163,163,163,163,163,163,163,912,
479,479,479,479,479,479,479,479,479,479,479,479,479,479,479,479,
479,479,479,479,479,479,479,479,479,479,479,479,479,163,163,163,

/* block 114 */
913,913,913,914,915,915,915,915,915,915,915,915,915,915,915,915,
915,915,915,915,915,915,915,915,915,915,915,915,915,915,915,915,
915,915,915,915,915,915,915,915,915,915,915,915,915,915,915,915,
915,915,915,916,914,914,913,913,913,913,914,914,913,913,914,914,
917,918,918,918,918,918,918,919,920,920,918,918,918,918,163,921,
922,922,922,922,922,922,922,922,922,922,163,163,163,163,918,918,
462,462,462,462,462,472,923,462,462,462,462,462,462,462,462,462,
473,473,473,473,473,473,473,473,473,473,462,462,462,462,462,163,

/* block 115 */
924,924,924,924,924,924,924,924,924,924,924,924,924,924,924,924,
924,924,924,924,924,924,924,924,924,924,924,924,924,924,924,924,
924,924,924,924,924,924,924,924,924,925,925,925,925,925,925,926,
926,925,925,926,926,925,925,163,163,163,163,163,163,163,163,163,
924,924,924,925,924,924,924,924,924,924,924,924,925,926,163,163,
927,927,927,927,927,927,927,927,927,927,163,163,928,929,929,929,
462,462,462,462,462,462,462,462,462,462,462,462,462,462,462,462,
923,462,462,462,462,462,462,474,474,474,462,471,472,471,462,462,

/* block 116 */
930,930,930,930,930,930,930,930,930,930,930,930,930,930,930,930,
930,930,930,930,930,930,930,930,930,930,930,930,930,930,930,930,
930,930,930,930,930,930,930,930,930,930,930,930,930,930,930,930,
931,930,931,931,931,932,932,931,931,932,930,932,932,930,931,933,
934,933,934,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,930,930,935,936,937,
938,938,938,938,938,938,938,938,938,938,938,939,940,940,939,939,
941,941,938,942,942,939,943,163,163,163,163,163,163,163,163,163,

/* block 117 */
163,484,484,484,484,484,484,163,163,484,484,484,484,484,484,163,
163,484,484,484,484,484,484,163,163,163,163,163,163,163,163,163,
484,484,484,484,484,484,484,163,484,484,484,484,484,484,484,163,
 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70,
 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 70,
 70, 70, 70,944, 70, 70, 70, 70, 70, 70, 70,867,147,147,147,147,
 70, 70, 70, 70, 70,221, 70, 70, 70,147, 46, 46,163,163,163,163,
945,945,945,945,945,945,945,945,945,945,945,945,945,945,945,945,

/* block 118 */
945,945,945,945,945,945,945,945,945,945,945,945,945,945,945,945,
945,945,945,945,945,945,945,945,945,945,945,945,945,945,945,945,
945,945,945,945,945,945,945,945,945,945,945,945,945,945,945,945,
945,945,945,945,945,945,945,945,945,945,945,945,945,945,945,945,
938,938,938,938,938,938,938,938,938,938,938,938,938,938,938,938,
938,938,938,938,938,938,938,938,938,938,938,938,938,938,938,938,
938,938,938,939,939,940,939,939,940,939,939,941,946,943,163,163,
947,947,947,947,947,947,947,947,947,947,163,163,163,163,163,163,

/* block 119 */
948,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,948,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,948,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,948,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
948,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,

/* block 120 */
949,949,949,949,949,949,949,949,949,949,949,949,948,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,948,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,948,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
948,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,948,949,949,949,

/* block 121 */
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,948,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,948,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
948,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,948,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,

/* block 122 */
949,949,949,949,949,949,949,949,948,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,948,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
948,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,948,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,948,949,949,949,949,949,949,949,

/* block 123 */
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,948,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
948,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,948,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,948,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,

/* block 124 */
949,949,949,949,948,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
948,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,948,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,948,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,948,949,949,949,949,949,949,949,949,949,949,949,

/* block 125 */
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
948,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,948,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,948,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,948,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,

/* block 126 */
949,949,949,949,949,949,949,949,948,949,949,949,949,949,949,949,
949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,949,
949,949,949,949,163,163,163,163,163,163,163,163,163,163,163,163,
482,482,482,482,482,482,482,482,482,482,482,482,482,482,482,482,
482,482,482,482,482,482,482,163,163,163,163,483,483,483,483,483,
483,483,483,483,483,483,483,483,483,483,483,483,483,483,483,483,
483,483,483,483,483,483,483,483,483,483,483,483,483,483,483,483,
483,483,483,483,483,483,483,483,483,483,483,483,163,163,163,163,

/* block 127 */
950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,
950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,
950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,
950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,
950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,
950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,
950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,
950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,950,

/* block 128 */
951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,
951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,
951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,
951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,
951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,
951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,
951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,
951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,

/* block 129 */
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,

/* block 130 */
952,952,952,952,952,952,952,952,952,952,952,952,952,952,839,839,
952,839,952,839,839,952,952,952,952,952,952,952,952,952,952,839,
952,839,952,839,839,952,952,839,839,839,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,163,163,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,

/* block 131 */
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 132 */
653,653,653,653,653,653,653,163,163,163,163,163,163,163,163,163,
163,163,163,257,257,257,257,257,163,163,163,163,163,270,265,270,
270,270,270,270,270,270,270,270,270,953,270,270,270,270,270,270,
270,270,270,270,270,270,270,262,270,270,270,270,270,262,270,262,
270,270,262,270,270,262,270,270,270,270,270,270,270,270,270,270,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,

/* block 133 */
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,331,331,331,331,331,331,331,331,331,331,331,331,331,331,
331,331,331,302,302,302,302,302,302,302,302,302,302,302,302,302,
302,302,302,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,

/* block 134 */
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,954,954,
954,954,954,954,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,

/* block 135 */
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,

/* block 136 */
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,955,956,
280,280,280,280,280,280,280,280,280,280,280,280,280,280,280,280,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,

/* block 137 */
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
302,302,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,302,302,302,302,302,302,302,280,
957,957,957,957,957,957,957,957,957,957,957,957,957,957,957,957,
957,957,957,957,957,957,957,957,957,957,957,957,957,957,957,957,
286,286,958,286,286,286,286,286,286,286,954,954,277,959,280,280,

/* block 138 */
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,961,
962,962,962,963,962,962,962,964,965,962,163,163,163,163,163,163,
154,154,154,154,154,154,154,154,154,154,154,154,154,154,856,856,
962,966,966,701,701,964,965,964,965,964,965,964,965,964,965,964,
965,967,968,967,968,799,799,964,965,962,962,962,962,701,701,701,
969,166,970,163,166,971,972,972,966,973,974,973,974,973,974,975,
962,976,714,977,978,978,716,163,976,430,975,962,163,163,163,163,
954,286,954,286,954,302,954,286,954,286,954,286,954,286,954,286,

/* block 139 */
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,286,
286,286,286,286,286,286,286,286,286,286,286,286,286,302,302, 51,

/* block 140 */
163,972,979,975,430,975,962,980,973,974,962,714,969,981,970,982,
983,983,983,983,983,983,983,983,983,983,971,166,978,716,978,972,
962,984,984,984,984,984,984, 59, 59, 59, 59, 59, 59, 59, 59, 59,
 59, 59, 59, 59, 59, 59, 59, 59, 59, 59, 59,973,976,974,985,701,
 46,986,986,986,986,986,986, 62, 62, 62, 62, 62, 62, 62, 62, 62,
 62, 62, 62, 62, 62, 62, 62, 62, 62, 62, 62,973,716,974,716,973,
974,987,988,989,990,826,825,825,825,825,825,825,825,825,825,825,
827,825,825,825,825,825,825,825,825,825,825,825,825,825,825,825,

/* block 141 */
825,825,825,825,825,825,825,825,825,825,825,825,825,825,825,825,
825,825,825,825,825,825,825,825,825,825,825,825,825,825,991,991,
831,830,830,830,830,830,830,830,830,830,830,830,830,830,830,830,
830,830,830,830,830,830,830,830,830,830,830,830,830,830,830,163,
163,163,830,830,830,830,830,830,163,163,830,830,830,830,830,830,
163,163,830,830,830,830,830,830,163,163,830,830,830,163,163,163,
430,430,716, 46,724,430,430,163,724,716,716,716,716,724,724,163,
708,708,708,708,708,708,708,708,708,992,992,992,724,724,957,957,

/* block 142 */
993,993,993,993,993,993,993,993,993,993,993,993,163,993,993,993,
993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,
993,993,993,993,993,993,993,163,993,993,993,993,993,993,993,993,
993,993,993,993,993,993,993,993,993,993,993,163,993,993,163,993,
993,993,993,993,993,993,993,993,993,993,993,993,993,993,163,163,
993,993,993,993,993,993,993,993,993,993,993,993,993,993,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 143 */
993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,
993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,
993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,
993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,
993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,
993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,
993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,993,
993,993,993,993,993,993,993,993,993,993,993,163,163,163,163,163,

/* block 144 */
994,995,996,163,163,163,163,997,997,997,997,997,997,997,997,997,
997,997,997,997,997,997,997,997,997,997,997,997,997,997,997,997,
997,997,997,997,997,997,997,997,997,997,997,997,997,997,997,997,
997,997,997,997,163,163,163,998,998,998,998,998,998,998,998,998,
999,999,999,999,999,999,999,999,999,999,999,999,999,999,999,999,
999,999,999,999,999,999,999,999,999,999,999,999,999,999,999,999,
999,999,999,999,999,999,999,999,999,999,999,999,999,999,999,999,
999,999,999,999,999,1000,1000,1000,1000,1001,1001,1001,1001,1001,1001,1001,

/* block 145 */
1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1000,1000,1001,1002,1002,163,
724,724,724,724,724,724,724,724,724,724,724,724,724,163,163,163,
1001,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,158,163,163,

/* block 146 */
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 147 */
1003,1003,1003,1003,1003,1003,1003,1003,1003,1003,1003,1003,1003,1003,1003,1003,
1003,1003,1003,1003,1003,1003,1003,1003,1003,1003,1003,1003,1003,163,163,163,
1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,
1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,
1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,1004,
1004,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
1005,1006,1006,1006,1006,1006,1006,1006,1006,1006,1006,1006,1006,1006,1006,1006,
1006,1006,1006,1006,1006,1006,1006,1006,1006,1006,1006,1006,163,163,163,163,

/* block 148 */
1007,1007,1007,1007,1007,1007,1007,1007,1007,1007,1007,1007,1007,1007,1007,1007,
1007,1007,1007,1007,1007,1007,1007,1007,1007,1007,1007,1007,1007,1007,1007,1007,
1008,1008,1008,1008,163,163,163,163,163,163,163,163,163,1007,1007,1007,
1009,1009,1009,1009,1009,1009,1009,1009,1009,1009,1009,1009,1009,1009,1009,1009,
1009,1010,1009,1009,1009,1009,1009,1009,1009,1009,1010,163,163,163,163,163,
1011,1011,1011,1011,1011,1011,1011,1011,1011,1011,1011,1011,1011,1011,1011,1011,
1011,1011,1011,1011,1011,1011,1011,1011,1011,1011,1011,1011,1011,1011,1011,1011,
1011,1011,1011,1011,1011,1011,1012,1012,1012,1012,1012,163,163,163,163,163,

/* block 149 */
1013,1013,1013,1013,1013,1013,1013,1013,1013,1013,1013,1013,1013,1013,1013,1013,
1013,1013,1013,1013,1013,1013,1013,1013,1013,1013,1013,1013,1013,1013,163,1014,
1015,1015,1015,1015,1015,1015,1015,1015,1015,1015,1015,1015,1015,1015,1015,1015,
1015,1015,1015,1015,1015,1015,1015,1015,1015,1015,1015,1015,1015,1015,1015,1015,
1015,1015,1015,1015,163,163,163,163,1015,1015,1015,1015,1015,1015,1015,1015,
1016,1017,1017,1017,1017,1017,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 150 */
1018,1018,1018,1018,1018,1018,1018,1018,1018,1018,1018,1018,1018,1018,1018,1018,
1018,1018,1018,1018,1018,1018,1018,1018,1018,1018,1018,1018,1018,1018,1018,1018,
1018,1018,1018,1018,1018,1018,1018,1018,1019,1019,1019,1019,1019,1019,1019,1019,
1019,1019,1019,1019,1019,1019,1019,1019,1019,1019,1019,1019,1019,1019,1019,1019,
1019,1019,1019,1019,1019,1019,1019,1019,1019,1019,1019,1019,1019,1019,1019,1019,
1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,
1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,
1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,1020,

/* block 151 */
1021,1021,1021,1021,1021,1021,1021,1021,1021,1021,1021,1021,1021,1021,1021,1021,
1021,1021,1021,1021,1021,1021,1021,1021,1021,1021,1021,1021,1021,1021,163,163,
1022,1022,1022,1022,1022,1022,1022,1022,1022,1022,163,163,163,163,163,163,
1023,1023,1023,1023,1023,1023,1023,1023,1023,1023,1023,1023,1023,1023,1023,1023,
1023,1023,1023,1023,1023,1023,1023,1023,1023,1023,1023,1023,1023,1023,1023,1023,
1023,1023,1023,1023,163,163,163,163,1024,1024,1024,1024,1024,1024,1024,1024,
1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,
1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,163,163,163,163,

/* block 152 */
1025,1025,1025,1025,1025,1025,1025,1025,1025,1025,1025,1025,1025,1025,1025,1025,
1025,1025,1025,1025,1025,1025,1025,1025,1025,1025,1025,1025,1025,1025,1025,1025,
1025,1025,1025,1025,1025,1025,1025,1025,163,163,163,163,163,163,163,163,
1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,
1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,
1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,1026,
1026,1026,1026,1026,163,163,163,163,163,163,163,163,163,163,163,1027,
1028,1028,1028,1028,1028,1028,1028,1028,1028,1028,1028,163,1028,1028,1028,1028,

/* block 153 */
1028,1028,1028,1028,1028,1028,1028,1028,1028,1028,1028,163,1028,1028,1028,1028,
1028,1028,1028,163,1028,1028,163,1029,1029,1029,1029,1029,1029,1029,1029,1029,
1029,1029,163,1029,1029,1029,1029,1029,1029,1029,1029,1029,1029,1029,1029,1029,
1029,1029,163,1029,1029,1029,1029,1029,1029,1029,163,1029,1029,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 154 */
1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,
1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,
1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,
1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,
1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,
1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,
1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,
1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,

/* block 155 */
1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,
1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,
1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,
1030,1030,1030,1030,1030,1030,1030,163,163,163,163,163,163,163,163,163,
1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,1030,
1030,1030,1030,1030,1030,1030,163,163,163,163,163,163,163,163,163,163,
1030,1030,1030,1030,1030,1030,1030,1030,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 156 */
147,1031,1031,147,147,147,163,147,147,147,147,147,147,147,147,147,
147,147,147,147,147,147,147,147,147,147,147,147,147,147,147,147,
147,147,147,147,147,147,147,147,147,147,147,147,147,147,147,147,
147,163,147,147,147,147,147,147,147,147,147,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 157 */
1032,1032,1032,1032,1032,1032,262,262,1032,262,1032,1032,1032,1032,1032,1032,
1032,1032,1032,1032,1032,1032,1032,1032,1032,1032,1032,1032,1032,1032,1032,1032,
1032,1032,1032,1032,1032,1032,1032,1032,1032,1032,1032,1032,1032,1032,1032,1032,
1032,1032,1032,1032,1032,1032,262,1032,1032,262,262,262,1032,262,262,1032,
1033,1033,1033,1033,1033,1033,1033,1033,1033,1033,1033,1033,1033,1033,1033,1033,
1033,1033,1033,1033,1033,1033,262,1034,1035,1035,1035,1035,1035,1035,1035,1035,
1036,1036,1036,1036,1036,1036,1036,1036,1036,1036,1036,1036,1036,1036,1036,1036,
1036,1036,1036,1036,1036,1036,1036,1037,1037,1038,1038,1038,1038,1038,1038,1038,

/* block 158 */
1039,1039,1039,1039,1039,1039,1039,1039,1039,1039,1039,1039,1039,1039,1039,1039,
1039,1039,1039,1039,1039,1039,1039,1039,1039,1039,1039,1039,1039,1039,1039,262,
262,262,262,262,262,262,262,1040,1040,1040,1040,1040,1040,1040,1040,1040,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
1041,1041,1041,1041,1041,1041,1041,1041,1041,1041,1041,1041,1041,1041,1041,1041,
1041,1041,1041,262,1041,1041,262,262,262,262,262,1042,1042,1042,1042,1042,

/* block 159 */
1043,1043,1043,1043,1043,1043,1043,1043,1043,1043,1043,1043,1043,1043,1043,1043,
1043,1043,1043,1043,1043,1043,1044,1044,1044,1044,1044,1044,262,262,262,1045,
1046,1046,1046,1046,1046,1046,1046,1046,1046,1046,1046,1046,1046,1046,1046,1046,
1046,1046,1046,1046,1046,1046,1046,1046,1046,1046,262,262,262,262,262,1047,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,

/* block 160 */
1048,1048,1048,1048,1048,1048,1048,1048,1048,1048,1048,1048,1048,1048,1048,1048,
1048,1048,1048,1048,1048,1048,1048,1048,1048,1048,1048,1048,1048,1048,1048,1048,
1049,1049,1049,1049,1049,1049,1049,1049,1049,1049,1049,1049,1049,1049,1049,1049,
1049,1049,1049,1049,1049,1049,1049,1049,262,262,262,262,1050,1050,1049,1049,
1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,
262,262,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,
1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,
1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,1050,

/* block 161 */
1051,1052,1052,1052,262,1052,1052,262,262,262,262,262,1052,1052,1052,1052,
1051,1051,1051,1051,262,1051,1051,1051,262,1051,1051,1051,1051,1051,1051,1051,
1051,1051,1051,1051,1051,1051,1051,1051,1051,1051,1051,1051,1051,1051,1051,1051,
1051,1051,1051,1051,1051,1051,262,262,1053,1053,1053,262,262,262,262,1054,
1055,1055,1055,1055,1055,1055,1055,1055,1055,262,262,262,262,262,262,262,
1056,1056,1056,1056,1056,1056,1057,1057,1056,262,262,262,262,262,262,262,
1058,1058,1058,1058,1058,1058,1058,1058,1058,1058,1058,1058,1058,1058,1058,1058,
1058,1058,1058,1058,1058,1058,1058,1058,1058,1058,1058,1058,1058,1059,1059,1060,

/* block 162 */
1061,1061,1061,1061,1061,1061,1061,1061,1061,1061,1061,1061,1061,1061,1061,1061,
1061,1061,1061,1061,1061,1061,1061,1061,1061,1061,1061,1061,1061,1062,1062,1062,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
1063,1063,1063,1063,1063,1063,1063,1063,1064,1063,1063,1063,1063,1063,1063,1063,
1063,1063,1063,1063,1063,1063,1063,1063,1063,1063,1063,1063,1063,1063,1063,1063,
1063,1063,1063,1063,1063,1065,1065,262,262,262,262,1066,1066,1066,1066,1066,
1067,1067,1068,1067,1067,1067,1069,262,262,262,262,262,262,262,262,262,

/* block 163 */
1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,
1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,
1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,1070,
1070,1070,1070,1070,1070,1070,262,262,262,1071,1072,1072,1072,1072,1072,1072,
1073,1073,1073,1073,1073,1073,1073,1073,1073,1073,1073,1073,1073,1073,1073,1073,
1073,1073,1073,1073,1073,1073,262,262,1074,1074,1074,1074,1074,1074,1074,1074,
1075,1075,1075,1075,1075,1075,1075,1075,1075,1075,1075,1075,1075,1075,1075,1075,
1075,1075,1075,262,262,262,262,262,1076,1076,1076,1076,1076,1076,1076,1076,

/* block 164 */
1077,1077,1077,1077,1077,1077,1077,1077,1077,1077,1077,1077,1077,1077,1077,1077,
1077,1077,262,262,262,262,262,262,262,1078,1078,1078,1078,262,262,262,
262,262,262,262,262,262,262,262,262,1079,1079,1079,1079,1079,1079,1079,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,

/* block 165 */
1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,
1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,
1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,
1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,1080,
1080,1080,1080,1080,1080,1080,1080,1080,1080,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,

/* block 166 */
1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,
1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,
1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,1081,
1081,1081,1081,262,262,262,262,262,262,262,262,262,262,262,262,262,
1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,
1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,
1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,1082,
1082,1082,1082,262,262,262,262,262,262,262,1083,1083,1083,1083,1083,1083,

/* block 167 */
1084,1084,1084,1084,1084,1084,1084,1084,1084,1084,1084,1084,1084,1084,1084,1084,
1084,1084,1084,1084,1084,1084,1084,1084,1084,1084,1084,1084,1084,1084,1084,1084,
1084,1084,1085,1085,1086,1086,1086,1086,302,302,302,302,302,302,302,302,
1087,1087,1087,1087,1087,1087,1087,1087,1087,1087,302,302,302,302,302,302,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,

/* block 168 */
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,

/* block 169 */
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
1088,1088,1088,1088,1088,1088,1088,1088,1088,1088,1088,1088,1088,1088,1088,1088,
1088,1088,1088,1088,1088,1088,1088,1088,1088,1088,1088,1088,1088,1088,1088,262,

/* block 170 */
1089,1089,1089,1089,1089,1089,1089,1089,1089,1089,1089,1089,1089,1089,1089,1089,
1089,1089,1089,1089,1089,1089,1089,1089,1089,1089,1089,1089,1089,1089,1089,1089,
1089,1089,1089,1089,1089,1089,1089,1089,1089,1089,262,1090,1090,1091,262,262,
1089,1089,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,
302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,
302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,
302,302,302,302,302,302,302,302,302,302,302,302,302,291,291,291,

/* block 171 */
1092,1092,1092,1092,1092,1092,1092,1092,1092,1092,1092,1092,1092,1092,1092,1092,
1092,1092,1092,1092,1092,1092,1092,1092,1092,1092,1092,1092,1092,1093,1093,1093,
1093,1093,1093,1093,1093,1093,1093,1092,262,262,262,262,262,262,262,262,
1094,1094,1094,1094,1094,1094,1094,1094,1094,1094,1094,1094,1094,1094,1094,1094,
1094,1094,1094,1094,1094,1094,1095,1095,1095,1095,1095,1095,1095,1095,1095,1095,
1095,1096,1096,1096,1096,1097,1097,1097,1097,1097,302,302,302,302,302,302,
302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,
1098,1098,1098,1098,1098,1098,1098,1098,1098,1098,1098,1098,1098,1098,1098,1098,

/* block 172 */
1098,1098,1099,1099,1099,1099,1100,1100,1100,1100,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
1101,1101,1101,1101,1101,1101,1101,1101,1101,1101,1101,1101,1101,1101,1101,1101,
1101,1101,1101,1101,1101,1102,1102,1102,1102,1102,1102,1102,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
1103,1103,1103,1103,1103,1103,1103,1103,1103,1103,1103,1103,1103,1103,1103,1103,
1103,1103,1103,1103,1103,1103,1103,262,262,262,262,262,262,262,262,262,

/* block 173 */
1104,1105,1104,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,
1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,
1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,1106,
1106,1106,1106,1106,1106,1106,1106,1106,1105,1105,1105,1105,1105,1105,1105,1105,
1105,1105,1105,1105,1105,1105,1107,1108,1108,1109,1109,1109,1109,1109,163,163,
163,163,1110,1110,1110,1110,1110,1110,1110,1110,1110,1110,1110,1110,1110,1110,
1110,1110,1110,1110,1110,1110,1111,1111,1111,1111,1111,1111,1111,1111,1111,1111,
1107,1106,1106,1105,1105,1106,163,163,163,163,163,163,163,163,163,1112,

/* block 174 */
1113,1113,1114,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,
1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,
1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,1115,
1114,1114,1114,1113,1113,1113,1113,1114,1114,1116,1117,1118,1118,1119,1120,1120,
1120,1120,1113,163,163,163,163,163,163,163,163,163,163,1119,163,163,
1121,1121,1121,1121,1121,1121,1121,1121,1121,1121,1121,1121,1121,1121,1121,1121,
1121,1121,1121,1121,1121,1121,1121,1121,1121,163,163,163,163,163,163,163,
1122,1122,1122,1122,1122,1122,1122,1122,1122,1122,163,163,163,163,163,163,

/* block 175 */
1123,1123,1123,1124,1124,1124,1124,1124,1124,1124,1124,1124,1124,1124,1124,1124,
1124,1124,1124,1124,1124,1124,1124,1124,1124,1124,1124,1124,1124,1124,1124,1124,
1124,1124,1124,1124,1124,1124,1124,1123,1123,1123,1123,1123,1125,1123,1123,1123,
1123,1123,1123,1126,1126,163,1127,1127,1127,1127,1127,1127,1127,1127,1127,1127,
1128,1129,1129,1129,1124,1125,1125,1124,163,163,163,163,163,163,163,163,
1130,1130,1130,1130,1130,1130,1130,1130,1130,1130,1130,1130,1130,1130,1130,1130,
1130,1130,1130,1130,1130,1130,1130,1130,1130,1130,1130,1130,1130,1130,1130,1130,
1130,1130,1130,1131,1132,1132,1130,163,163,163,163,163,163,163,163,163,

/* block 176 */
1133,1133,1134,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,
1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,
1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,1135,
1135,1135,1135,1134,1134,1134,1133,1133,1133,1133,1133,1133,1133,1133,1133,1134,
1136,1135,1137,1137,1135,1138,1138,1139,1139,1140,1141,1141,1141,1138,1134,1133,
1142,1142,1142,1142,1142,1142,1142,1142,1142,1142,1135,1139,1135,1139,1138,1138,
163,1143,1143,1143,1143,1143,1143,1143,1143,1143,1143,1143,1143,1143,1143,1143,
1143,1143,1143,1143,1143,163,163,163,163,163,163,163,163,163,163,163,

/* block 177 */
1144,1144,1144,1144,1144,1144,1144,1144,1144,1144,1144,1144,1144,1144,1144,1144,
1144,1144,163,1144,1144,1144,1144,1144,1144,1144,1144,1144,1144,1144,1144,1144,
1144,1144,1144,1144,1144,1144,1144,1144,1144,1144,1144,1144,1145,1145,1145,1146,
1146,1146,1145,1145,1146,1147,1148,1146,1149,1149,1150,1149,1149,1151,1146,1144,
1144,1146,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 178 */
1152,1152,1152,1152,1152,1152,1152,163,1152,163,1152,1152,1152,1152,163,1152,
1152,1152,1152,1152,1152,1152,1152,1152,1152,1152,1152,1152,1152,1152,163,1152,
1152,1152,1152,1152,1152,1152,1152,1152,1152,1153,163,163,163,163,163,163,
1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,
1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,
1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1154,1155,
1156,1156,1156,1155,1155,1155,1155,1155,1155,1157,1158,163,163,163,163,163,
1159,1159,1159,1159,1159,1159,1159,1159,1159,1159,163,163,163,163,163,163,

/* block 179 */
1160,1161,1162,1163,163,1164,1164,1164,1164,1164,1164,1164,1164,163,163,1164,
1164,163,163,1164,1164,1164,1164,1164,1164,1164,1164,1164,1164,1164,1164,1164,
1164,1164,1164,1164,1164,1164,1164,1164,1164,163,1164,1164,1164,1164,1164,1164,
1164,163,1164,1164,163,1164,1164,1164,1164,1164,163,1165,1166,1164,1167,1162,
1160,1162,1162,1162,1162,163,163,1162,1162,163,163,1162,1162,1168,163,163,
1164,163,163,163,163,163,163,1167,163,163,163,163,163,1169,1164,1164,
1164,1164,1162,1162,163,163,1170,1170,1170,1170,1170,1170,1170,163,163,163,
1170,1170,1170,1170,1170,163,163,163,163,163,163,163,163,163,163,163,

/* block 180 */
1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,
1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,
1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,1171,
1171,1171,1171,1171,1171,1172,1172,1172,1173,1173,1173,1173,1173,1173,1173,1173,
1172,1172,1174,1173,1173,1172,1175,1171,1171,1171,1171,1176,1176,1177,1178,1178,
1179,1179,1179,1179,1179,1179,1179,1179,1179,1179,1177,1177,163,1178,1180,1171,
1171,1171,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 181 */
1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,
1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,
1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,1181,
1182,1183,1183,1184,1184,1184,1184,1184,1184,1183,1184,1183,1183,1182,1183,1184,
1184,1183,1185,1186,1181,1181,1187,1181,163,163,163,163,163,163,163,163,
1188,1188,1188,1188,1188,1188,1188,1188,1188,1188,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 182 */
1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,
1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,
1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1189,1190,
1191,1191,1192,1192,1192,1192,163,163,1191,1191,1191,1191,1192,1192,1191,1193,
1194,1195,1196,1196,1197,1197,1198,1198,1198,1196,1196,1196,1196,1196,1196,1196,
1196,1196,1196,1196,1196,1196,1196,1196,1189,1189,1189,1189,1192,1192,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 183 */
1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,
1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,
1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,1199,
1200,1200,1200,1201,1201,1201,1201,1201,1201,1201,1201,1200,1200,1201,1200,1202,
1201,1203,1203,1204,1199,163,163,163,163,163,163,163,163,163,163,163,
1205,1205,1205,1205,1205,1205,1205,1205,1205,1205,163,163,163,163,163,163,
531,531,531,531,531,531,531,531,531,531,531,531,531,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 184 */
1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,
1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,
1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,1206,1207,1208,1207,1208,1208,
1207,1207,1207,1207,1207,1207,1209,1210,1206,1211,163,163,163,163,163,163,
1212,1212,1212,1212,1212,1212,1212,1212,1212,1212,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 185 */
1213,1213,1213,1213,1213,1213,1213,1213,1213,1213,1213,1213,1213,1213,1213,1213,
1213,1213,1213,1213,1213,1213,1213,1213,1213,1213,1213,163,163,1214,1214,1214,
1215,1215,1214,1214,1214,1214,1216,1214,1214,1214,1214,1217,163,163,163,163,
1218,1218,1218,1218,1218,1218,1218,1218,1218,1218,1219,1219,1220,1220,1220,1221,
1213,1213,1213,1213,1213,1213,1213,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 186 */
1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,
1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,
1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,1222,1223,1223,1223,1224,
1224,1224,1224,1224,1224,1224,1224,1224,1223,1225,1226,1227,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 187 */
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
1228,1228,1228,1228,1228,1228,1228,1228,1228,1228,1228,1228,1228,1228,1228,1228,
1228,1228,1228,1228,1228,1228,1228,1228,1228,1228,1228,1228,1228,1228,1228,1228,
1229,1229,1229,1229,1229,1229,1229,1229,1229,1229,1229,1229,1229,1229,1229,1229,
1229,1229,1229,1229,1229,1229,1229,1229,1229,1229,1229,1229,1229,1229,1229,1229,
1230,1230,1230,1230,1230,1230,1230,1230,1230,1230,1231,1231,1231,1231,1231,1231,
1231,1231,1231,163,163,163,163,163,163,163,163,163,163,163,163,1232,

/* block 188 */
1233,1233,1233,1233,1233,1233,1233,163,163,1233,163,163,1233,1233,1233,1233,
1233,1233,1233,1233,163,1233,1233,163,1233,1233,1233,1233,1233,1233,1233,1233,
1233,1233,1233,1233,1233,1233,1233,1233,1233,1233,1233,1233,1233,1233,1233,1233,
1234,1235,1235,1235,1235,1235,163,1235,1235,163,163,1236,1236,1237,1238,1239,
1235,1239,1235,1240,1241,1242,1241,163,163,163,163,163,163,163,163,163,
1243,1243,1243,1243,1243,1243,1243,1243,1243,1243,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 189 */
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
1244,1244,1244,1244,1244,1244,1244,1244,163,163,1244,1244,1244,1244,1244,1244,
1244,1244,1244,1244,1244,1244,1244,1244,1244,1244,1244,1244,1244,1244,1244,1244,
1244,1244,1244,1244,1244,1244,1244,1244,1244,1244,1244,1244,1244,1244,1244,1244,
1244,1245,1245,1245,1246,1246,1246,1246,163,163,1246,1246,1245,1245,1245,1245,
1247,1244,1248,1244,1245,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 190 */
1249,1250,1250,1250,1250,1250,1250,1251,1251,1250,1250,1249,1249,1249,1249,1249,
1249,1249,1249,1249,1249,1249,1249,1249,1249,1249,1249,1249,1249,1249,1249,1249,
1249,1249,1249,1249,1249,1249,1249,1249,1249,1249,1249,1249,1249,1249,1249,1249,
1249,1249,1249,1252,1253,1250,1250,1250,1250,1254,1255,1250,1250,1250,1250,1256,
1256,1256,1257,1257,1256,1256,1256,1253,163,163,163,163,163,163,163,163,
1258,1259,1259,1259,1259,1259,1259,1260,1260,1259,1259,1259,1258,1258,1258,1258,
1258,1258,1258,1258,1258,1258,1258,1258,1258,1258,1258,1258,1258,1258,1258,1258,
1258,1258,1258,1258,1258,1258,1258,1258,1258,1258,1258,1258,1258,1258,1258,1258,

/* block 191 */
1258,1258,1258,1258,1261,1261,1261,1261,1261,1261,1259,1259,1259,1259,1259,1259,
1259,1259,1259,1259,1259,1259,1259,1260,1262,1263,1264,1265,1265,1258,1264,1264,
1264,1266,1266,163,163,163,163,163,163,163,163,163,163,163,163,163,
496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,496,
1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,
1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,
1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,1267,
1267,1267,1267,1267,1267,1267,1267,1267,1267,163,163,163,163,163,163,163,

/* block 192 */
343,343,343,343,343,343,343,343,343,343,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 193 */
1268,1268,1268,1268,1268,1268,1268,1268,1268,163,1268,1268,1268,1268,1268,1268,
1268,1268,1268,1268,1268,1268,1268,1268,1268,1268,1268,1268,1268,1268,1268,1268,
1268,1268,1268,1268,1268,1268,1268,1268,1268,1268,1268,1268,1268,1268,1268,1269,
1270,1270,1270,1270,1270,1270,1270,163,1270,1270,1270,1270,1270,1270,1269,1271,
1268,1272,1272,1273,1274,1274,163,163,163,163,163,163,163,163,163,163,
1275,1275,1275,1275,1275,1275,1275,1275,1275,1275,1276,1276,1276,1276,1276,1276,
1276,1276,1276,1276,1276,1276,1276,1276,1276,1276,1276,1276,1276,163,163,163,
1277,1278,1279,1279,1279,1279,1279,1279,1279,1279,1279,1279,1279,1279,1279,1279,

/* block 194 */
1279,1279,1279,1279,1279,1279,1279,1279,1279,1279,1279,1279,1279,1279,1279,1279,
163,163,1280,1280,1280,1280,1280,1280,1280,1280,1280,1280,1280,1280,1280,1280,
1280,1280,1280,1280,1280,1280,1280,1280,163,1281,1280,1280,1280,1280,1280,1280,
1280,1281,1280,1280,1281,1280,1280,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 195 */
1282,1282,1282,1282,1282,1282,1282,163,1282,1282,163,1282,1282,1282,1282,1282,
1282,1282,1282,1282,1282,1282,1282,1282,1282,1282,1282,1282,1282,1282,1282,1282,
1282,1282,1282,1282,1282,1282,1282,1282,1282,1282,1282,1282,1282,1282,1282,1282,
1282,1283,1283,1283,1283,1283,1283,163,163,163,1283,163,1283,1283,163,1283,
1283,1283,1284,1283,1285,1285,1286,1283,163,163,163,163,163,163,163,163,
1287,1287,1287,1287,1287,1287,1287,1287,1287,1287,163,163,163,163,163,163,
1288,1288,1288,1288,1288,1288,163,1288,1288,163,1288,1288,1288,1288,1288,1288,
1288,1288,1288,1288,1288,1288,1288,1288,1288,1288,1288,1288,1288,1288,1288,1288,

/* block 196 */
1288,1288,1288,1288,1288,1288,1288,1288,1288,1288,1289,1289,1289,1289,1289,163,
1290,1290,163,1289,1289,1290,1289,1291,1288,163,163,163,163,163,163,163,
1292,1292,1292,1292,1292,1292,1292,1292,1292,1292,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 197 */
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
1293,1293,1293,1293,1293,1293,1293,1293,1293,1293,1293,1293,1293,1293,1293,1293,
1293,1293,1293,1294,1294,1295,1295,1296,1296,163,163,163,163,163,163,163,

/* block 198 */
1297,1297,1298,1299,1300,1300,1300,1300,1300,1300,1300,1300,1300,1300,1300,1300,
1300,163,1300,1300,1300,1300,1300,1300,1300,1300,1300,1300,1300,1300,1300,1300,
1300,1300,1300,1300,1300,1300,1300,1300,1300,1300,1300,1300,1300,1300,1300,1300,
1300,1300,1300,1300,1299,1299,1297,1297,1297,1297,1297,163,163,163,1299,1299,
1297,1301,1302,1303,1303,1304,1304,1304,1304,1304,1304,1304,1304,1304,1304,1304,
1305,1305,1305,1305,1305,1305,1305,1305,1305,1305,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 199 */
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
843,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
1306,1306,1306,1306,1306,1306,1306,1306,1306,1306,1306,1306,1306,1306,1306,1306,
388,388,1306,388,1306,390,390,390,390,390,390,390,390,391,391,391,
391,390,390,390,390,390,390,390,390,390,390,390,390,390,390,390,
390,390,163,163,163,163,163,163,163,163,163,163,163,163,163,1307,

/* block 200 */
1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,
1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,
1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,
1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,
1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,
1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,
1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,
1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,

/* block 201 */
1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,
1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 202 */
1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,
1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,
1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,
1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,
1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,
1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,
1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,1309,163,
1310,1310,1310,1310,1310,163,163,163,163,163,163,163,163,163,163,163,

/* block 203 */
1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,
1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,
1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,
1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,1308,
1308,1308,1308,1308,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 204 */
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,
1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,
1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,
1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,
1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,
1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,1311,
1311,1312,1312,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 205 */
1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,
1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,
1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,
1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,
1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,
1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,
1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,
1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,

/* block 206 */
1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,
1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,
1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,1313,
1314,1314,1314,1314,1314,1314,1314,1314,1314,1314,1314,1314,1314,1314,1314,1314,
1315,1313,1313,1313,1313,1313,1313,1316,1316,1316,1316,1316,1316,1316,1316,1316,
1316,1316,1316,1316,1316,1316,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 207 */
1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,
1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,
1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,
1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,
1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,
1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,
1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,
1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,

/* block 208 */
1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,
1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,
1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,
1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,1317,
1317,1317,1317,1317,1317,1317,1317,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 209 */
859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,
859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,
859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,
859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,
859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,
859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,
859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,
859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,

/* block 210 */
859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,
859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,
859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,859,
859,859,859,859,859,859,859,859,859,163,163,163,163,163,163,163,
1318,1318,1318,1318,1318,1318,1318,1318,1318,1318,1318,1318,1318,1318,1318,1318,
1318,1318,1318,1318,1318,1318,1318,1318,1318,1318,1318,1318,1318,1318,1318,163,
1319,1319,1319,1319,1319,1319,1319,1319,1319,1319,163,163,163,163,1320,1320,
1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,

/* block 211 */
1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,
1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,
1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,
1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,1321,163,
1322,1322,1322,1322,1322,1322,1322,1322,1322,1322,163,163,163,163,163,163,
1323,1323,1323,1323,1323,1323,1323,1323,1323,1323,1323,1323,1323,1323,1323,1323,
1323,1323,1323,1323,1323,1323,1323,1323,1323,1323,1323,1323,1323,1323,163,163,
1324,1324,1324,1324,1324,1325,163,163,163,163,163,163,163,163,163,163,

/* block 212 */
1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,
1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,
1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,
1327,1327,1327,1327,1327,1327,1327,1328,1328,1329,1330,1330,1331,1331,1331,1331,
1332,1332,1333,1333,1328,1331,163,163,163,163,163,163,163,163,163,163,
1334,1334,1334,1334,1334,1334,1334,1334,1334,1334,163,1335,1335,1335,1335,1335,
1335,1335,163,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,
1326,1326,1326,1326,1326,1326,1326,1326,163,163,163,163,163,1326,1326,1326,

/* block 213 */
1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,1326,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 214 */
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
1336,1336,1336,1336,1336,1336,1336,1336,1336,1336,1336,1336,1336,1336,1336,1336,
1336,1336,1336,1336,1336,1336,1336,1336,1336,1336,1336,1336,1336,1336,1336,1336,
1337,1337,1337,1337,1337,1337,1337,1337,1337,1337,1337,1337,1337,1337,1337,1337,
1337,1337,1337,1337,1337,1337,1337,1337,1337,1337,1337,1337,1337,1337,1337,1337,

/* block 215 */
1338,1338,1338,1338,1338,1338,1338,1338,1338,1338,1338,1338,1338,1338,1338,1338,
1338,1338,1338,1338,1338,1338,1338,1339,1340,1341,1341,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 216 */
1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,
1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,
1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,
1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,
1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,1342,163,163,163,163,1343,
1342,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,
1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,
1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,1344,

/* block 217 */
1344,1344,1344,1344,1344,1344,1344,1344,163,163,163,163,163,163,163,1345,
1345,1345,1345,1346,1346,1346,1346,1346,1346,1346,1346,1346,1346,1346,1346,1346,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
1347,1348,1349,800,1350,163,163,163,163,163,163,163,163,163,163,163,
1351,1351,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 218 */
1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,
1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,
1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,
1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,
1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,
1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,
1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,
1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,

/* block 219 */
1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,
1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,
1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,
1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,
1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,
1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,
1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,1352,
1352,1352,1352,1352,1352,1352,1352,1352,163,163,163,163,163,163,163,163,

/* block 220 */
1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,
1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,
1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,
1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,
1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,
1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,
1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,
1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,

/* block 221 */
1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,
1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,
1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,
1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,
1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,1353,
1353,1353,1353,1353,1353,1353,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 222 */
1352,1352,1352,1352,1352,1352,1352,1352,1352,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 223 */
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
1354,1354,1354,1354,163,1354,1354,1354,1354,1354,1354,1354,163,1354,1354,163,

/* block 224 */
825,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,

/* block 225 */
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,

/* block 226 */
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,820,
825,825,825,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,820,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
820,820,820,163,163,825,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,825,825,825,825,163,163,163,163,163,163,163,163,
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,

/* block 227 */
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,

/* block 228 */
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,
1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,1355,163,163,163,163,

/* block 229 */
1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,
1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,
1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,
1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,
1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,
1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,
1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,163,163,163,163,163,
1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,163,163,163,

/* block 230 */
1356,1356,1356,1356,1356,1356,1356,1356,1356,163,163,163,163,163,163,163,
1356,1356,1356,1356,1356,1356,1356,1356,1356,1356,163,163,1357,1358,1359,1360,
1361,1361,1361,1361,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 231 */
154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,
154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,
154,154,154,154,154,154,154,154,154,154,154,154,154,154,163,163,
154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,
154,154,154,154,154,154,154,163,163,163,163,163,163,163,163,163,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,

/* block 232 */
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 233 */
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,

/* block 234 */
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,163,163,163,163,163,163,163,163,163,163,

/* block 235 */
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,163,163,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,1362,1363,154,154,154,461,461,461,1364,1365,1365,
1365,1365,1365, 51, 51, 51, 51, 51, 51, 51, 51,154,154,154,154,154,

/* block 236 */
154,154,154,461,461,154,154,154,154,154,154,154,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,154,154,154,154,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,724,724,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 237 */
1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,
1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,
1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,
1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,1001,
1001,1001,1366,1366,1366,1001,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 238 */
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
836,836,836,836,836,836,836,836,836,836,836,836,836,836,836,836,
836,836,836,836,163,163,163,163,163,163,163,163,163,163,163,163,
836,836,836,836,836,836,836,836,836,836,836,836,836,836,836,836,
836,836,836,836,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 239 */
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,163,163,163,163,163,163,163,163,163,
833,833,833,833,833,833,833,833,833,833,833,833,833,833,833,833,
833,833,836,836,836,836,836,836,836,163,163,163,163,163,163,163,

/* block 240 */
725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,
725,725,725,725,725,725,725,725,725,725,726,726,726,726,726,726,
726,726,737,737,726,726,726,726,726,726,726,726,726,726,726,726,
726,726,726,726,725,725,725,725,725,725,725,725,725,725,725,725,
725,725,725,725,725,725,725,725,725,725,725,725,725,725,726,726,
726,726,726,726,726,163,737,737,726,726,726,726,726,726,726,726,
726,726,726,726,726,726,726,726,725,725,725,725,725,725,725,725,
725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,

/* block 241 */
725,725,726,726,726,726,726,726,726,726,737,737,726,726,726,726,
726,726,726,726,726,726,726,726,726,726,726,726,725,163,725,725,
163,163,725,163,163,725,725,163,163,725,725,725,725,163,725,725,
725,725,725,725,725,725,726,726,726,726,163,726,163,726,737,737,
726,726,726,726,163,726,726,726,726,726,726,726,726,726,726,726,
725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,
725,725,725,725,725,725,725,725,725,725,726,726,726,726,726,726,
726,726,737,737,726,726,726,726,726,726,726,726,726,726,726,726,

/* block 242 */
726,726,726,726,725,725,163,725,725,725,725,163,163,725,725,725,
725,725,725,725,725,163,725,725,725,725,725,725,725,163,726,726,
726,726,726,726,726,726,737,737,726,726,726,726,726,726,726,726,
726,726,726,726,726,726,726,726,725,725,163,725,725,725,725,163,
725,725,725,725,725,163,725,163,163,163,725,725,725,725,725,725,
725,163,726,726,726,726,726,726,726,726,737,737,726,726,726,726,
726,726,726,726,726,726,726,726,726,726,726,726,725,725,725,725,
725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,

/* block 243 */
725,725,725,725,725,725,726,726,726,726,726,726,726,726,737,737,
726,726,726,726,726,726,726,726,726,726,726,726,726,726,726,726,
725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,
725,725,725,725,725,725,725,725,725,725,726,726,726,726,726,726,
726,726,737,737,726,726,726,726,726,726,726,726,726,726,726,726,
726,726,726,726,725,725,725,725,725,725,725,725,725,725,725,725,
725,725,725,725,725,725,725,725,725,725,725,725,725,725,726,726,
726,726,726,726,726,726,737,737,726,726,726,726,726,726,726,726,

/* block 244 */
726,726,726,726,726,726,726,726,725,725,725,725,725,725,725,725,
725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,
725,725,726,726,726,726,726,726,726,726,737,737,726,726,726,726,
726,726,726,726,726,726,726,726,726,726,726,726,725,725,725,725,
725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,
725,725,725,725,725,725,726,726,726,726,726,726,726,726,737,737,
726,726,726,726,726,726,726,726,726,726,726,726,726,726,726,726,
725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,

/* block 245 */
725,725,725,725,725,725,725,725,725,725,726,726,726,726,726,726,
726,726,737,737,726,726,726,726,726,726,726,726,726,726,726,726,
726,726,726,726,726,726,163,163,725,725,725,725,725,725,725,725,
725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,
725,1367,726,726,726,726,726,726,726,726,726,726,726,726,726,726,
726,726,726,726,726,726,726,726,726,726,726,716,726,726,726,726,
726,726,725,725,725,725,725,725,725,725,725,725,725,725,725,725,
725,725,725,725,725,725,725,725,725,725,725,1367,726,726,726,726,

/* block 246 */
726,726,726,726,726,726,726,726,726,726,726,726,726,726,726,726,
726,726,726,726,726,716,726,726,726,726,726,726,725,725,725,725,
725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,
725,725,725,725,725,1367,726,726,726,726,726,726,726,726,726,726,
726,726,726,726,726,726,726,726,726,726,726,726,726,726,726,716,
726,726,726,726,726,726,725,725,725,725,725,725,725,725,725,725,
725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,1367,
726,726,726,726,726,726,726,726,726,726,726,726,726,726,726,726,

/* block 247 */
726,726,726,726,726,726,726,726,726,716,726,726,726,726,726,726,
725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,725,
725,725,725,725,725,725,725,725,725,1367,726,726,726,726,726,726,
726,726,726,726,726,726,726,726,726,726,726,726,726,726,726,726,
726,726,726,716,726,726,726,726,726,726,725,726,163,163,1368,1368,
1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,
1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,
1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,1368,

/* block 248 */
1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,
1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,
1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,
1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,
1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,
1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,
1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,
1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,

/* block 249 */
1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,
1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,
1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,
1370,1370,1370,1370,1370,1370,1370,1369,1369,1369,1369,1370,1370,1370,1370,1370,
1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,
1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,
1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1369,1369,1369,
1369,1369,1369,1369,1369,1370,1369,1369,1369,1369,1369,1369,1369,1369,1369,1369,

/* block 250 */
1369,1369,1369,1369,1370,1369,1369,1371,1372,1371,1371,1373,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,1370,1370,1370,1370,1370,
163,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,1370,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 251 */
 70, 70, 70, 70, 70, 70, 70, 70, 70, 70, 92, 70, 70, 70, 70, 70,
 70, 70, 70, 70, 70, 70, 70, 70, 70, 70,644, 70, 70, 70, 70,163,
163,163,163,163,163, 70, 70, 70, 70, 70, 70,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 252 */
1374,1374,1374,1374,1374,1374,1374,163,1374,1374,1374,1374,1374,1374,1374,1374,
1374,1374,1374,1374,1374,1374,1374,1374,1374,163,163,1374,1374,1374,1374,1374,
1374,1374,163,1374,1374,163,1374,1374,1374,1374,1374,163,163,163,163,163,
858,858,858,858,858,858,858,858,858,858,858,858,858,858,858,858,
858,858,858,858,858,858,858,858,858,858,858,858,1375,1375,858,858,
858,858,858,858,858,858,858,858,858,858,858,858,858,858,858,858,
858,858,858,858,858,858,858,858,1375,858,858,858,858,858,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 253 */
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,788,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 254 */
1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,
1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,
1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,1376,163,163,163,
1377,1377,1377,1377,1377,1377,1377,1378,1378,1378,1378,1378,1379,1379,163,163,
1380,1380,1380,1380,1380,1380,1380,1380,1380,1380,163,163,163,163,1376,1381,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 255 */
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
1382,1382,1382,1382,1382,1382,1382,1382,1382,1382,1382,1382,1382,1382,1382,1382,
1382,1382,1382,1382,1382,1382,1382,1382,1382,1382,1382,1382,1382,1382,1383,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,
1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,
1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,1384,1385,1385,1385,1385,
1386,1386,1386,1386,1386,1386,1386,1386,1386,1386,163,163,163,163,163,1387,

/* block 256 */
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
1388,1388,1388,1388,1388,1388,1388,1388,1388,1388,1388,1388,1388,1388,1388,1388,
1388,1388,1388,1388,1388,1388,1388,1388,1388,1388,1388,1389,1390,1390,1390,1390,
1391,1391,1391,1391,1391,1391,1391,1391,1391,1391,163,163,163,163,163,163,

/* block 257 */
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
484,484,484,484,484,484,484,163,484,484,484,484,163,484,484,163,
484,484,484,484,484,484,484,484,484,484,484,484,484,484,484,163,

/* block 258 */
1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,
1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,
1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,
1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,
1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,
1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,
1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,
1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,

/* block 259 */
1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,
1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,
1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,
1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,1392,
1392,1392,1392,1392,1392,262,262,1393,1393,1393,1393,1393,1393,1393,1393,1393,
1394,1394,1394,1394,1394,1394,1394,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,

/* block 260 */
1395,1395,1395,1395,1395,1395,1395,1395,1395,1395,1395,1395,1395,1395,1395,1395,
1395,1395,1395,1395,1395,1395,1395,1395,1395,1395,1395,1395,1395,1395,1395,1395,
1395,1395,1396,1396,1396,1396,1396,1396,1396,1396,1396,1396,1396,1396,1396,1396,
1396,1396,1396,1396,1396,1396,1396,1396,1396,1396,1396,1396,1396,1396,1396,1396,
1396,1396,1396,1396,1397,1397,1397,1398,1399,1399,1399,1400,262,262,262,262,
1401,1401,1401,1401,1401,1401,1401,1401,1401,1401,262,262,262,262,1402,1402,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,

/* block 261 */
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
302,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,

/* block 262 */
1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,
1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,
1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1404,1403,1403,1403,
1405,1403,1403,1403,1403,302,302,302,302,302,302,302,302,302,302,302,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,

/* block 263 */
302,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,
1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,
1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1404,1403,
1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,1403,302,302,
302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,
262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,262,

/* block 264 */
1406,1406,1406,1406,302,1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,
1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,
302,1406,1406,302,1406,302,302,1406,302,1406,1406,1406,1406,1406,1406,1406,
1406,1406,1406,302,1406,1406,1406,1406,302,1406,302,1406,302,302,302,302,
302,302,1406,302,302,302,302,1406,302,1406,302,1406,302,1406,1406,1406,
302,1406,1406,302,1406,302,302,1406,302,1406,302,1406,302,1406,302,1406,
302,1406,1406,302,1406,302,302,1406,1406,1406,1406,302,1406,1406,1406,1406,
1406,1406,1406,302,1406,1406,1406,1406,302,1406,1406,1406,1406,302,1406,302,

/* block 265 */
1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,302,1406,1406,1406,1406,1406,
1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,302,302,302,302,
302,1406,1406,1406,302,1406,1406,1406,1406,1406,302,1406,1406,1406,1406,1406,
1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,1406,302,302,302,302,
302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,
302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,
302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,302,
274,274,302,302,302,302,302,302,302,302,302,302,302,302,302,302,

/* block 266 */
1407,1407,1407,1407,1408,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1409,1409,1409,1409,
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,

/* block 267 */
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,1407,1407,1407,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1409,
1409,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1409,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1408,
1409,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,1407,1407,1407,1407,1407,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,

/* block 268 */
 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 58, 58,1407,1407,1407,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,1407,
1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,
1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,461,461,461,461,461,461,
1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,
1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,724,724,1407,1407,1407,1407,
1411,1411,1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,1411,1411,

/* block 269 */
1410,1410,1410,1410,1410,1410,1410,1410,1410,1410,461,461,461,461,1412,461,
461,1412,1412,1412,1412,1412,1412,1412,1412,1412,1412,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,1407,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1413,1413,1413,1413,1413,1413,1413,1413,1413,1413,
1413,1413,1413,1413,1413,1413,1413,1413,1413,1413,1413,1413,1413,1413,1413,1413,

/* block 270 */
1414,1412,1415,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
461,461,461,461,461,461,461,461,461,461,1412,461,461,461,461,461,
461,461,461,461,461,461,461,461,461,461,461,461,461,461,461,1412,
461,461,1412,1412,1412,1412,1412,1415,1412,1412,1412,461,1409,1409,1409,1409,
461,461,461,461,461,461,461,461,461,1409,1409,1409,1409,1409,1409,1409,
1416,1416,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1407,1407,1407,1407,1407,1407,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,

/* block 271 */
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,

/* block 272 */
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,728,1407,1407,728,728,728,728,728,728,728,728,728,1408,1408,1408,
1408,1408,1408,1408,1408,1408,728,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,728,1408,1408,

/* block 273 */
1408,1408,1408,1408,1408,1417,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1407,1407,728,728,1407,728,728,728,1407,1407,728,728,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1417,1417,1417,1408,1408,1417,1408,1408,1417,1418,1418,728,728,1408,
1408,1408,1408,1408,728,728,728,728,728,728,728,728,728,728,728,728,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1407,1407,728,1408,728,1407,728,1408,1408,1408,1419,1419,1419,1419,1419,

/* block 274 */
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,728,
1408,728,1417,1417,1408,1408,1417,1417,1417,1417,1417,1417,1417,1417,1417,1417,
1417,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1417,1417,1417,1417,1417,1417,1417,1417,1417,1417,
1417,1417,1417,1417,1417,1417,1417,1417,1417,1408,1408,1408,1417,1408,1408,1408,

/* block 275 */
1408,1417,1417,1417,1408,1417,1417,1417,1408,1408,1408,1408,1408,1408,1408,1417,
1408,1417,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1417,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,728,1407,1408,

/* block 276 */
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,724,724,
724,724,724,724,724,724,1407,1407,1407,728,728,1408,1408,1408,1408,1407,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1407,1407,1407,1407,1407,1407,1407,728,
728,1407,1407,728,1418,1418,728,728,728,728,1417,1407,1407,1407,1407,1407,

/* block 277 */
1407,1407,1407,1407,1407,1407,1407,728,1407,1407,728,728,728,728,1407,1407,
1418,1407,1407,1407,1407,1417,1417,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,1407,1407,1407,1408,728,1407,1407,728,1407,1407,1407,1407,1407,1407,1407,
1407,728,728,1407,1407,1407,1407,1407,1407,1407,1407,1407,728,1407,1407,1407,
1407,1407,728,728,728,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,728,728,728,1407,1407,1407,1407,1407,1407,1407,1407,728,728,728,1407,
1407,728,1407,728,1407,1407,1407,1407,728,1407,1407,1407,1407,1407,1407,728,
1407,1407,1407,728,1407,1407,1407,1407,1407,1407,728,1408,1408,1408,1408,1408,

/* block 278 */
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1417,1417,1417,1408,1408,1408,1417,1417,1417,1417,1417,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,

/* block 279 */
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1417,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1417,1417,1417,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1417,1408,1408,1408,1408,1408,1407,1407,1407,1407,1407,728,1417,728,728,728,
1408,1408,1408,1407,1407,1408,1408,1408,1409,1409,1409,1409,1408,1408,1408,1408,
728,728,728,728,728,728,1407,1407,1407,728,1407,1408,1408,1409,1409,1409,
728,1407,1407,728,1408,1408,1408,1408,1408,1408,1408,1408,1408,1409,1409,1409,

/* block 280 */
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,1407,1407,1407,1409,1409,1409,1409,1407,1407,1407,1407,1407,

/* block 281 */
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,1407,1407,1407,1407,1407,1409,1409,1409,1409,1409,1409,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1409,1409,1409,1409,
1408,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,

/* block 282 */
724,724,724,724,724,724,724,724,724,724,724,724,1409,1409,1409,1409,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,1409,1409,1409,1409,1409,1409,1409,1409,
724,724,724,724,724,724,724,724,724,724,1409,1409,1409,1409,1409,1409,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,

/* block 283 */
724,724,724,724,724,724,724,724,1409,1409,1409,1409,1409,1409,1409,1409,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,1409,1409,
1407,1407,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,

/* block 284 */
724,724,724,724,724,724,724,724,724,724,724,724,1417,1408,1408,1417,
1408,1408,1408,1408,1408,1408,1408,1408,1417,1417,1417,1417,1417,1417,1417,1417,
1408,1408,1408,1408,1408,1408,1417,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1417,1417,1417,1417,1417,1417,1417,1417,1417,1417,1408,724,1417,1417,1417,1408,
1408,1408,1408,1408,1408,1408,724,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1417,1408,1408,1408,1408,1408,1408,1408,1408,

/* block 285 */
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1420,1420,1420,1420,1408,1417,1417,1408,1417,1417,1408,1417,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1417,1417,1417,
1408,1417,1417,1417,1417,1417,1417,1417,1417,1417,1417,1417,1417,1417,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,

/* block 286 */
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,
1407,1407,1407,1407,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1407,1409,1409,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1409,1409,1409,

/* block 287 */
1408,1408,1408,1408,1408,1408,1408,1408,1408,1409,1409,1409,1409,1409,1409,1409,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1409,1408,
1408,1408,1408,1417,1417,1417,1409,1409,1409,1409,1409,1409,1409,1409,1408,1408,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1408,1409,1409,1409,1409,
1408,1408,1408,1408,1408,1408,1408,1408,1408,1409,1409,1409,1409,1409,1409,1409,
1417,1417,1417,1417,1417,1417,1417,1417,1417,1409,1409,1409,1409,1409,1409,1409,

/* block 288 */
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,

/* block 289 */
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,163,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,724,
724,724,724,724,724,724,724,724,724,724,724,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
1421,1421,1421,1421,1421,1421,1421,1421,1421,1421,163,163,163,163,163,163,

/* block 290 */
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,
1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,1409,957,957,

/* block 291 */
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 292 */
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,163,163,163,163,163,163,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,

/* block 293 */
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,163,163,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,

/* block 294 */
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,

/* block 295 */
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 296 */
952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,952,
952,952,952,952,952,952,952,952,952,952,952,952,952,952,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 297 */
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,957,957,

/* block 298 */
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,163,163,163,163,163,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,

/* block 299 */
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,839,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,
163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,

/* block 300 */
708,713,708,708,708,708,708,708,708,708,708,708,708,708,708,708,
708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,
1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,
1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,
1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,
1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,
1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,
1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,1422,

/* block 301 */
708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,
708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,
708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,
708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,
708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,
708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,
708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,
708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,

/* block 302 */
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,

/* block 303 */
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,
960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,960,
708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,708,

/* block 304 */
951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,
951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,
951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,
951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,
951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,
951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,
951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,951,
951,951,951,951,951,951,951,951,951,951,951,951,951,951,957,957,
};

#if UCD_BLOCK_SIZE != 128
#error Please correct UCD_BLOCK_SIZE in pcre2_internal.h
#endif
#endif  /* SUPPORT_UNICODE */

#endif  /* PCRE2_PCRE2TEST */

/* End of pcre2_ucd.c */
