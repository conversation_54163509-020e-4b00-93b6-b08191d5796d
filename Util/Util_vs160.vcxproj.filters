<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Application">
      <UniqueIdentifier>{7ded4303-3a4a-4467-8d3c-148b75487820}</UniqueIdentifier>
    </Filter>
    <Filter Include="Application\Header Files">
      <UniqueIdentifier>{94dbf62d-745b-43b5-ad17-537c46c5b34b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Application\Source Files">
      <UniqueIdentifier>{64bcae61-6772-4d11-b2e2-5507c6e5247e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Configuration">
      <UniqueIdentifier>{ae094d6c-de2a-4968-b8d7-b2f1a26d1da0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Configuration\Header Files">
      <UniqueIdentifier>{c7792d22-a4a4-4f5a-8e5b-4a621538dffd}</UniqueIdentifier>
    </Filter>
    <Filter Include="Configuration\Source Files">
      <UniqueIdentifier>{cc5c7c87-1f1a-4eb3-a3c7-cc8b538dc295}</UniqueIdentifier>
    </Filter>
    <Filter Include="Options">
      <UniqueIdentifier>{06c74c47-8446-4ca1-aed1-9a7a897e867d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Options\Header Files">
      <UniqueIdentifier>{c076a75b-f7cc-4893-a958-536e3bf1f164}</UniqueIdentifier>
    </Filter>
    <Filter Include="Options\Source Files">
      <UniqueIdentifier>{6995b784-af0b-4200-ab7b-8989b08b591d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Windows">
      <UniqueIdentifier>{2489924c-1dba-46bd-89c2-4ccc67ef5a36}</UniqueIdentifier>
    </Filter>
    <Filter Include="Windows\Header Files">
      <UniqueIdentifier>{8d391b72-678f-4e8d-90ff-eead7df1cab2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Windows\Source Files">
      <UniqueIdentifier>{d18f5fbf-f664-415c-864d-fb54aedf9cf6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Util">
      <UniqueIdentifier>{c61b423f-39bf-4a15-8199-202ff5d9febd}</UniqueIdentifier>
    </Filter>
    <Filter Include="Util\Header Files">
      <UniqueIdentifier>{6650e8ad-1582-41c9-90c8-6a851470fa0d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Util\Source Files">
      <UniqueIdentifier>{70f78e78-2f5f-4742-800c-d5d136a8dcad}</UniqueIdentifier>
    </Filter>
    <Filter Include="Timer">
      <UniqueIdentifier>{fb3c1b05-5c95-4d91-b356-479ce79eca20}</UniqueIdentifier>
    </Filter>
    <Filter Include="Timer\Header Files">
      <UniqueIdentifier>{92916904-274a-4459-b7b8-d17b27dcbfd1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Timer\Source Files">
      <UniqueIdentifier>{85c6b2a6-cee8-48c3-bea1-c212f96e1935}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\Poco\Util\Application.h">
      <Filter>Application\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\LoggingSubsystem.h">
      <Filter>Application\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\ServerApplication.h">
      <Filter>Application\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\Subsystem.h">
      <Filter>Application\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\AbstractConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\ConfigurationMapper.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\ConfigurationView.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\FilesystemConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\IniFileConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\JSONConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\LayeredConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\LocalConfigurationView.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\LoggingConfigurator.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\MapConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\PropertyFileConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\SystemConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\XMLConfiguration.h">
      <Filter>Configuration\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\HelpFormatter.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\IntValidator.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\Option.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\OptionCallback.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\OptionException.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\OptionProcessor.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\OptionSet.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\RegExpValidator.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\Validator.h">
      <Filter>Options\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\WinRegistryConfiguration.h">
      <Filter>Windows\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\WinRegistryKey.h">
      <Filter>Windows\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\WinService.h">
      <Filter>Windows\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\Util.h">
      <Filter>Util\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\Timer.h">
      <Filter>Timer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\TimerTask.h">
      <Filter>Timer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\Poco\Util\TimerTaskAdapter.h">
      <Filter>Timer\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\Application.cpp">
      <Filter>Application\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LoggingSubsystem.cpp">
      <Filter>Application\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ServerApplication.cpp">
      <Filter>Application\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Subsystem.cpp">
      <Filter>Application\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\AbstractConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ConfigurationMapper.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ConfigurationView.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FilesystemConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\IniFileConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\JSONConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LayeredConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LocalConfigurationView.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\LoggingConfigurator.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MapConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PropertyFileConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SystemConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\XMLConfiguration.cpp">
      <Filter>Configuration\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HelpFormatter.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\IntValidator.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Option.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OptionCallback.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OptionException.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OptionProcessor.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OptionSet.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RegExpValidator.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Validator.cpp">
      <Filter>Options\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WinRegistryConfiguration.cpp">
      <Filter>Windows\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WinRegistryKey.cpp">
      <Filter>Windows\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WinService.cpp">
      <Filter>Windows\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Timer.cpp">
      <Filter>Timer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TimerTask.cpp">
      <Filter>Timer\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\DLLVersion.rc" />
  </ItemGroup>
</Project>