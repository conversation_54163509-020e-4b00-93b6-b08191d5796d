#! /bin/bash
#
# mkrelease
#
# Create a release for distribution.
#
# usage: mkrelease [-i] [-f specfile] <version> [<component>...]
#

if [ "$1" = "" ] ; then
	echo "usage: $0 [-i] <version> [<component>...]"
	echo "       -i          - include internal tools"
	echo "       -f specfile - read component list from specfile"
	echo "       -o dir      - specify output directory"
	echo "       -c location - specify line-ending conversion utility (enables conversion)"
	echo "       -l          - include licensing"
	exit 1
fi

comps="Foundation Encodings XML JSON Util Net"
internal=0
specfile=""
version=""
output=""
lineEndConv=""
licensingDep=""

while [ "$1" != "" ] ;
do
	if [ "$1" = "-i" ] ; then
		shift
		internal=1
	elif [ "$1" = "-f" ] ; then
		shift
		specfile=$1
		shift
	elif [ "$1" = "-o" ] ; then
		shift
		output=$1
		shift
	elif [ "$1" = "-c" ] ; then
		shift
		lineEndConv=$1
		shift
	elif [ "$1" = "-l" ] ; then
		shift
		licensingDep="Licensing-libexec"
		comps="$comps Licensing"
	elif [ "$version" = "" ] ; then
		version=$1
		shift
	else
		comps="$comps $1"
		shift
	fi
done

if [ "$specfile" != "" ] ; then
	while read c
	do
		comps="$comps $c"
	done <$specfile
fi

if [ "$version" = "" ] ; then
	echo "Error: no version specified."
	exit 1
fi

if [ "$output" != "" ] ; then
	target=$output
else
	target=$POCO_BASE/releases/poco-${version}
fi

mkdir -p ${target}
mkdir -p ${target}/doc
mkdir -p ${target}/contrib
mkdir -p ${target}/patches
mkdir -p ${target}/cmake


#
# readme files, etc.
#
echo ${version} "(`date +%Y-%m-%d`)" >${target}/VERSION
cp ${POCO_BASE}/LICENSE ${target}
cp ${POCO_BASE}/README ${target}
cp ${POCO_BASE}/CHANGELOG ${target}
cp ${POCO_BASE}/CONTRIBUTORS ${target}
cp ${POCO_BASE}/DLLVersion.rc ${target}

cp ${POCO_BASE}/doc/Acknowledgements.html ${target}/doc
cp ${POCO_BASE}/doc/*.page ${target}/doc

cp -R ${POCO_BASE}/contrib/* ${target}/contrib
cp -R ${POCO_BASE}/patches/* ${target}/patches

cp -R ${POCO_BASE}/cmake/* ${target}/cmake
cp ${POCO_BASE}/CMakeLists.txt ${target}


#
# build system
#
mkdir -p ${target}/build/config
mkdir -p ${target}/build/rules
mkdir -p ${target}/build/script

cp ${POCO_BASE}/build/config/* ${target}/build/config
cp ${POCO_BASE}/build/rules/* ${target}/build/rules
cp ${POCO_BASE}/build/script/* ${target}/build/script
cp ${POCO_BASE}/buildwin.cmd ${target}
cp ${POCO_BASE}/buildwin.ps1 ${target}
cp ${POCO_BASE}/configure ${target}
cp ${POCO_BASE}/libversion ${target}

if [ $internal = 1 ] ; then
	cp ${POCO_BASE}/build/script/testall ${target}/build/script
	cp ${POCO_BASE}/build/script/waitfortest ${target}/build/script
fi


#
# CppUnit
#
mkdir -p ${target}/CppUnit
mkdir -p ${target}/CppUnit/doc
mkdir -p ${target}/CppUnit/include/CppUnit
mkdir -p ${target}/CppUnit/src

cp ${POCO_BASE}/CppUnit/doc/* ${target}/CppUnit/doc >/dev/null 2>&1
cp ${POCO_BASE}/CppUnit/include/CppUnit/* ${target}/CppUnit/include/CppUnit >/dev/null 2>&1
cp ${POCO_BASE}/CppUnit/src/* ${target}/CppUnit/src >/dev/null 2>&1
cp ${POCO_BASE}/CppUnit/*.sln ${target}/CppUnit >/dev/null 2>&1
cp ${POCO_BASE}/CppUnit/*.vcxproj ${target}/CppUnit >/dev/null 2>&1
cp ${POCO_BASE}/CppUnit/*.vcxproj.filters ${target}/CppUnit >/dev/null 2>&1
cp ${POCO_BASE}/CppUnit/Makefile ${target}/CppUnit >/dev/null 2>&1
cp ${POCO_BASE}/CppUnit/*.progen ${target}/CppUnit >/dev/null 2>&1
cp ${POCO_BASE}/CppUnit/CMakeLists.txt ${target}/CppUnit >/dev/null 2>&1

#
# Copy components
#
for comp in $comps ;
do
	cpproj ${POCO_BASE}/$comp ${target}/$comp
done


#
# Create components file
#
echo "CppUnit" >${target}/components
for comp in $comps ;
do
	echo $comp >>${target}/components
done


#
# Make all files writeable
#
chmod -R +w ${target}


#
# Remove VS90 and progen
#
find ${target} -name '*.progen' -exec rm {} \;
find ${target} -iname '*_vs90.sln' -exec rm {} \;


#
# Generate Makefile
#
cat >${target}/Makefile <<'ENDOFSCRIPT'
#
# Makefile
#
# The global Makefile for POCO [generated by mkrelease]
#

sinclude config.make
sinclude config.build

ifndef POCO_BASE
$(warning WARNING: POCO_BASE is not defined. Assuming current directory.)
export POCO_BASE=$(shell pwd)
endif
ifdef POCO_VERBOSE
$(info POCO_BASE           = $(POCO_BASE))
endif

ifndef POCO_BUILD
export POCO_BUILD=$(POCO_BASE)
endif
ifdef POCO_VERBOSE
$(info POCO_BUILD          = $(POCO_BUILD))
endif

#
# Determine OS
#
POCO_HOST_OSNAME = $(shell uname)
ifeq ($(findstring CYGWIN,$(POCO_HOST_OSNAME)),CYGWIN)
POCO_HOST_OSNAME = CYGWIN
endif

ifeq ($(findstring MINGW,$(POCO_HOST_OSNAME)),MINGW)
POCO_HOST_OSNAME = MinGW
endif
ifdef POCO_VERBOSE
$(info POCO_HOST_OSNAME    = $(POCO_HOST_OSNAME))
endif

POCO_HOST_OSARCH ?= $(subst /,-,$(shell uname -m | tr ' ' _))
ifdef POCO_VERBOSE
$(info POCO_HOST_OSARCH    = $(POCO_HOST_OSARCH))
endif

#
# If POCO_CONFIG is not set, use the OS name as configuration name
#
ifndef POCO_CONFIG
POCO_CONFIG = $(POCO_HOST_OSNAME)
endif
ifdef POCO_VERBOSE
$(info POCO_CONFIG         = $(POCO_CONFIG))
endif

#
# Include System Specific Settings
#
include $(POCO_BASE)/build/config/$(POCO_CONFIG)

#
# Determine operating system
#
ifndef POCO_TARGET_OSNAME
OSNAME   := $(POCO_HOST_OSNAME)
else
OSNAME   := $(POCO_TARGET_OSNAME)
endif
ifdef POCO_VERBOSE
$(info OSNAME              = $(OSNAME))
endif

ifndef POCO_TARGET_OSARCH
OSARCH   := $(POCO_HOST_OSARCH)
else
OSARCH   := $(POCO_TARGET_OSARCH)
endif
ifdef POCO_VERBOSE
$(info OSARCH              = $(OSARCH))
endif

.PHONY: poco all libexecs cppunit tests samples cleans clean distclean install

# TESTS and SAMPLES are set in config.make
poco: libexecs $(if $(TESTS),tests) $(if $(SAMPLES),samples)
all: libexecs tests samples

INSTALLDIR = $(DESTDIR)$(POCO_PREFIX)
ENDOFSCRIPT

echo "COMPONENTS = $comps" >>${target}/Makefile

cat >>${target}/Makefile <<'ENDOFSCRIPT'

cppunit:
	$(MAKE) -C $(POCO_BASE)/CppUnit

CppUnit-clean:
	$(MAKE) -C $(POCO_BASE)/CppUnit clean

install: libexecs
	mkdir -p $(INSTALLDIR)/include/Poco
	mkdir -p $(INSTALLDIR)/lib
	mkdir -p $(INSTALLDIR)/bin
	for comp in $(filter-out $(foreach f,$(OMIT),$f%),$(COMPONENTS)) ; do \
		if [ -d "$(POCO_BASE)/$$comp/include" ] ; then \
			cp -Rf $(POCO_BASE)/$$comp/include/* $(INSTALLDIR)/include/ ; \
		fi ; \
		if [ -d "$(POCO_BUILD)/$$comp/bin" ] ; then \
			find $(POCO_BUILD)/$$comp/bin -perm -700 -type f -exec cp -f {} $(INSTALLDIR)/bin \; ; \
		fi ; \
	done
ifeq ($(OSNAME), CYGWIN)
	find $(POCO_BUILD)/lib/$(OSNAME)/$(OSARCH) -name "cygPoco*" -type f -exec cp -f  {} $(INSTALLDIR)/bin \;
	find $(POCO_BUILD)/lib/$(OSNAME)/$(OSARCH) -name "cygPoco*" -type l -exec cp -Rf {} $(INSTALLDIR)/bin \;
endif
	find $(POCO_BUILD)/lib/$(OSNAME)/$(OSARCH) -name "libPoco*" -type f -exec cp -f  {} $(INSTALLDIR)/lib \;
	find $(POCO_BUILD)/lib/$(OSNAME)/$(OSARCH) -name "libPoco*" -type l -exec cp -Rf {} $(INSTALLDIR)/lib \;

ENDOFSCRIPT

libexecs=""
tests=""
samples=""
cleans=""
for comp in $comps ;
do
	libexecs="$libexecs ${comp}-libexec"
	cleans="$cleans ${comp}-clean"
	if [ -d "${POCO_BASE}/${comp}/testsuite" ] ; then
		tests="$tests ${comp}-tests"
	fi
	if [ -d "${POCO_BASE}/${comp}/samples" ] ; then
		samples="$samples ${comp}-samples"
	fi
done

echo "libexecs = $libexecs" >>${target}/Makefile
echo "tests    = $tests" >> ${target}/Makefile
echo "samples  = $samples" >> ${target}/Makefile
echo "cleans   = $cleans" >> ${target}/Makefile
echo "" >>${target}/Makefile
echo '.PHONY: $(libexecs)' >>${target}/Makefile
echo '.PHONY: $(tests)' >>${target}/Makefile
echo '.PHONY: $(samples)' >>${target}/Makefile
echo '.PHONY: $(cleans)' >>${target}/Makefile
echo "" >>${target}/Makefile
echo 'libexecs: $(filter-out $(foreach f,$(OMIT),$f%),$(libexecs))' >>${target}/Makefile
echo 'tests: $(filter-out $(foreach f,$(OMIT),$f%),$(tests))' >>${target}/Makefile
echo 'samples: $(filter-out $(foreach f,$(OMIT),$f%),$(samples))' >>${target}/Makefile
echo 'cleans: $(filter-out $(foreach f,$(OMIT),$f%),$(cleans))' >>${target}/Makefile

for comp in $comps ;
do
	if [ "`grep -c POCO_LICENSING "${POCO_BASE}/${comp}/Makefile"`" != 0 ] ; then
		dependencies=$licensingDep
	else
		dependencies=""
	fi
	if [ -f "${POCO_BASE}/${comp}/dependencies" ] ; then
		for dep in `cat "${POCO_BASE}/${comp}/dependencies"` ;
		do
			# get rid of surrounding whitespace (trailing \r on Cygwin)
			read dep <<< "$dep"
			dependencies="$dependencies ${dep}-libexec"
		done
	fi
	cat >>${target}/Makefile <<ENDOFSCRIPT

${comp}-libexec: $dependencies
	\$(MAKE) -C \$(POCO_BASE)/${comp}
ENDOFSCRIPT

	if [ -d "${POCO_BASE}/${comp}/testsuite" ] ; then
		tdependencies=""
		if [ -f "${POCO_BASE}/${comp}/testsuite/dependencies" ] ; then
			for dep in `cat "${POCO_BASE}/${comp}/testsuite/dependencies"` ;
			do
				# get rid of surrounding whitespace (trailing \r on Cygwin)
				read dep <<< ${dep}
				tdependencies="$tdependencies ${dep}-libexec"
			done
		fi
		cat >>${target}/Makefile <<ENDOFSCRIPT

${comp}-tests: ${comp}-libexec $tdependencies cppunit
	\$(MAKE) -C \$(POCO_BASE)/${comp}/testsuite
ENDOFSCRIPT
	fi

	if [ -d "${POCO_BASE}/${comp}/samples" ] ; then
		sdependencies=""
		if [ -f "${POCO_BASE}/${comp}/samples/dependencies" ] ; then
			for dep in `cat "${POCO_BASE}/${comp}/samples/dependencies"` ;
			do
				# get rid of surrounding whitespace (trailing \r on Cygwin)
				read dep <<< ${dep}
				sdependencies="$sdependencies ${dep}-libexec"
			done
		fi
		cat >>${target}/Makefile <<ENDOFSCRIPT

${comp}-samples: ${comp}-libexec $sdependencies
	\$(MAKE) -C \$(POCO_BASE)/${comp}/samples
ENDOFSCRIPT
	fi

	cat >>${target}/Makefile <<ENDOFSCRIPT

${comp}-clean:
	\$(MAKE) -C \$(POCO_BASE)/${comp} clean
ENDOFSCRIPT

	if [ -f "${POCO_BASE}/${comp}/testsuite/Makefile" ] ; then
		cat >>${target}/Makefile <<ENDOFSCRIPT
	\$(MAKE) -C \$(POCO_BASE)/${comp}/testsuite clean
ENDOFSCRIPT
	fi

	if [ -f "${POCO_BASE}/${comp}/samples/Makefile" ] ; then
		cat >>${target}/Makefile <<ENDOFSCRIPT
	\$(MAKE) -C \$(POCO_BASE)/${comp}/samples clean
ENDOFSCRIPT
	fi
done

cat >>${target}/Makefile <<ENDOFSCRIPT

clean: cleans CppUnit-clean

distclean:
	rm -rf \$(POCO_BUILD)/lib
	find \$(POCO_BUILD) -name obj -type d -print0 | xargs -0 rm -rf
	find \$(POCO_BUILD) -name .dep -type d -print0 | xargs -0 rm -rf
	find \$(POCO_BUILD) -name bin -type d -print0 | xargs -0 rm -rf
ENDOFSCRIPT


#
# Create Visual Studio 16 build script
#
cat >${target}/build_vs160.cmd <<'ENDOFSCRIPT'
@echo off
buildwin 160 build shared both Win32 samples
ENDOFSCRIPT


#
# Create Visual Studio 17 build script
#
cat >${target}/build_vs170.cmd <<'ENDOFSCRIPT'
@echo off
buildwin 170 build shared both Win32 samples
ENDOFSCRIPT


#
# Fix line endings
#
if [ "$lineEndConv" != "" ] ; then
	$lineEndConv ${target}/build_vs160.cmd
	$lineEndConv ${target}/build_vs170.cmd
	$lineEndConv ${target}/Makefile
	$lineEndConv ${target}/components
fi


#
# Create .tar and .zip archives
#
if [ "$output" = "" ] ; then
	cd releases
	find ${target}/ -print | sed "s:^${target}/*::" >${target}/MANIFEST

	tar cf poco-${version}.tar poco-${version}
	gzip poco-${version}.tar

	tar cf poco-${version}.tar poco-${version}
	bzip2 poco-${version}.tar

	if [ -x /usr/bin/zip ] ; then
		/usr/bin/zip -r -q poco-${version}.zip poco-${version}
	fi
fi

exit 0
