<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="NetCore">
      <UniqueIdentifier>{ccf69a11-3b4f-4fb5-93ca-a66f44149c48}</UniqueIdentifier>
    </Filter>
    <Filter Include="NetCore\Header Files">
      <UniqueIdentifier>{94b25e15-93e7-47f2-a81d-1c5d88c8837a}</UniqueIdentifier>
    </Filter>
    <Filter Include="NetCore\Source Files">
      <UniqueIdentifier>{f8fbf2e7-5cf3-41b1-91be-0eb2f4cf1267}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite">
      <UniqueIdentifier>{d633334b-99ff-4603-8805-305650f37f2d}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Header Files">
      <UniqueIdentifier>{64c0b7c5-c20f-4113-b4b6-b3aafd25ef11}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Suite\Source Files">
      <UniqueIdentifier>{0a7d0f20-26dc-465b-af0a-9f5256916311}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver">
      <UniqueIdentifier>{fccf4b01-a418-4f01-9dab-d1deec184fcd}</UniqueIdentifier>
    </Filter>
    <Filter Include="_Driver\Source Files">
      <UniqueIdentifier>{d0850f33-f071-4a51-b503-e5fe0ee47926}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sockets">
      <UniqueIdentifier>{d54dcd42-362b-49cf-b4ae-46c62407d267}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sockets\Header Files">
      <UniqueIdentifier>{1cab5781-e109-441f-bb4d-bab057e06722}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sockets\Source Files">
      <UniqueIdentifier>{418fe205-dbef-48a1-aa84-e6e9da687ed0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Messages">
      <UniqueIdentifier>{34c332c1-6d51-42f9-ae1e-8d325338dd2d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Messages\Header Files">
      <UniqueIdentifier>{c66c3436-01fc-49a8-a7e0-35fc9ef384b5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Messages\Source Files">
      <UniqueIdentifier>{1b196df7-014f-4959-95dc-98cbd480f524}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTP">
      <UniqueIdentifier>{cccfbc2c-95f9-490b-a467-5587dc831ec9}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTP\Header Files">
      <UniqueIdentifier>{bfb45726-49d6-4ffe-8d10-5a821c7e87d2}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTP\Source Files">
      <UniqueIdentifier>{db5347a7-c1f8-4acf-ac7c-36e8ea44c8a2}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer">
      <UniqueIdentifier>{01d68d57-6a59-47fc-a0ea-8b80648c445d}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Header Files">
      <UniqueIdentifier>{9aa28a76-6929-460e-941d-ae529d287419}</UniqueIdentifier>
    </Filter>
    <Filter Include="TCPServer\Source Files">
      <UniqueIdentifier>{8f464237-7ceb-4059-b9c7-cb28e0befa39}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPServer">
      <UniqueIdentifier>{4fe17b2e-c3de-48d5-ba1e-330388a57569}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPServer\Header Files">
      <UniqueIdentifier>{4e3ff9ce-a727-4dec-922f-44938e70fbaf}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPServer\Source Files">
      <UniqueIdentifier>{eec99316-a951-4b33-bab6-1daacc54a419}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTML">
      <UniqueIdentifier>{b4a5eb31-a286-4119-adf1-7c03c1ceb562}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTML\Header Files">
      <UniqueIdentifier>{f7b34631-a0d8-4f4d-96eb-ba083e834c0e}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTML\Source Files">
      <UniqueIdentifier>{0e6ff99b-5eab-483f-b3b3-f8c0021d1892}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPClient">
      <UniqueIdentifier>{adc74447-114b-405e-80d1-2fb275145931}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPClient\Header Files">
      <UniqueIdentifier>{72291cca-671e-41e0-8d6a-7f5d8322f90e}</UniqueIdentifier>
    </Filter>
    <Filter Include="HTTPClient\Source Files">
      <UniqueIdentifier>{4ecf1471-9d95-4333-ba23-809ad18db7f7}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPClient">
      <UniqueIdentifier>{5bd15a5d-8440-48db-a07c-4a880560c9c4}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPClient\Header Files">
      <UniqueIdentifier>{bce40ba2-ee14-46a0-b3d8-83ad00655814}</UniqueIdentifier>
    </Filter>
    <Filter Include="FTPClient\Source Files">
      <UniqueIdentifier>{56b970a9-e0be-499b-9054-4d2eb9ad4c6b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Reactor">
      <UniqueIdentifier>{01e47e93-fa20-4e6e-86bc-b9e57045a9cc}</UniqueIdentifier>
    </Filter>
    <Filter Include="Reactor\Header Files">
      <UniqueIdentifier>{fd22ec4d-0628-4f38-8f4a-c199f9905eff}</UniqueIdentifier>
    </Filter>
    <Filter Include="Reactor\Source Files">
      <UniqueIdentifier>{5f25c787-c9bd-45aa-bb40-b78f2e05fd2d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail">
      <UniqueIdentifier>{fcdc47be-4fed-44fe-bdff-d2761a228284}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Header Files">
      <UniqueIdentifier>{8a707c12-bc94-41c6-a07b-d4ecef8033ab}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mail\Source Files">
      <UniqueIdentifier>{cc8e67a7-772f-45c9-8d34-b8bb058a5d15}</UniqueIdentifier>
    </Filter>
    <Filter Include="ICMP">
      <UniqueIdentifier>{255833d8-babc-4044-b69d-84c280ca4823}</UniqueIdentifier>
    </Filter>
    <Filter Include="ICMP\Header Files">
      <UniqueIdentifier>{d5ef0835-a8da-4463-9f1d-d305e69dedbc}</UniqueIdentifier>
    </Filter>
    <Filter Include="ICMP\Source Files">
      <UniqueIdentifier>{72aa1633-e42e-4cbe-bb4d-18f93bca2eb9}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTP">
      <UniqueIdentifier>{85932f39-3ee6-445e-af3e-6dbf8b8549e3}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTP\Header Files">
      <UniqueIdentifier>{cecc3afb-0c17-4c55-a9cb-4ae0ddb28f27}</UniqueIdentifier>
    </Filter>
    <Filter Include="NTP\Source Files">
      <UniqueIdentifier>{8c1d1b02-45b1-4634-8057-17e78215c0bb}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging">
      <UniqueIdentifier>{a7506abb-b3ca-4a72-9ba6-0be2e9b7d4d0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Header Files">
      <UniqueIdentifier>{6c9e9fed-86ec-4422-9741-cb6f7e1b3463}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging\Source Files">
      <UniqueIdentifier>{148417de-d0ca-4c96-9ecd-01ff6ef96f13}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket">
      <UniqueIdentifier>{08d99a1b-f8e5-4e95-8ef2-052177a2bdf0}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Header Files">
      <UniqueIdentifier>{8722b61e-7930-4e5f-a1ca-e0bfec9e0d25}</UniqueIdentifier>
    </Filter>
    <Filter Include="WebSocket\Source Files">
      <UniqueIdentifier>{40f6264f-2a50-485a-b34a-dd4a61d96125}</UniqueIdentifier>
    </Filter>
    <Filter Include="OAuth">
      <UniqueIdentifier>{6afe5e17-85d4-480b-9a65-2f771c1148a3}</UniqueIdentifier>
    </Filter>
    <Filter Include="OAuth\Header Files">
      <UniqueIdentifier>{e0e410de-583f-4574-aab8-9d4f2c5664d9}</UniqueIdentifier>
    </Filter>
    <Filter Include="OAuth\Source Files">
      <UniqueIdentifier>{b9ad3872-d4ac-46de-b538-ba7258bfb6ec}</UniqueIdentifier>
    </Filter>
    <Filter Include="UDP">
      <UniqueIdentifier>{cf6d2861-dfc4-4cf6-929d-0f3790206d3c}</UniqueIdentifier>
    </Filter>
    <Filter Include="UDP\Source Files">
      <UniqueIdentifier>{cf26df73-83d3-408e-a77e-f1118009f0b1}</UniqueIdentifier>
    </Filter>
    <Filter Include="UDP\Header Files">
      <UniqueIdentifier>{2b3a8d7d-8917-4c61-8583-ca1f8adda21e}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\DNSTest.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\IPAddressTest.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NetCoreTestSuite.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NetworkInterfaceTest.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SocketAddressTest.h">
      <Filter>NetCore\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NetTestSuite.h">
      <Filter>_Suite\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DatagramSocketTest.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DialogServer.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\DialogSocketTest.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\EchoServer.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\MulticastEchoServer.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\MulticastSocketTest.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\PollSetTest.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\RawSocketTest.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SocketsTestSuite.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SocketStreamTest.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SocketTest.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UDPEchoServer.h">
      <Filter>Sockets\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\MediaTypeTest.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\MessageHeaderTest.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\MessagesTestSuite.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\MultipartReaderTest.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\MultipartWriterTest.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NameValueCollectionTest.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\QuotedPrintableTest.h">
      <Filter>Messages\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPCookieTest.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPCredentialsTest.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPRequestTest.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPResponseTest.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPTestServer.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPTestSuite.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NTLMCredentialsTest.h">
      <Filter>HTTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TCPServerTest.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\TCPServerTestSuite.h">
      <Filter>TCPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPServerTest.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPServerTestSuite.h">
      <Filter>HTTPServer\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTMLFormTest.h">
      <Filter>HTML\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTMLTestSuite.h">
      <Filter>HTML\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPClientSessionTest.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPClientTestSuite.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\HTTPStreamFactoryTest.h">
      <Filter>HTTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FTPClientSessionTest.h">
      <Filter>FTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FTPClientTestSuite.h">
      <Filter>FTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\FTPStreamFactoryTest.h">
      <Filter>FTPClient\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ReactorTestSuite.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SocketConnectorTest.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SocketReactorTest.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SocketProactorTest.h">
      <Filter>Reactor\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\MailMessageTest.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\MailStreamTest.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\MailTestSuite.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\POP3ClientSessionTest.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SMTPClientSessionTest.h">
      <Filter>Mail\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ICMPClientTest.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ICMPClientTestSuite.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ICMPSocketTest.h">
      <Filter>ICMP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NTPClientTest.h">
      <Filter>NTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\NTPClientTestSuite.h">
      <Filter>NTP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\SyslogTest.h">
      <Filter>Logging\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\WebSocketTest.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\WebSocketTestSuite.h">
      <Filter>WebSocket\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\OAuth10CredentialsTest.h">
      <Filter>OAuth\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\OAuth20CredentialsTest.h">
      <Filter>OAuth\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\OAuthTestSuite.h">
      <Filter>OAuth\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UDPServerTest.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\UDPServerTestSuite.h">
      <Filter>UDP\Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\DNSTest.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\IPAddressTest.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NetCoreTestSuite.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NetworkInterfaceTest.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketAddressTest.cpp">
      <Filter>NetCore\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NetTestSuite.cpp">
      <Filter>_Suite\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\Driver.cpp">
      <Filter>_Driver\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DatagramSocketTest.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DialogServer.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\DialogSocketTest.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\EchoServer.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MulticastEchoServer.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MulticastSocketTest.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\PollSetTest.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\RawSocketTest.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketsTestSuite.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketStreamTest.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketTest.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UDPEchoServer.cpp">
      <Filter>Sockets\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MediaTypeTest.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MessageHeaderTest.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MessagesTestSuite.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MultipartReaderTest.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MultipartWriterTest.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NameValueCollectionTest.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\QuotedPrintableTest.cpp">
      <Filter>Messages\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPCookieTest.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPCredentialsTest.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPRequestTest.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPResponseTest.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPTestServer.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPTestSuite.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NTLMCredentialsTest.cpp">
      <Filter>HTTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerTest.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\TCPServerTestSuite.cpp">
      <Filter>TCPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerTest.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPServerTestSuite.cpp">
      <Filter>HTTPServer\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTMLFormTest.cpp">
      <Filter>HTML\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTMLTestSuite.cpp">
      <Filter>HTML\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPClientSessionTest.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPClientTestSuite.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\HTTPStreamFactoryTest.cpp">
      <Filter>HTTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FTPClientSessionTest.cpp">
      <Filter>FTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FTPClientTestSuite.cpp">
      <Filter>FTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\FTPStreamFactoryTest.cpp">
      <Filter>FTPClient\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ReactorTestSuite.cpp">
      <Filter>Reactor\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketConnectorTest.cpp">
      <Filter>Reactor\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketReactorTest.cpp">
      <Filter>Reactor\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SocketProactorTest.cpp">
      <Filter>Reactor\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MailMessageTest.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MailStreamTest.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\MailTestSuite.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\POP3ClientSessionTest.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SMTPClientSessionTest.cpp">
      <Filter>Mail\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPClientTest.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPClientTestSuite.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ICMPSocketTest.cpp">
      <Filter>ICMP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NTPClientTest.cpp">
      <Filter>NTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\NTPClientTestSuite.cpp">
      <Filter>NTP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\SyslogTest.cpp">
      <Filter>Logging\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WebSocketTest.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\WebSocketTestSuite.cpp">
      <Filter>WebSocket\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OAuth10CredentialsTest.cpp">
      <Filter>OAuth\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OAuth20CredentialsTest.cpp">
      <Filter>OAuth\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\OAuthTestSuite.cpp">
      <Filter>OAuth\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UDPServerTest.cpp">
      <Filter>UDP\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\UDPServerTestSuite.cpp">
      <Filter>UDP\Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>