<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="Encodings"
	ProjectGUID="{D7AAB91A-9AB8-457D-A329-02D1FA47CB7E}"
	RootNamespace="Encodings"
	Keyword="Win32Proj"
	TargetFrameworkVersion="0"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="debug_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_USRDLL;Encodings_EXPORTS"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies=""
				OutputFile="..\bin\PocoEncodingsd.dll"
				LinkIncremental="2"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\lib"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="..\bin\PocoEncodingsd.pdb"
				SubSystem="1"
				ImportLibrary="..\lib\PocoEncodingsd.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_USRDLL;Encodings_EXPORTS"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions=""
				AdditionalDependencies=""
				OutputFile="..\bin\PocoEncodings.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				ImportLibrary="..\lib\PocoEncodings.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\lib\PocoEncodingsMTd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				AdditionalDependencies=""
				OutputFile="..\lib\PocoEncodingsMTd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="0"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoEncodingsMT.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="debug_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				ProgramDataBaseFileName="..\lib\PocoEncodingsMDd.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoEncodingsMDd.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="release_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions=""
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\Foundation\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\PocoEncodingsMD.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Encodings"
			>
			<Filter
				Name="Header Files"
				>
				<File
					RelativePath=".\include\Poco\DoubleByteEncoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Encodings.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\ISO8859_10Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\ISO8859_11Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\ISO8859_13Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\ISO8859_14Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\ISO8859_16Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\ISO8859_3Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\ISO8859_4Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\ISO8859_5Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\ISO8859_6Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\ISO8859_7Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\ISO8859_8Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\ISO8859_9Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\MacCentralEurRomanEncoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\MacChineseSimpEncoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\MacChineseTradEncoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\MacCyrillicEncoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\MacJapaneseEncoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\MacKoreanEncoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\MacRomanEncoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Windows1253Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Windows1254Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Windows1255Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Windows1256Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Windows1257Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Windows1258Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Windows874Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Windows932Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Windows936Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Windows949Encoding.h"
					>
				</File>
				<File
					RelativePath=".\include\Poco\Windows950Encoding.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Source Files"
				>
				<File
					RelativePath=".\src\DoubleByteEncoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Encodings.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ISO8859_10Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ISO8859_11Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ISO8859_13Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ISO8859_14Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ISO8859_16Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ISO8859_3Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ISO8859_4Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ISO8859_5Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ISO8859_6Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ISO8859_7Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ISO8859_8Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\ISO8859_9Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MacCentralEurRomanEncoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MacChineseSimpEncoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MacChineseTradEncoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MacCyrillicEncoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MacJapaneseEncoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MacKoreanEncoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\MacRomanEncoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Windows1253Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Windows1254Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Windows1255Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Windows1256Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Windows1257Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Windows1258Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Windows874Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Windows932Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Windows936Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Windows949Encoding.cpp"
					>
				</File>
				<File
					RelativePath=".\src\Windows950Encoding.cpp"
					>
				</File>
			</Filter>
		</Filter>
		<File
			RelativePath="..\DLLVersion.rc"
			>
			<FileConfiguration
				Name="debug_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_mt|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="debug_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="release_static_md|Win32"
				ExcludedFromBuild="true"
				>
				<Tool
					Name="VCResourceCompilerTool"
				/>
			</FileConfiguration>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
