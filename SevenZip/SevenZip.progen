vc.project.guid = BBDBC9A3-15CD-495B-9B16-D03CFBFB8D2D
vc.project.name = SevenZip
vc.project.target = Poco${vc.project.name}
vc.project.type = library
vc.project.pocobase = ..
vc.project.outdir = ${vc.project.pocobase}
vc.project.platforms = Win32
vc.project.configurations = debug_shared, release_shared, debug_static_mt, release_static_mt, debug_static_md, release_static_md
vc.project.prototype = ${vc.project.name}_vs90.vcproj
vc.project.compiler.include = ..\\Foundation\\include
vc.project.compiler.defines =
vc.project.compiler.defines.shared = ${vc.project.name}_EXPORTS
vc.project.compiler.defines.debug_shared = ${vc.project.compiler.defines.shared}
vc.project.compiler.defines.release_shared = ${vc.project.compiler.defines.shared}
vc.project.compiler.disableWarnings = 4244;4267
vc.project.compiler.additionalOptions = /Zc:__cplusplus
vc.solution.create = true
