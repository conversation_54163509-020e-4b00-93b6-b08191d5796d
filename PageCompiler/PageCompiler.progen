vc.project.guid = E12E5C71-79A4-495A-848F-F1710111E610
vc.project.name = PageCompiler
vc.project.target = cpspc
vc.project.type = executable
vc.project.pocobase = ..
vc.project.outdir = ${vc.project.pocobase}
vc.project.platforms = Win32
vc.project.configurations = debug_shared, release_shared, debug_static_mt, release_static_mt, debug_static_md, release_static_md
vc.project.prototype = PageCompiler_vs90.vcproj
vc.project.compiler.include = ..\\Foundation\\include;..\\XML\\include;..\\Util\\include;..\\Net\\include
vc.project.compiler.defines =
vc.project.compiler.defines.shared =
vc.project.compiler.defines.debug_shared = ${vc.project.compiler.defines.shared}
vc.project.compiler.defines.release_shared = ${vc.project.compiler.defines.shared}
vc.project.compiler.additionalOptions = /Zc:__cplusplus
vc.project.linker.dependencies = ws2_32.lib iphlpapi.lib
vc.solution.create = true
